# rs_czsc

使用 rust 优化 czsc 库的计算性能，以更高的效率实践缠中说禅思维方式。

> [缠中说禅博客导读](https://s0cqcxuy3p.feishu.cn/wiki/GisDwfvt1i6Ol1ko55IcLrzenYg?fromScene=spaceOverview)


### 代码规范

- 代码风格遵循 [Rust 官方风格指南](https://rustwiki.org/zh-CN/style-guide/index.html)
- 代码格式化工具：`rustfmt`，使用 `cargo fmt` 进行格式化
- 代码检查工具：`clippy`，使用 `cargo clippy` 进行检查
- 代码注释：中文注释，使用 `///` 进行注释

本地打包 wheel：
```shell
cd python
maturin build --release
# wheel包在./target/wheels/
```