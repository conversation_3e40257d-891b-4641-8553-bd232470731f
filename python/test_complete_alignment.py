#!/usr/bin/env python3
"""
完整的对齐测试脚本
验证 rs_czsc 与 czsc 库的完全一致性，包括每一笔数值
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import rs_czsc
    print("✓ 导入 rs_czsc 成功")
except ImportError as e:
    print(f"✗ 导入 rs_czsc 失败: {e}")
    sys.exit(1)

try:
    import czsc
    print("✓ 导入 czsc 成功")
    CZSC_AVAILABLE = True
except ImportError as e:
    print(f"✗ 导入 czsc 失败: {e}")
    print("将只测试 rs_czsc 功能")
    CZSC_AVAILABLE = False


def create_realistic_stock_data(num_days=200):
    """创建更真实的股票数据"""
    np.random.seed(42)  # 保证可重现性
    
    dates = pd.date_range(start='2024-01-01', periods=num_days, freq='D')
    
    # 创建真实的股价走势：趋势 + 噪音 + 周期性波动
    base_price = 50.0
    trend = np.linspace(0, 20, num_days)  # 长期上升趋势
    
    # 添加周期性波动
    cycle1 = 5 * np.sin(np.linspace(0, 4*np.pi, num_days))
    cycle2 = 3 * np.sin(np.linspace(0, 8*np.pi, num_days))
    
    # 添加随机游走
    noise = np.cumsum(np.random.normal(0, 1, num_days))
    
    # 合成价格序列
    close_prices = base_price + trend + cycle1 + cycle2 + noise
    
    # 生成开盘价、最高价、最低价
    opens = []
    highs = []
    lows = []
    volumes = []
    amounts = []
    
    for i, close in enumerate(close_prices):
        # 开盘价为前一日收盘价加上小幅随机变动
        if i == 0:
            open_price = close + np.random.normal(0, 0.5)
        else:
            open_price = close_prices[i-1] + np.random.normal(0, 0.3)
        
        # 最高价和最低价
        high = max(open_price, close) + abs(np.random.normal(0, 0.8))
        low = min(open_price, close) - abs(np.random.normal(0, 0.8))
        
        # 成交量和成交额
        volume = max(100000, int(np.random.normal(1000000, 300000)))
        amount = volume * (high + low + open_price + close) / 4
        
        opens.append(open_price)
        highs.append(high)
        lows.append(low)
        volumes.append(volume)
        amounts.append(amount)
    
    df = pd.DataFrame({
        'dt': dates,
        'symbol': ['000001.SZ'] * num_days,
        'open': opens,
        'close': close_prices,
        'high': highs,
        'low': lows,
        'vol': volumes,
        'amount': amounts
    })
    
    return df


def test_rs_czsc_functionality():
    """测试 rs_czsc 的功能"""
    print("\n" + "="*60)
    print("测试 rs_czsc 功能")
    print("="*60)
    
    # 创建测试数据
    df = create_realistic_stock_data(200)
    print(f"创建测试数据成功，共 {len(df)} 行")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    # 格式化为 K线数据
    bars = rs_czsc.format_standard_kline(df, freq=rs_czsc.Freq.D)
    print(f"格式化 K线数据成功，共 {len(bars)} 根 K线")
    
    # 创建 CZSC 对象
    rs_czsc_obj = rs_czsc.CZSC(bars, max_bi_num=50)
    print(f"创建 CZSC 对象成功: {rs_czsc_obj}")
    
    # 测试基本属性
    print(f"\n基本属性:")
    print(f"  符号: {rs_czsc_obj.symbol}")
    print(f"  频率: {rs_czsc_obj.freq}")
    print(f"  最大笔数量: {rs_czsc_obj.max_bi_num}")
    print(f"  笔数量: {len(rs_czsc_obj.bi_list)}")
    
    # 测试分型
    print(f"\n分型信息:")
    fx_list = rs_czsc_obj.fx_list
    print(f"  分型数量: {len(fx_list)}")
    
    if len(fx_list) > 0:
        print("  前3个分型:")
        for i, fx in enumerate(fx_list[:3]):
            print(f"    {i+1}. {fx.dt.strftime('%Y-%m-%d')} {fx.mark} {fx.fx:.2f}")
    
    # 测试笔
    print(f"\n笔信息:")
    bi_list = rs_czsc_obj.bi_list
    if len(bi_list) > 0:
        print("  前3笔详情:")
        for i, bi in enumerate(bi_list[:3]):
            print(f"    {i+1}. {bi}")
            print(f"       方向: {bi.direction}, 高点: {bi.high:.2f}, 低点: {bi.low:.2f}")
            print(f"       力度: {bi.power:.2f}, 长度: {bi.length}, SNR: {bi.SNR:.4f}")
            print(f"       涨跌幅: {bi.change:.4f}, 斜率: {bi.slope:.4f}")
    else:
        print("  没有检测到笔")
    
    return rs_czsc_obj, bars, df


def test_czsc_comparison(rs_czsc_obj, bars, df):
    """与原版 czsc 库进行对比测试"""
    if not CZSC_AVAILABLE:
        print("\n跳过 czsc 对比测试（czsc 库不可用）")
        return
        
    print("\n" + "="*60)
    print("对比测试 rs_czsc vs czsc")
    print("="*60)
    
    try:
        # 创建 czsc 对象
        czsc_bars = czsc.format_standard_kline(df, freq="日线")
        czsc_obj = czsc.CZSC(czsc_bars, max_bi_num=50)
        print(f"创建原版 czsc 对象成功: {czsc_obj}")
        
        # 对比基本信息
        print(f"\n基本信息对比:")
        print(f"  符号: rs={rs_czsc_obj.symbol}, czsc={czsc_obj.symbol} {'✓' if rs_czsc_obj.symbol == czsc_obj.symbol else '✗'}")
        print(f"  笔数量: rs={len(rs_czsc_obj.bi_list)}, czsc={len(czsc_obj.bi_list)} {'✓' if len(rs_czsc_obj.bi_list) == len(czsc_obj.bi_list) else '✗'}")
        
        # 对比分型
        rs_fx_list = rs_czsc_obj.fx_list
        czsc_fx_list = czsc_obj.fx_list
        print(f"  分型数量: rs={len(rs_fx_list)}, czsc={len(czsc_fx_list)} {'✓' if len(rs_fx_list) == len(czsc_fx_list) else '✗'}")
        
        # 逐个对比分型
        fx_match_count = 0
        fx_test_count = min(len(rs_fx_list), len(czsc_fx_list))
        print(f"\n分型详细对比（共{fx_test_count}个，显示前5个）:")
        for i in range(min(5, len(rs_fx_list), len(czsc_fx_list))):
            rs_fx = rs_fx_list[i]
            czsc_fx = czsc_fx_list[i]
            
            # 处理时区问题
            rs_dt = rs_fx.dt.replace(tzinfo=None) if rs_fx.dt.tzinfo else rs_fx.dt
            czsc_dt = czsc_fx.dt.replace(tzinfo=None) if czsc_fx.dt.tzinfo else czsc_fx.dt
            dt_match = abs((rs_dt - czsc_dt).total_seconds()) < 86400  # 1天误差内
            # 修复标记匹配比较：将中文转换为一致格式
            rs_mark = str(rs_fx.mark)  # 例如："Mark.D" 
            czsc_mark = str(czsc_fx.mark)  # 例如："底分型"
            
            # 标准化比较
            if rs_mark == "Mark.D" and czsc_mark == "底分型":
                mark_match = True
            elif rs_mark == "Mark.G" and czsc_mark == "顶分型":
                mark_match = True
            else:
                mark_match = rs_mark == czsc_mark
            fx_match = abs(rs_fx.fx - czsc_fx.fx) < 1e-6
            
            if dt_match and mark_match and fx_match:
                fx_match_count += 1
                status = "✓"
            else:
                status = "✗"
                
            print(f"  分型{i+1} {status}:")
            print(f"    时间: rs={rs_dt.strftime('%Y-%m-%d')}, czsc={czsc_dt.strftime('%Y-%m-%d')}")
            print(f"    标记: rs={rs_fx.mark}, czsc={czsc_fx.mark}")
            print(f"    值: rs={rs_fx.fx:.4f}, czsc={czsc_fx.fx:.4f}")
        
        # 逐个对比笔
        bi_match_count = 0
        if len(rs_czsc_obj.bi_list) > 0 and len(czsc_obj.bi_list) > 0:
            print(f"\n笔详细对比（前3笔）:")
            for i in range(min(3, len(rs_czsc_obj.bi_list), len(czsc_obj.bi_list))):
                rs_bi = rs_czsc_obj.bi_list[i]
                czsc_bi = czsc_obj.bi_list[i]
                
                # 对比各项指标 - 修复方向匹配
                rs_direction = str(rs_bi.direction)  # 例如："Direction.Up"
                czsc_direction = str(czsc_bi.direction)  # 例如："向上"
                
                if rs_direction == "Direction.Up" and czsc_direction == "向上":
                    direction_match = True
                elif rs_direction == "Direction.Down" and czsc_direction == "向下":
                    direction_match = True
                else:
                    direction_match = rs_direction == czsc_direction
                high_match = abs(rs_bi.high - czsc_bi.high) < 1e-6
                low_match = abs(rs_bi.low - czsc_bi.low) < 1e-6
                power_match = abs(rs_bi.power - czsc_bi.power) < 1e-6
                length_match = rs_bi.length == czsc_bi.length
                snr_match = abs(rs_bi.SNR - czsc_bi.SNR) < 1e-4
                change_match = abs(rs_bi.change - czsc_bi.change) < 1e-6
                slope_match = abs(rs_bi.slope - czsc_bi.slope) < 1e-6
                
                all_match = all([direction_match, high_match, low_match, power_match, 
                               length_match, snr_match, change_match, slope_match])
                
                if all_match:
                    bi_match_count += 1
                    status = "✓"
                else:
                    status = "✗"
                
                print(f"  笔{i+1} {status}:")
                print(f"    方向: rs={rs_bi.direction}, czsc={czsc_bi.direction} {'✓' if direction_match else '✗'}")
                print(f"    高点: rs={rs_bi.high:.4f}, czsc={czsc_bi.high:.4f} {'✓' if high_match else '✗'}")
                print(f"    低点: rs={rs_bi.low:.4f}, czsc={czsc_bi.low:.4f} {'✓' if low_match else '✗'}")
                print(f"    力度: rs={rs_bi.power:.4f}, czsc={czsc_bi.power:.4f} {'✓' if power_match else '✗'}")
                print(f"    长度: rs={rs_bi.length}, czsc={czsc_bi.length} {'✓' if length_match else '✗'}")
                print(f"    SNR: rs={rs_bi.SNR:.6f}, czsc={czsc_bi.SNR:.6f} {'✓' if snr_match else '✗'}")
                print(f"    涨跌幅: rs={rs_bi.change:.6f}, czsc={czsc_bi.change:.6f} {'✓' if change_match else '✗'}")
                print(f"    斜率: rs={rs_bi.slope:.6f}, czsc={czsc_bi.slope:.6f} {'✓' if slope_match else '✗'}")
        
        # 完整验证所有分型（不显示详情）
        print(f"\n完整分型验证:")
        fx_match_count = 0
        for i in range(fx_test_count):
            rs_fx = rs_fx_list[i]
            czsc_fx = czsc_fx_list[i]
            
            rs_dt = rs_fx.dt.replace(tzinfo=None) if rs_fx.dt.tzinfo else rs_fx.dt
            czsc_dt = czsc_fx.dt.replace(tzinfo=None) if czsc_fx.dt.tzinfo else czsc_fx.dt
            dt_match = abs((rs_dt - czsc_dt).total_seconds()) < 86400
            
            rs_mark = str(rs_fx.mark)
            czsc_mark = str(czsc_fx.mark)
            if rs_mark == "Mark.D" and czsc_mark == "底分型":
                mark_match = True
            elif rs_mark == "Mark.G" and czsc_mark == "顶分型":
                mark_match = True
            else:
                mark_match = rs_mark == czsc_mark
                
            fx_match = abs(rs_fx.fx - czsc_fx.fx) < 1e-6
            
            if dt_match and mark_match and fx_match:
                fx_match_count += 1
        
        # 完整验证所有笔
        print(f"\n完整笔验证:")
        bi_match_count = 0
        bi_test_count = min(len(rs_czsc_obj.bi_list), len(czsc_obj.bi_list))
        for i in range(bi_test_count):
            rs_bi = rs_czsc_obj.bi_list[i]
            czsc_bi = czsc_obj.bi_list[i]
            
            rs_direction = str(rs_bi.direction)
            czsc_direction = str(czsc_bi.direction)
            if rs_direction == "Direction.Up" and czsc_direction == "向上":
                direction_match = True
            elif rs_direction == "Direction.Down" and czsc_direction == "向下":
                direction_match = True
            else:
                direction_match = rs_direction == czsc_direction
                
            high_match = abs(rs_bi.high - czsc_bi.high) < 1e-6
            low_match = abs(rs_bi.low - czsc_bi.low) < 1e-6
            power_match = abs(rs_bi.power - czsc_bi.power) < 1e-6
            length_match = rs_bi.length == czsc_bi.length
            snr_match = abs(rs_bi.SNR - czsc_bi.SNR) < 1e-4
            change_match = abs(rs_bi.change - czsc_bi.change) < 1e-6
            slope_match = abs(rs_bi.slope - czsc_bi.slope) < 1e-6
            
            if all([direction_match, high_match, low_match, power_match, 
                   length_match, snr_match, change_match, slope_match]):
                bi_match_count += 1

        # 总结
        print(f"\n🎯 最终对比测试总结:")
        print(f"  📊 分型完全匹配: {fx_match_count}/{fx_test_count} ({fx_match_count/fx_test_count*100:.1f}%)")
        print(f"  📈 笔完全匹配: {bi_match_count}/{bi_test_count} ({bi_match_count/bi_test_count*100:.1f}%)")
        
        if fx_match_count == fx_test_count and bi_match_count == bi_test_count:
            print(f"  🎉 完美！所有数值计算完全一致！")
        else:
            print(f"  ⚠️  发现 {fx_test_count - fx_match_count} 个分型和 {bi_test_count - bi_match_count} 个笔不匹配")
        
    except Exception as e:
        print(f"对比测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("开始完整对齐测试...")
    print(f"Python 版本: {sys.version}")
    
    # 测试 rs_czsc 功能
    rs_czsc_obj, bars, df = test_rs_czsc_functionality()
    
    # 与原版 czsc 对比
    test_czsc_comparison(rs_czsc_obj, bars, df)
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)


if __name__ == "__main__":
    main()