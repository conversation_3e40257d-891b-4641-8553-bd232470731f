# This file is automatically generated by pyo3_stub_gen
# ruff: noqa: E501, F401

import builtins
import datetime
import numpy
import numpy.typing
import typing
from enum import Enum

class BI:
    r"""
    笔
    """
    @property
    def sdt(self) -> datetime.datetime: ...
    @property
    def direction(self) -> Direction: ...
    @property
    def symbol(self) -> builtins.str: ...
    @property
    def fx_a(self) -> FX: ...
    @property
    def fx_b(self) -> FX: ...
    @property
    def fxs(self) -> builtins.list[FX]: ...
    @property
    def edt(self) -> datetime.datetime: ...
    @property
    def fake_bis(self) -> builtins.list[FakeBI]: ...
    @property
    def high(self) -> builtins.float: ...
    @property
    def low(self) -> builtins.float: ...
    @property
    def power_price(self) -> builtins.float:
        r"""
        价差力度
        """
    @property
    def power(self) -> builtins.float: ...
    @property
    def power_volume(self) -> builtins.float:
        r"""
        成交量力度
        """
    @property
    def power_snr(self) -> builtins.float:
        r"""
        SNR 度量力度
        SNR越大，说明内部走势越顺畅，力度也就越大
        """
    @property
    def change(self) -> builtins.float:
        r"""
        笔的涨跌幅
        """
    @property
    def SNR(self) -> builtins.float:
        r"""
        笔内部的信噪比
        """
    @property
    def slope(self) -> builtins.float:
        r"""
        笔内部高低点之间的斜率
        """
    @property
    def acceleration(self) -> builtins.float:
        r"""
        笔内部价格的加速度
        
        负号表示开口向下；正号表示开口向上。数值越大，表示加速度越大。
        """
    @property
    def length(self) -> builtins.int:
        r"""
        笔的无包含关系K线数量
        """
    @property
    def rsq(self) -> builtins.float:
        r"""
        笔的原始K线close单变量线性回归 拟合优度
        """
    @property
    def hypotenuse(self) -> builtins.float:
        r"""
        笔的斜边长度
        """
    @property
    def angle(self) -> builtins.float:
        r"""
        笔的斜边与竖直方向的夹角，角度越大，力度越大
        """
    @property
    def raw_bars(self) -> builtins.list[RawBar]:
        r"""
        构成笔的原始K线序列，不包含首尾分型的首根K线
        """
    def __repr__(self) -> builtins.str: ...

class BarGenerator:
    def __new__(cls, base_freq:Freq, freqs:typing.Sequence[Freq], max_count:builtins.int, market:Market) -> BarGenerator: ...
    def init_freq_bars(self, freq:Freq, bars:typing.Sequence[RawBar]) -> None:
        r"""
        初始化某个周期的K线序列
        
        # 函数计算逻辑
        
        1. 检查输入的`freq`是否存在于`self.freq_bars`的键中。如果不存在，返回错误。
        2. 检查`self.freq_bars[freq]`是否为空。如果不为空，返回错误，表示不允许重复初始化。
        3. 如果以上检查都通过，将输入的`bars`存储到`self.freq_bars[freq]`中。
        4. 从`bars`中获取最后一根K线的交易标的代码，更新`self.symbol`。
        
        # Arguments
        
        * `freq` - 周期名称
        * `bars` - K线序列
        """
    def get_latest_date(self) -> typing.Optional[builtins.str]:
        r"""
        获取最新K线日期
        """
    def get_symbol(self) -> typing.Optional[builtins.str]:
        r"""
        获取所属品种
        """
    def update(self, bar:RawBar) -> None:
        r"""
        更新各周期K线
        
        # 函数计算逻辑
        
        1. 获取基准周期`base_freq`，并验证输入`bar`的周期值是否与之匹配
        2. 更新`self.symbol`和`self.end_dt`为当前K线的对应值
        3. 检查重复性：
           - 检查`self.bars[base_freq]`中是否已存在相同时间的K线
           - 如果存在重复K线，返回错误，不进行更新
        4. 如果无重复，遍历所有周期：
           - 对每个周期调用`update_freq`方法更新K线数据
        5. 维护数据量：
           - 遍历所有周期的K线数据
           - 确保每个周期的K线数量不超过`max_count`
           - 如果超过限制，保留最新的`max_count`条数据
        
        # Arguments
        
        * `bar` - 已完成的基准周期K线的引用
        """

class CZSC:
    @property
    def symbol(self) -> builtins.str: ...
    @property
    def freq(self) -> Freq: ...
    @property
    def max_bi_num(self) -> builtins.int: ...
    @property
    def bi_list(self) -> builtins.list[BI]: ...
    def __new__(cls, bars_raw:typing.Sequence[RawBar], max_bi_num:builtins.int=50) -> CZSC: ...
    def get_fx_list(self) -> builtins.list[FX]:
        r"""
        获取分型列表
        """
    def update(self, bar:RawBar) -> None:
        r"""
        更新K线数据
        """
    def __repr__(self) -> builtins.str: ...

class FX:
    r"""
    分型
    """
    @property
    def symbol(self) -> builtins.str: ...
    @property
    def dt(self) -> datetime.datetime: ...
    @property
    def mark(self) -> Mark: ...
    @property
    def high(self) -> builtins.float: ...
    @property
    def low(self) -> builtins.float: ...
    @property
    def fx(self) -> builtins.float: ...
    @property
    def new_bars(self) -> builtins.list[NewBar]:
        r"""
        获取构成分型的NewBar列表
        """
    @property
    def raw_bars(self) -> builtins.list[RawBar]:
        r"""
        获取原始K线列表（从NewBar的elements中提取）
        """
    @property
    def power_str(self) -> builtins.str:
        r"""
        判断分型强度
        """
    @property
    def power_volume(self) -> builtins.float:
        r"""
        计算成交量力度
        """
    @property
    def has_zs(self) -> builtins.bool:
        r"""
        判断构成分型的三根无包含K线是否有重叠中枢
        """
    def __repr__(self) -> builtins.str: ...

class FakeBI:
    r"""
    虚拟笔
    主要为笔的内部分析提供便利
    """
    @property
    def symbol(self) -> builtins.str: ...
    @property
    def sdt(self) -> datetime.datetime: ...
    @property
    def edt(self) -> datetime.datetime: ...
    @property
    def direction(self) -> Direction: ...
    @property
    def high(self) -> builtins.float: ...
    @property
    def low(self) -> builtins.float: ...
    @property
    def power(self) -> builtins.float: ...
    def __repr__(self) -> builtins.str: ...

class NewBar:
    r"""
    去除包含关系后的K线元素
    """
    @property
    def symbol(self) -> builtins.str: ...
    @property
    def dt(self) -> builtins.int: ...
    @property
    def freq(self) -> Freq: ...
    @property
    def id(self) -> builtins.int: ...
    @property
    def open(self) -> builtins.float: ...
    @property
    def close(self) -> builtins.float: ...
    @property
    def high(self) -> builtins.float: ...
    @property
    def low(self) -> builtins.float: ...
    @property
    def vol(self) -> builtins.float: ...
    @property
    def amount(self) -> builtins.float: ...
    @property
    def elements(self) -> builtins.list[RawBar]:
        r"""
        获取原始K线列表
        """
    @property
    def raw_bars(self) -> builtins.list[RawBar]:
        r"""
        获取原始K线列表（别名方法）
        """
    def __new__(cls, symbol:builtins.str, dt:typing.Any, freq:Freq, id:builtins.int, open:builtins.float, close:builtins.float, high:builtins.float, low:builtins.float, vol:builtins.float, amount:builtins.float, elements:typing.Optional[typing.Sequence[RawBar]]=None) -> NewBar: ...

class PyWeightBacktest:
    @staticmethod
    def from_arrow(data:bytes, digits:builtins.int=2, fee_rate:typing.Optional[builtins.float]=0.0002, n_jobs:typing.Optional[builtins.int]=4, weight_type:builtins.str='ts', yearly_days:builtins.int=252) -> PyWeightBacktest: ...
    def stats(self) -> dict: ...
    def daily_return(self) -> bytes: ...
    def dailys(self) -> bytes: ...
    def alpha(self) -> bytes: ...
    def pairs(self) -> bytes: ...

class RawBar:
    r"""
    原始K线元素
    """
    @property
    def symbol(self) -> builtins.str: ...
    @property
    def dt(self) -> builtins.int: ...
    @property
    def freq(self) -> Freq: ...
    @property
    def id(self) -> builtins.int: ...
    @property
    def open(self) -> builtins.float: ...
    @property
    def close(self) -> builtins.float: ...
    @property
    def high(self) -> builtins.float: ...
    @property
    def low(self) -> builtins.float: ...
    @property
    def vol(self) -> builtins.float: ...
    @property
    def amount(self) -> builtins.float: ...
    @property
    def upper(self) -> builtins.float:
        r"""
        上影
        """
    @property
    def lower(self) -> builtins.float:
        r"""
        下影
        """
    @property
    def solid(self) -> builtins.float:
        r"""
        实体
        """
    def __new__(cls, symbol:builtins.str, dt:typing.Any, freq:Freq, open:builtins.float, close:builtins.float, high:builtins.float, low:builtins.float, vol:builtins.float, amount:builtins.float, id:builtins.int=0) -> RawBar: ...
    def __repr__(self) -> builtins.str: ...

class Direction(Enum):
    r"""
    方向
    """
    Up = ...
    r"""
    向上
    """
    Down = ...
    r"""
    向下
    """

class Freq(Enum):
    r"""
    时间周期
    """
    Tick = ...
    r"""
    逐笔
    """
    F1 = ...
    r"""
    1分钟
    """
    F2 = ...
    r"""
    2分钟
    """
    F3 = ...
    r"""
    3分钟
    """
    F4 = ...
    r"""
    4分钟
    """
    F5 = ...
    r"""
    5分钟
    """
    F6 = ...
    r"""
    6分钟
    """
    F10 = ...
    r"""
    10分钟
    """
    F12 = ...
    r"""
    12分钟
    """
    F15 = ...
    r"""
    15分钟
    """
    F20 = ...
    r"""
    20分钟
    """
    F30 = ...
    r"""
    30分钟
    """
    F60 = ...
    r"""
    60分钟
    """
    F120 = ...
    r"""
    120分钟
    """
    F240 = ...
    r"""
    240分钟
    """
    F360 = ...
    r"""
    360分钟
    """
    D = ...
    r"""
    日线
    """
    W = ...
    r"""
    周线
    """
    M = ...
    r"""
    月线
    """
    S = ...
    r"""
    季线
    """
    Y = ...
    r"""
    年线
    """

    def is_minute_freq(self) -> builtins.bool:
        r"""
        判断是否为分钟级别的周期
        """
    def minutes(self) -> typing.Optional[builtins.int]:
        r"""
        获取对应的分钟数
        """

class Mark(Enum):
    r"""
    分型类型
    """
    D = ...
    r"""
    底分型
    """
    G = ...
    r"""
    顶分型
    """

class Market(Enum):
    AShare = ...
    r"""
    A股
    """
    Futures = ...
    r"""
    期货
    """
    Default = ...
    r"""
    默认
    """

def boll_positions(series:typing.Sequence[builtins.float], n:builtins.int, k:builtins.float) -> builtins.list[builtins.int]:
    r"""
    布林线多空信号
    返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
    """

def boll_reverse_positions(series:typing.Sequence[builtins.float], n:builtins.int, k:builtins.float) -> builtins.list[builtins.int]:
    r"""
    布林带反转策略的多空持仓信号
    返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
    """

def chip_distribution_triangle(data:numpy.typing.NDArray[numpy.float64], price_step:builtins.float, decay_factor:builtins.float) -> tuple[numpy.typing.NDArray[numpy.float64], numpy.typing.NDArray[numpy.float64]]:
    r"""
    计算筹码分布（三角形分布 + 筹码沉淀机制）
    
    此函数用于估算基于历史K线的筹码分布情况，结合三角形分布模型和筹码沉淀（衰减）机制。
    
    # Python 接口说明
    
    输入一个二维 numpy 数组，形状为 (N, 3)，每一行对应一根K线，列顺序为：
    `[high, low, vol]`，类型必须为 `float64`。
    
    示例：
    ```python
    columns = ['high', 'low', 'vol']
    arr2 = df[columns].to_numpy(dtype=np.float64)
    price_centers, chip_dist = chip_distribution_triangle(arr2, 0.01, 0.9)
    ```
    
    # 参数
    
    - `data`: 二维数组，形状为 (N, 3)，分别是每根K线的最高价、最低价和成交量。
    - `price_step`: 分档间隔（如0.01表示以0.01为单位划分价格区间）。
    - `decay_factor`: 筹码衰减因子，表示前一根K线上的筹码有多少比例沉淀保留到下一根K线上，范围为(0, 1)，例如0.98表示保留98%。
    
    # 返回值
    
    返回一个元组 `(price_centers, chip_distribution)`:
    - `price_centers`: 一维数组，表示价格分布区间的中心价位。
    - `chip_distribution`: 一维数组，对应每个价格中心的筹码强度（权重/密度）。
    
    返回的两个数组长度相同，可用于绘制筹码分布图或进一步分析。
    """

def daily_performance(daily_returns:numpy.typing.NDArray[numpy.float64], yearly_days:typing.Optional[builtins.int]=None) -> typing.Any:
    r"""
    采用单利计算日收益数据的各项指标
    
    函数计算逻辑：
    
    1. 首先，将传入的日收益率数据转换为NumPy数组，并指定数据类型为float64。
    2. 然后，进行一系列判断：如果日收益率数据为空或标准差为零或全部为零，则返回字典，其中所有指标的值都为零。
    3. 如果日收益率数据满足要求，则进行具体的指标计算：
    
        - 年化收益率 = 日收益率列表的和 / 日收益率列表的长度 * 252
        - 夏普比率 = 日收益率的均值 / 日收益率的标准差 * 标准差的根号252
        - 最大回撤 = 累计日收益率的最高累积值 - 累计日收益率
        - 卡玛比率 = 年化收益率 / 最大回撤（如果最大回撤不为零，则除以最大回撤；否则为10）
        - 日胜率 = 大于零的日收益率的个数 / 日收益率的总个数
        - 年化波动率 = 日收益率的标准差 * 标准差的根号252
        - 下行波动率 = 日收益率中小于零的日收益率的标准差 * 标准差的根号252
        - 非零覆盖 = 非零的日收益率个数 / 日收益率的总个数
        - 回撤风险 = 最大回撤 / 年化波动率；一般认为 1 以下为低风险，1-2 为中风险，2 以上为高风险
    
    4. 将所有指标的值存储在字典中，其中键为指标名称，值为相应的计算结果。
    
    :param daily_returns: 日收益率数据，样例：
        [0.01, 0.02, -0.01, 0.03, 0.02, -0.02, 0.01, -0.01, 0.02, 0.01]
    :param yearly_days: 一年的交易日数，默认为 252
    :return: dict，输出样例如下
    
        {'绝对收益': 1.0595,
        '年化': 0.1419,
        '夏普': 0.7358,
        '最大回撤': 0.3803,
        '卡玛': 0.3732,
        '日胜率': 0.5237,
        '日盈亏比': 1.0351,
        '日赢面': 0.0658,
        '年化波动率': 0.1929,
        '下行波动率': 0.1409,
        '非零覆盖': 1.0,
        '盈亏平衡点': 0.9846,
        '新高间隔': 312.0,
        '新高占比': 0.0579,
        '回撤风险': 1.9712,
        '回归年度回报率': 0.1515,
        '长度调整平均最大回撤': 0.446}
    """

def double_sma_positions(series:typing.Sequence[builtins.float], n:builtins.int, m:builtins.int) -> builtins.list[builtins.float]:
    r"""
    双均线多空信号
    返回每个点的多空信号（-1.0, 0.0, 1.0）
    """

def ema(series:typing.Sequence[builtins.float], period:builtins.int) -> builtins.list[builtins.float]:
    r"""
    计算指数移动平均 (EMA)
    返回每个点的 EMA 值
    """

def exponential_smoothing(series:typing.Sequence[builtins.float], alpha:builtins.float) -> builtins.list[builtins.float]:
    r"""
    指数平滑 - 基础时间序列平滑技术
    返回平滑后的序列
    """

def holt_winters(series:typing.Sequence[builtins.float], season_length:builtins.int, alpha:builtins.float, beta:builtins.float, gamma:builtins.float) -> builtins.list[builtins.float]:
    r"""
    Holt-Winters三参数平滑 - 支持趋势和季节性的平滑方法
    返回平滑后的序列
    """

def jurik_volty(close:typing.Sequence[builtins.float], period:builtins.int, power:builtins.float) -> builtins.list[builtins.float]:
    r"""
    Jurik波动平滑器 - 低噪声波动指标
    返回平滑波动率值
    """

def mid_positions(series:typing.Sequence[builtins.float], n:builtins.int) -> builtins.list[builtins.float]:
    r"""
    取窗口内的中间值作为中轴的多空信号
    返回每个点的多空信号（-1.0, 0.0, 1.0）
    """

def mms_positions(series:typing.Sequence[builtins.float], timeperiod:builtins.int, window:builtins.int) -> builtins.list[builtins.float]:
    r"""
    均线的最大最小值归一化
    返回归一化后的值，范围在 [-1, 1] 之间
    """

def normalize_feature(data:bytes, x_col:str, q:builtins.float) -> bytes: ...

def print_it(dt_utc_timestamp:builtins.int) -> None: ...

def rank_positions(series:typing.Sequence[builtins.float], n:builtins.int) -> builtins.list[builtins.float]:
    r"""
    rank 多空策略
    返回每个点的持仓信号（-1 到 1 之间的值）
    """

def rolling_rank(series:typing.Sequence[builtins.float], window:builtins.int) -> builtins.list[typing.Optional[builtins.int]]:
    r"""
    滚动排名函数
    计算每个数据点在其滚动窗口内的排名
    """

def rsi_reverse_positions(series:typing.Sequence[builtins.float], n:builtins.int, rsi_upper:builtins.float, rsi_lower:builtins.float, rsi_exit:builtins.float) -> builtins.list[builtins.int]:
    r"""
    RSI 反转策略的多空持仓信号
    返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
    """

def rsx_ss2(close:typing.Sequence[builtins.float], period:builtins.int, smooth_period:builtins.int) -> builtins.list[builtins.float]:
    r"""
    RSX-SS2 - 自适应平滑的RSI变体
    返回每个点的 RSX-SS2 值
    """

def single_ema_positions(series:typing.Sequence[builtins.float], n:builtins.int) -> builtins.list[builtins.float]:
    r"""
    单指数移动平均多空信号
    返回每个点的多空信号（-1.0, 0.0, 1.0）
    """

def single_sma_positions(series:typing.Sequence[builtins.float], n:builtins.int) -> builtins.list[builtins.float]:
    r"""
    单均线多空信号
    返回每个点的多空信号（-1.0, 0.0, 1.0）
    """

def tanh_positions(series:typing.Sequence[builtins.float], n:builtins.int) -> builtins.list[builtins.float]:
    r"""
    tanh 多空策略
    返回每个点的持仓信号（-1 到 1 之间的值）
    """

def top_drawdowns(returns:bytes, top:builtins.int) -> bytes: ...

def triple_sma_positions(series:typing.Sequence[builtins.float], m1:builtins.int, m2:builtins.int, m3:builtins.int) -> builtins.list[builtins.int]:
    r"""
    三均线系统持仓信号
    返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
    """

def true_range(high:typing.Sequence[builtins.float], low:typing.Sequence[builtins.float], close_prev:typing.Sequence[builtins.float]) -> builtins.list[builtins.float]:
    r"""
    计算真实波幅 (True Range)
    返回每个点的真实波幅值
    """

def ultimate_bands(close:typing.Sequence[builtins.float], period:builtins.int, std_multiplier:builtins.float, smooth_period:builtins.int) -> tuple[builtins.list[builtins.float], builtins.list[builtins.float], builtins.list[builtins.float]]:
    r"""
    终极带 - 基于终极平滑器的布林带变体
    返回 (中线, 上轨, 下轨)
    """

def ultimate_channel(high:typing.Sequence[builtins.float], low:typing.Sequence[builtins.float], close:typing.Sequence[builtins.float], period:builtins.int, multiplier:builtins.float) -> tuple[builtins.list[builtins.float], builtins.list[builtins.float], builtins.list[builtins.float]]:
    r"""
    终极通道 - 基于终极平滑器的通道指标
    返回 (中线, 上轨, 下轨)
    """

def ultimate_oscillator(high:typing.Sequence[builtins.float], low:typing.Sequence[builtins.float], close:typing.Sequence[builtins.float], short_period:builtins.int, med_period:builtins.int, long_period:builtins.int) -> builtins.list[builtins.float]:
    r"""
    终极波动指标 (UOS) - 多周期融合振荡器
    返回 UOS 值
    """

def ultimate_smoother(close:typing.Sequence[builtins.float], period:builtins.float) -> builtins.list[builtins.float]:
    r"""
    终极平滑器 - 低滞后平滑技术
    返回平滑后的序列
    """

