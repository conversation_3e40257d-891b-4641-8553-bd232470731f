#!/usr/bin/env python3
"""
时间戳转换工具，确保与 czsc 库的完全一致性
"""

import pandas as pd
from datetime import datetime
from typing import Union


def to_pandas_timestamp(dt: Union[datetime, pd.Timestamp]) -> pd.Timestamp:
    """
    将 datetime 对象转换为 pandas.Timestamp，确保与 czsc 库一致
    
    Args:
        dt: datetime 或 pandas.Timestamp 对象
        
    Returns:
        pandas.Timestamp 对象，不带时区信息
    """
    if isinstance(dt, pd.Timestamp):
        # 如果已经是 Timestamp，确保去掉时区信息
        return dt.tz_localize(None) if dt.tz else dt
    elif isinstance(dt, datetime):
        # 如果是 datetime，转换为 Timestamp 并去掉时区信息
        return pd.Timestamp(dt).tz_localize(None) if dt.tzinfo else pd.Timestamp(dt)
    else:
        # 尝试直接转换
        return pd.Timestamp(dt)


def patch_objects_with_timestamp_conversion():
    """
    为 rs_czsc 对象的时间戳属性添加自动转换功能
    """
    import rs_czsc
    
    # 保存原始类
    original_fx_class = rs_czsc.FX
    original_bi_class = rs_czsc.BI
    original_rawbar_class = rs_czsc.RawBar
    
    # 为 FX 类添加时间戳转换
    class PatchedFX(original_fx_class):
        @property
        def dt(self):
            original_dt = super().dt
            return to_pandas_timestamp(original_dt)
    
    # 为 BI 类添加时间戳转换
    class PatchedBI(original_bi_class):
        @property
        def sdt(self):
            original_sdt = super().sdt
            return to_pandas_timestamp(original_sdt)
            
        @property
        def edt(self):
            original_edt = super().edt
            return to_pandas_timestamp(original_edt)
    
    # 为 RawBar 类添加时间戳转换
    class PatchedRawBar(original_rawbar_class):
        @property
        def dt(self):
            original_dt = super().dt
            return to_pandas_timestamp(original_dt)
    
    # 替换原始类
    rs_czsc.FX = PatchedFX
    rs_czsc.BI = PatchedBI
    rs_czsc.RawBar = PatchedRawBar


# 自动应用补丁
try:
    patch_objects_with_timestamp_conversion()
except ImportError:
    # rs_czsc 尚未构建或导入失败
    pass