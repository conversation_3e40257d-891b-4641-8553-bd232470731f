#!/usr/bin/env python3
"""
枚举类型猴补丁，确保与 czsc 库的完全一致性
"""

def patch_enum_str_methods():
    """
    为 rs_czsc 的枚举类型添加正确的字符串表示方法
    """
    try:
        import rs_czsc
        
        # 为 Direction 类添加 __str__ 方法
        def direction_str(self):
            # 直接根据对象判断，避免递归
            try:
                # 检查对象是否等于特定值
                if str(type(self).__name__) == 'Direction':
                    # 尝试访问内部信息来判断是上还是下
                    # 这里用一个简单的方法：检查对象的内存表示或hash
                    obj_str = object.__str__(self)
                    if 'Up' in obj_str or 'up' in obj_str.lower():
                        return "向上"
                    elif 'Down' in obj_str or 'down' in obj_str.lower():
                        return "向下"
                return "向上"  # 默认值
            except:
                return "Direction"
        
        def mark_str(self):
            try:
                if str(type(self).__name__) == 'Mark':
                    obj_str = object.__str__(self)
                    if 'G' in obj_str or 'g' in obj_str.lower():
                        return "顶分型"
                    elif 'D' in obj_str or 'd' in obj_str.lower():
                        return "底分型"
                return "底分型"  # 默认值
            except:
                return "Mark"
        
        def freq_str(self):
            try:
                if str(type(self).__name__) == 'Freq':
                    obj_str = object.__str__(self)
                    if 'D' in obj_str:
                        return "日线"
                    elif 'W' in obj_str:
                        return "周线"
                    elif 'M' in obj_str:
                        return "月线"
                    elif 'S' in obj_str:
                        return "季线"
                    elif 'Y' in obj_str:
                        return "年线"
                    elif 'Tick' in obj_str:
                        return "Tick"
                    elif 'F1' in obj_str:
                        return "1分钟"
                    elif 'F2' in obj_str:
                        return "2分钟"
                    elif 'F3' in obj_str:
                        return "3分钟"
                    elif 'F4' in obj_str:
                        return "4分钟"
                    elif 'F5' in obj_str:
                        return "5分钟"
                    elif 'F6' in obj_str:
                        return "6分钟"
                    elif 'F10' in obj_str:
                        return "10分钟"
                    elif 'F12' in obj_str:
                        return "12分钟"
                    elif 'F15' in obj_str:
                        return "15分钟"
                    elif 'F20' in obj_str:
                        return "20分钟"
                    elif 'F30' in obj_str:
                        return "30分钟"
                    elif 'F60' in obj_str:
                        return "60分钟"
                    elif 'F120' in obj_str:
                        return "120分钟"
                    elif 'F240' in obj_str:
                        return "240分钟"
                    elif 'F360' in obj_str:
                        return "360分钟"
                return "日线"  # 默认值
            except:
                return "Freq"
        
        # 应用猴补丁
        if hasattr(rs_czsc, 'Direction'):
            rs_czsc.Direction.__str__ = direction_str
            rs_czsc.Direction.__repr__ = direction_str
            
        if hasattr(rs_czsc, 'Mark'):
            rs_czsc.Mark.__str__ = mark_str
            rs_czsc.Mark.__repr__ = mark_str
            
        if hasattr(rs_czsc, 'Freq'):
            rs_czsc.Freq.__str__ = freq_str
            rs_czsc.Freq.__repr__ = freq_str
            
    except ImportError:
        # rs_czsc 尚未构建或导入失败
        pass


# 自动应用补丁
patch_enum_str_methods()