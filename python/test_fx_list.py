#!/usr/bin/env python3
"""
测试 fx_list 属性访问
"""

import rs_czsc
import pandas as pd
import numpy as np

# 创建测试数据
np.random.seed(42)
dates = pd.date_range(start='2024-01-01', periods=50, freq='D')

base_price = 50.0
trend = np.linspace(0, 5, 50)
cycle = 2 * np.sin(np.linspace(0, 4*np.pi, 50))
noise = np.cumsum(np.random.normal(0, 0.5, 50))
close_prices = base_price + trend + cycle + noise

opens = []
highs = []
lows = []

for i, close in enumerate(close_prices):
    if i == 0:
        open_price = close + np.random.normal(0, 0.2)
    else:
        open_price = close_prices[i-1] + np.random.normal(0, 0.1)
    
    high = max(open_price, close) + abs(np.random.normal(0, 0.3))
    low = min(open_price, close) - abs(np.random.normal(0, 0.3))
    
    opens.append(open_price)
    highs.append(high)
    lows.append(low)

df = pd.DataFrame({
    'dt': dates,
    'symbol': ['000001.SZ'] * 50,
    'open': opens,
    'close': close_prices,
    'high': highs,
    'low': lows,
    'vol': np.random.randint(100000, 1000000, 50),
    'amount': np.random.randint(1000000, 10000000, 50)
})

# 格式化为 K线数据
bars = rs_czsc.format_standard_kline(df, freq=rs_czsc.Freq.D)
rs_obj = rs_czsc.CZSC(bars, max_bi_num=50)

print("测试 fx_list 属性访问:")
print("="*50)

# 测试属性访问
try:
    fx_list_attr = rs_obj.fx_list
    print(f"✓ rs_obj.fx_list 属性访问成功: 类型={type(fx_list_attr)}, 长度={len(fx_list_attr)}")
except Exception as e:
    print(f"✗ rs_obj.fx_list 属性访问失败: {e}")

# 测试方法访问  
try:
    fx_list_method = rs_obj.get_fx_list()
    print(f"✓ rs_obj.get_fx_list() 方法访问成功: 类型={type(fx_list_method)}, 长度={len(fx_list_method)}")
except Exception as e:
    print(f"✗ rs_obj.get_fx_list() 方法访问失败: {e}")

# 检查对象属性
print(f"\n对象属性列表:")
for attr in sorted(dir(rs_obj)):
    if not attr.startswith('_'):
        print(f"  {attr}")

print(f"\n对象包含 fx_list: {'fx_list' in dir(rs_obj)}")