[package]
name = "czsc-python"
version.workspace = true
edition = "2024"

[lib]
name = "_rs_czsc"
crate-type = ["cdylib", "rlib"]

[dependencies]
error-macros = { path = "../crates/error-macros" }
error-support = { path = "../crates/error-support" }
czsc = { path = "../crates/czsc", features = ["python"] }
czsc_ta = { path = "../crates/czsc-ta", features = ["rust-numpy"] }

polars = { workspace = true }
chrono.workspace = true
thiserror.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true

pyo3 = { workspace = true, features = [
    "chrono",
    "extension-module",
    "abi3",
    "abi3-py38",
] }
numpy = { workspace = true }
pyo3-stub-gen = { workspace = true }
env_logger = "0.11.8"

[[bin]]
name = "stub_gen"
doc = false
