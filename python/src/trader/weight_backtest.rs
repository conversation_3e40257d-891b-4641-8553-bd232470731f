use std::str::FromStr;

use crate::{
    errors::PythonError,
    utils::df_convert::{df_to_pyarrow, pyarrow_to_df},
};
use czsc::trader::weight_backtest::{WeightBacktest, WeightType};
use polars::prelude::IntoLazy;
use pyo3::{
    prelude::*,
    types::{PyBytes, PyBytesMethods, PyDict},
};
use pyo3_stub_gen::derive::{gen_stub_pyclass, gen_stub_pymethods};

#[gen_stub_pyclass]
#[pyclass(module = "rs_czsc._rs_czsc")]
#[repr(transparent)]
pub struct PyWeightBacktest {
    inner: WeightBacktest,
}

#[gen_stub_pymethods]
#[pymethods]
impl PyWeightBacktest {
    #[staticmethod]
    #[pyo3(signature = (data, digits=2, fee_rate=Some(0.0002), n_jobs=Some(4), weight_type="ts", yearly_days=252))]
    fn from_arrow<'py>(
        py: Python<'py>,
        data: Bound<'py, PyBytes>,
        digits: i64,
        fee_rate: Option<f64>,
        n_jobs: Option<usize>,
        weight_type: &str,
        yearly_days: usize,
    ) -> PyResult<Self> {
        let data = data.as_bytes();
        let df = pyarrow_to_df(data).map_err(PythonError::from)?;
        let weight_type = WeightType::from_str(weight_type).unwrap_or(WeightType::TS);

        let mut inner = WeightBacktest::new(df, digits, fee_rate).map_err(PythonError::from)?;
        let _report = py.allow_threads(|| {
            inner
                .backtest(n_jobs, weight_type, yearly_days)
                .map_err(PythonError::from)
        })?;
        Ok(Self { inner })
    }

    fn stats<'py>(&self, py: Python<'py>) -> PyResult<Bound<'py, PyDict>> {
        let py_dict = PyDict::new(py);

        if let Some(ref report) = self.inner.report {
            let stats = &report.stats;

            let dp = &stats.daily_performance;
            let ep = &stats.evaluate_pairs;
            py_dict.set_item("开始日期", stats.start_date.to_string())?;
            py_dict.set_item("结束日期", stats.end_date.to_string())?;
            py_dict.set_item("绝对收益", dp.absolute_return)?;
            py_dict.set_item("年化", dp.annual_returns)?;
            py_dict.set_item("夏普", dp.sharpe_ratio)?;
            py_dict.set_item("最大回撤", dp.max_drawdown)?;
            py_dict.set_item("卡玛", dp.calmar_ratio)?;
            py_dict.set_item("日胜率", dp.daily_win_rate)?;
            py_dict.set_item("日盈亏比", dp.daily_profit_loss_ratio)?;
            py_dict.set_item("日赢面", dp.daily_win_probability)?;
            py_dict.set_item("年化波动率", dp.annual_volatility)?;
            py_dict.set_item("下行波动率", dp.downside_volatility)?;
            py_dict.set_item("非零覆盖", dp.non_zero_coverage)?;
            py_dict.set_item("盈亏平衡点", dp.break_even_point)?;
            py_dict.set_item("新高间隔", dp.new_high_interval)?;
            py_dict.set_item("新高占比", dp.new_high_ratio)?;
            py_dict.set_item("回撤风险", dp.drawdown_risk)?;
            py_dict.set_item("回归年度回报率", dp.annual_lin_reg_cumsum_return)?;
            py_dict.set_item(
                "长度调整平均最大回撤",
                dp.length_adjusted_average_max_drawdown,
            )?;
            py_dict.set_item("交易胜率", ep.win_rate)?;
            py_dict.set_item("单笔收益", ep.single_trade_profit)?;
            py_dict.set_item("持仓K线数", ep.position_k_days)?;
            py_dict.set_item("多头占比", stats.long_rate)?;
            py_dict.set_item("空头占比", stats.short_rate)?;
            py_dict.set_item("与基准相关性", stats.relevance)?;
            py_dict.set_item("与基准空头相关性", stats.relevance_short)?;
            py_dict.set_item("波动比", stats.volatility_ratio)?;
            py_dict.set_item("与基准波动相关性", stats.relevance_volatility)?;
            py_dict.set_item("品种数量", stats.symbols_count)?;
        }

        Ok(py_dict)
    }

    fn daily_return<'py>(&mut self, py: Python<'py>) -> PyResult<Bound<'py, PyBytes>> {
        if let Some(ref mut report) = self.inner.report {
            let df_bytes = df_to_pyarrow(&mut report.daily_return)?;
            Ok(PyBytes::new(py, &df_bytes))
        } else {
            Ok(PyBytes::new(py, b"".as_slice()))
        }
    }

    fn dailys<'py>(&mut self, py: Python<'py>) -> PyResult<Bound<'py, PyBytes>> {
        let df_bytes = df_to_pyarrow(&mut self.inner.dailys)?;
        Ok(PyBytes::new(py, &df_bytes))
    }

    fn alpha<'py>(&mut self, py: Python<'py>) -> PyResult<Bound<'py, PyBytes>> {
        let mut df = self.inner.alpha().collect().map_err(PythonError::from)?;
        let df_bytes = df_to_pyarrow(&mut df)?;
        Ok(PyBytes::new(py, &df_bytes))
    }

    fn pairs<'py>(&mut self, py: Python<'py>) -> PyResult<Bound<'py, PyBytes>> {
        if let Some(ref report) = self.inner.report {
            // 收集所有品种的交易对数据
            let mut all_pairs = Vec::new();
            for symbol_report in &report.symbols {
                all_pairs.push(symbol_report.pair.clone().lazy());
            }

            if !all_pairs.is_empty() {
                let mut combined_pairs =
                    polars::prelude::concat(all_pairs, polars::prelude::UnionArgs::default())
                        .map_err(PythonError::from)?
                        .collect()
                        .map_err(PythonError::from)?;

                let df_bytes = df_to_pyarrow(&mut combined_pairs)?;
                Ok(PyBytes::new(py, &df_bytes))
            } else {
                Ok(PyBytes::new(py, b"".as_slice()))
            }
        } else {
            Ok(PyBytes::new(py, b"".as_slice()))
        }
    }
}
