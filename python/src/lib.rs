mod core;
mod errors;
mod trader;
mod utils;

use core::objects::{print_it, PyCZSC, PyBI, PyFakeBI, PyFX, PyRawBar, PyNewBar, PyBarGenerator, PyFreq, PyDirection, PyMark, PyMarket};
use pyo3::prelude::*;
use pyo3_stub_gen::define_stub_info_gatherer;
use trader::weight_backtest::PyWeightBacktest;
use utils::corr::normalize_feature;
use utils::daily_performance::daily_performance;
use utils::ta::*;
use utils::top_drawdowns::top_drawdowns;

/// PyO3 绑定的 Python 模块
#[pymodule]
fn _rs_czsc(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {
    // 添加工具函数
    m.add_function(wrap_pyfunction!(daily_performance, m)?)?;
    m.add_function(wrap_pyfunction!(top_drawdowns, m)?)?;
    m.add_function(wrap_pyfunction!(normalize_feature, m)?)?;

    // 添加技术分析函数

    m.add_function(wrap_pyfunction!(ultimate_smoother, m)?)?;
    m.add_function(wrap_pyfunction!(rolling_rank, m)?)?;
    m.add_function(wrap_pyfunction!(single_sma_positions, m)?)?;
    m.add_function(wrap_pyfunction!(single_ema_positions, m)?)?;
    m.add_function(wrap_pyfunction!(mid_positions, m)?)?;
    m.add_function(wrap_pyfunction!(double_sma_positions, m)?)?;
    m.add_function(wrap_pyfunction!(triple_sma_positions, m)?)?;
    m.add_function(wrap_pyfunction!(boll_positions, m)?)?;
    m.add_function(wrap_pyfunction!(boll_reverse_positions, m)?)?;
    m.add_function(wrap_pyfunction!(mms_positions, m)?)?;
    m.add_function(wrap_pyfunction!(rsi_reverse_positions, m)?)?;
    m.add_function(wrap_pyfunction!(tanh_positions, m)?)?;
    m.add_function(wrap_pyfunction!(rank_positions, m)?)?;
    m.add_function(wrap_pyfunction!(ema, m)?)?;
    m.add_function(wrap_pyfunction!(true_range, m)?)?;
    m.add_function(wrap_pyfunction!(rsx_ss2, m)?)?;
    m.add_function(wrap_pyfunction!(jurik_volty, m)?)?;
    m.add_function(wrap_pyfunction!(ultimate_channel, m)?)?;
    m.add_function(wrap_pyfunction!(ultimate_bands, m)?)?;
    m.add_function(wrap_pyfunction!(ultimate_oscillator, m)?)?;
    m.add_function(wrap_pyfunction!(exponential_smoothing, m)?)?;
    m.add_function(wrap_pyfunction!(holt_winters, m)?)?;
    m.add_function(wrap_pyfunction!(chip_distribution_triangle, m)?)?;

    // 添加回测相关类
    m.add_class::<PyWeightBacktest>()?;

    // 添加基础功能函数
    m.add_function(wrap_pyfunction!(print_it, m)?)?;

    // 添加基础数据类型
    m.add_class::<PyMarket>()?;
    m.add_class::<PyFreq>()?;
    m.add_class::<PyRawBar>()?;
    m.add_class::<PyNewBar>()?;
    m.add_class::<PyBarGenerator>()?;

    // 添加CZSC相关类型
    m.add_class::<PyCZSC>()?;
    m.add_class::<PyBI>()?;
    m.add_class::<PyFakeBI>()?;
    m.add_class::<PyFX>()?;
    m.add_class::<PyDirection>()?;
    m.add_class::<PyMark>()?;

    Ok(())
}

define_stub_info_gatherer!(stub_info);
