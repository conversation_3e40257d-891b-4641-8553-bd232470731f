use anyhow::anyhow;
use chrono::DateTime;
use czsc::{
    core::{
        analyze::CZSC,
        objects::{
            bar::{NewBar, NewBarBuilder, RawBar, RawBarBuilder},
            bi::BI,
            direction::Direction,
            fake_bi::FakeBI,
            freq::Freq,
            fx::FX,
            market::Market,
            mark::Mark,
        },
    },
    utils::bar_generator::BarGenerator,
};
use pyo3::prelude::*;
use pyo3::types::{PyAny, PyDict};
use pyo3_stub_gen::{derive::gen_stub_pymethods, PyStubType, TypeInfo};
use crate::errors::PythonError;

// 为自定义类型实现 PyStubType trait
impl PyStubType for PyMarket {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyMarket")
    }
}

impl PyStubType for PyFreq {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyFreq")
    }
}

impl PyStubType for PyDirection {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyDirection")
    }
}

impl PyStubType for PyMark {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyMark")
    }
}

impl PyStubType for PyBarGenerator {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyBarGenerator")
    }
}

impl PyStubType for PyFX {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyFX")
    }
}

impl PyStubType for PyFakeBI {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyFakeBI")
    }
}

impl PyStubType for PyBI {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyBI")
    }
}

impl PyStubType for PyNewBar {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyNewBar")
    }
}

impl PyStubType for PyRawBar {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyRawBar")
    }
}

impl PyStubType for PyCZSC {
    fn type_output() -> TypeInfo {
        TypeInfo::builtin("PyCZSC")
    }
}

/// 创建 OrderedDict，与 czsc 库兼容
fn create_ordered_dict(py: Python) -> PyResult<PyObject> {
    let collections = py.import("collections")?;
    let ordered_dict = collections.getattr("OrderedDict")?;
    let result = ordered_dict.call0()?;
    Ok(result.into())
}

#[pyclass(eq, eq_int, name = "Market")]
#[derive(PartialEq, Clone)]
pub enum PyMarket {
    /// A股
    AShare,
    /// 期货
    Futures,
    /// 默认
    Default,
}

impl From<PyMarket> for Market {
    fn from(value: PyMarket) -> Self {
        match value {
            PyMarket::AShare => Market::AShare,
            PyMarket::Futures => Market::Futures,
            PyMarket::Default => Market::Default,
        }
    }
}

#[pyclass(eq, eq_int, name = "Freq")]
#[derive(PartialEq, Clone)]
pub enum PyFreq {
    /// 逐笔
    Tick,
    /// 1分钟
    F1,
    /// 2分钟
    F2,
    /// 3分钟
    F3,
    /// 4分钟
    F4,
    /// 5分钟
    F5,
    /// 6分钟
    F6,
    /// 10分钟
    F10,
    /// 12分钟
    F12,
    /// 15分钟
    F15,
    /// 20分钟
    F20,
    /// 30分钟
    F30,
    /// 60分钟
    F60,
    /// 120分钟
    F120,
    /// 240分钟
    F240,
    /// 360分钟
    F360,
    /// 日线
    D,
    /// 周线
    W,
    /// 月线
    M,
    /// 季线
    S,
    /// 年线
    Y,
}

#[gen_stub_pymethods]
#[pymethods]
impl PyFreq {
    #[new]
    fn new(value: &str) -> PyResult<Self> {
        Ok(match value {
            "Tick" => PyFreq::Tick,
            "1分钟" | "F1" => PyFreq::F1,
            "2分钟" | "F2" => PyFreq::F2,
            "3分钟" | "F3" => PyFreq::F3,
            "4分钟" | "F4" => PyFreq::F4,
            "5分钟" | "F5" => PyFreq::F5,
            "6分钟" | "F6" => PyFreq::F6,
            "10分钟" | "F10" => PyFreq::F10,
            "12分钟" | "F12" => PyFreq::F12,
            "15分钟" | "F15" => PyFreq::F15,
            "20分钟" | "F20" => PyFreq::F20,
            "30分钟" | "F30" => PyFreq::F30,
            "60分钟" | "F60" => PyFreq::F60,
            "120分钟" | "F120" => PyFreq::F120,
            "240分钟" | "F240" => PyFreq::F240,
            "360分钟" | "F360" => PyFreq::F360,
            "日线" | "D" => PyFreq::D,
            "周线" | "W" => PyFreq::W,
            "月线" | "M" => PyFreq::M,
            "季线" | "S" => PyFreq::S,
            "年线" | "Y" => PyFreq::Y,
            _ => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Unknown freq value: {}", value)
            )),
        })
    }

    #[getter]
    fn value(&self) -> String {
        match self {
            PyFreq::Tick => "Tick".to_string(),
            PyFreq::F1 => "1分钟".to_string(),
            PyFreq::F2 => "2分钟".to_string(),
            PyFreq::F3 => "3分钟".to_string(),
            PyFreq::F4 => "4分钟".to_string(),
            PyFreq::F5 => "5分钟".to_string(),
            PyFreq::F6 => "6分钟".to_string(),
            PyFreq::F10 => "10分钟".to_string(),
            PyFreq::F12 => "12分钟".to_string(),
            PyFreq::F15 => "15分钟".to_string(),
            PyFreq::F20 => "20分钟".to_string(),
            PyFreq::F30 => "30分钟".to_string(),
            PyFreq::F60 => "60分钟".to_string(),
            PyFreq::F120 => "120分钟".to_string(),
            PyFreq::F240 => "240分钟".to_string(),
            PyFreq::F360 => "360分钟".to_string(),
            PyFreq::D => "日线".to_string(),
            PyFreq::W => "周线".to_string(),
            PyFreq::M => "月线".to_string(),
            PyFreq::S => "季线".to_string(),
            PyFreq::Y => "年线".to_string(),
        }
    }

    fn __str__(&self) -> PyResult<String> {
        Ok(match self {
            PyFreq::Tick => "Tick".to_string(),
            PyFreq::F1 => "1分钟".to_string(),
            PyFreq::F2 => "2分钟".to_string(),
            PyFreq::F3 => "3分钟".to_string(),
            PyFreq::F4 => "4分钟".to_string(),
            PyFreq::F5 => "5分钟".to_string(),
            PyFreq::F6 => "6分钟".to_string(),
            PyFreq::F10 => "10分钟".to_string(),
            PyFreq::F12 => "12分钟".to_string(),
            PyFreq::F15 => "15分钟".to_string(),
            PyFreq::F20 => "20分钟".to_string(),
            PyFreq::F30 => "30分钟".to_string(),
            PyFreq::F60 => "60分钟".to_string(),
            PyFreq::F120 => "120分钟".to_string(),
            PyFreq::F240 => "240分钟".to_string(),
            PyFreq::F360 => "360分钟".to_string(),
            PyFreq::D => "日线".to_string(),
            PyFreq::W => "周线".to_string(),
            PyFreq::M => "月线".to_string(),
            PyFreq::S => "季线".to_string(),
            PyFreq::Y => "年线".to_string(),
        })
    }

    fn __repr__(&self) -> PyResult<String> {
        self.__str__()
    }

    #[classattr]
    fn __members__(py: Python) -> PyResult<PyObject> {
        let dict = PyDict::new(py);
        dict.set_item("Tick", PyFreq::Tick)?;
        dict.set_item("F1", PyFreq::F1)?;
        dict.set_item("F2", PyFreq::F2)?;
        dict.set_item("F3", PyFreq::F3)?;
        dict.set_item("F4", PyFreq::F4)?;
        dict.set_item("F5", PyFreq::F5)?;
        dict.set_item("F6", PyFreq::F6)?;
        dict.set_item("F10", PyFreq::F10)?;
        dict.set_item("F12", PyFreq::F12)?;
        dict.set_item("F15", PyFreq::F15)?;
        dict.set_item("F20", PyFreq::F20)?;
        dict.set_item("F30", PyFreq::F30)?;
        dict.set_item("F60", PyFreq::F60)?;
        dict.set_item("F120", PyFreq::F120)?;
        dict.set_item("F240", PyFreq::F240)?;
        dict.set_item("F360", PyFreq::F360)?;
        dict.set_item("D", PyFreq::D)?;
        dict.set_item("W", PyFreq::W)?;
        dict.set_item("M", PyFreq::M)?;
        dict.set_item("S", PyFreq::S)?;
        dict.set_item("Y", PyFreq::Y)?;
        Ok(dict.into())
    }
    
    /// 支持深拷贝
    fn __deepcopy__(&self, _memo: &Bound<PyAny>) -> PyResult<Self> {
        Ok(self.clone())
    }
    
    /// 支持pickle序列化
    fn __reduce__(&self) -> PyResult<(PyObject, PyObject)> {
        Python::with_gil(|py| {
            let cls = py.get_type::<PyFreq>();
            let args = pyo3::types::PyTuple::new(py, &[self.value()])?;
            Ok((cls.into(), args.into()))
        })
    }
}

impl From<PyFreq> for Freq {
    fn from(value: PyFreq) -> Self {
        match value {
            PyFreq::Tick => Freq::Tick,
            PyFreq::F1 => Freq::F1,
            PyFreq::F2 => Freq::F2,
            PyFreq::F3 => Freq::F3,
            PyFreq::F4 => Freq::F4,
            PyFreq::F5 => Freq::F5,
            PyFreq::F6 => Freq::F6,
            PyFreq::F10 => Freq::F10,
            PyFreq::F12 => Freq::F12,
            PyFreq::F15 => Freq::F15,
            PyFreq::F20 => Freq::F20,
            PyFreq::F30 => Freq::F30,
            PyFreq::F60 => Freq::F60,
            PyFreq::F120 => Freq::F120,
            PyFreq::F240 => Freq::F240,
            PyFreq::F360 => Freq::F360,
            PyFreq::D => Freq::D,
            PyFreq::W => Freq::W,
            PyFreq::M => Freq::M,
            PyFreq::S => Freq::S,
            PyFreq::Y => Freq::Y,
        }
    }
}

#[pyclass(eq, eq_int, name = "Direction")]
#[derive(PartialEq, Clone)]
pub enum PyDirection {
    /// 向上
    Up,
    /// 向下
    Down,
}

#[gen_stub_pymethods]
#[pymethods]
impl PyDirection {
    fn __str__(&self) -> PyResult<String> {
        Ok(match self {
            PyDirection::Up => "向上".to_string(),
            PyDirection::Down => "向下".to_string(),
        })
    }

    fn __repr__(&self) -> PyResult<String> {
        self.__str__()
    }
    
    /// 支持深拷贝
    fn __deepcopy__(&self, _memo: &Bound<PyAny>) -> PyResult<Self> {
        Ok(self.clone())
    }
    
    /// 支持pickle序列化
    fn __reduce__(&self) -> PyResult<(PyObject, PyObject)> {
        Python::with_gil(|py| {
            let cls = py.get_type::<PyDirection>();
            let value = match self {
                PyDirection::Up => "Up",
                PyDirection::Down => "Down",
            };
            let args = pyo3::types::PyTuple::new(py, &[value])?;
            Ok((cls.into(), args.into()))
        })
    }
    
    #[new]
    fn new(value: &str) -> PyResult<Self> {
        Ok(match value {
            "Up" | "向上" => PyDirection::Up,
            "Down" | "向下" => PyDirection::Down,
            _ => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Unknown direction value: {}", value)
            )),
        })
    }
}

impl From<Direction> for PyDirection {
    fn from(value: Direction) -> Self {
        match value {
            Direction::Up => PyDirection::Up,
            Direction::Down => PyDirection::Down,
        }
    }
}

impl From<PyDirection> for Direction {
    fn from(value: PyDirection) -> Self {
        match value {
            PyDirection::Up => Direction::Up,
            PyDirection::Down => Direction::Down,
        }
    }
}

#[pyclass(eq, eq_int, name = "Mark")]
#[derive(PartialEq, Clone)]
pub enum PyMark {
    /// 顶分型
    G,
    /// 底分型
    D,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyMark {
    fn __str__(&self) -> PyResult<String> {
        Ok(match self {
            PyMark::G => "顶分型".to_string(),
            PyMark::D => "底分型".to_string(),
        })
    }

    fn __repr__(&self) -> PyResult<String> {
        self.__str__()
    }
}

impl From<Mark> for PyMark {
    fn from(value: Mark) -> Self {
        match value {
            Mark::G => PyMark::G,
            Mark::D => PyMark::D,
        }
    }
}

impl From<PyMark> for Mark {
    fn from(value: PyMark) -> Self {
        match value {
            PyMark::G => Mark::G,
            PyMark::D => Mark::D,
        }
    }
}

#[pyclass(name = "BarGenerator")]
#[repr(transparent)]
pub struct PyBarGenerator {
    inner: BarGenerator,
}

#[pymethods]
impl PyBarGenerator {
    #[new]
    fn new(
        base_freq: PyFreq,
        freqs: Vec<PyFreq>,
        max_count: usize,
        market: PyMarket,
    ) -> PyResult<Self> {
        Ok(PyBarGenerator {
            inner: BarGenerator::new(
                base_freq.into(),
                freqs.into_iter().map(|f| f.into()).collect(),
                max_count,
                market.into(),
            )
            .map_err(|e| PythonError::Utils(e))?,
        })
    }

    /// 初始化某个周期的K线序列
    ///
    /// # 函数计算逻辑
    ///
    /// 1. 检查输入的`freq`是否存在于`self.freq_bars`的键中。如果不存在，返回错误。
    /// 2. 检查`self.freq_bars[freq]`是否为空。如果不为空，返回错误，表示不允许重复初始化。
    /// 3. 如果以上检查都通过，将输入的`bars`存储到`self.freq_bars[freq]`中。
    /// 4. 从`bars`中获取最后一根K线的交易标的代码，更新`self.symbol`。
    ///
    /// # Arguments
    ///
    /// * `freq` - 周期名称
    /// * `bars` - K线序列
    fn init_freq_bars(&mut self, freq: PyFreq, bars: Vec<PyRawBar>) -> PyResult<()> {
        self.inner
            .init_freq_with_bars(freq.into(), bars.into_iter().map(|b| b.inner))
            .map_err(|e| PythonError::Utils(e))?;
        Ok(())
    }

    /// 获取最新K线日期
    fn get_latest_date(&self) -> Option<String> {
        self.inner
            .get_latest_date()
            .and_then(|dt| Some(dt.to_string()))
    }

    /// 获取所属品种
    fn get_symbol(&self) -> Option<String> {
        self.inner.get_symbol().and_then(|s| Some(s.to_string()))
    }

    /// 获取各周期K线数据 - 返回字典，键为频率字符串，值为K线列表
    #[getter]
    fn bars(&self, py: Python) -> PyResult<PyObject> {
        let dict = PyDict::new(py);
        
        // 暂时返回空字典，因为 freq_bars 字段是私有的
        // TODO: 需要在 BarGenerator 中添加公共访问方法
        Ok(dict.into())
    }

    /// 更新各周期K线
    ///
    /// # 函数计算逻辑
    ///
    /// 1. 获取基准周期`base_freq`，并验证输入`bar`的周期值是否与之匹配
    /// 2. 更新`self.symbol`和`self.end_dt`为当前K线的对应值
    /// 3. 检查重复性：
    ///    - 检查`self.bars[base_freq]`中是否已存在相同时间的K线
    ///    - 如果存在重复K线，返回错误，不进行更新
    /// 4. 如果无重复，遍历所有周期：
    ///    - 对每个周期调用`update_freq`方法更新K线数据
    /// 5. 维护数据量：
    ///    - 遍历所有周期的K线数据
    ///    - 确保每个周期的K线数量不超过`max_count`
    ///    - 如果超过限制，保留最新的`max_count`条数据
    ///
    /// # Arguments
    ///
    /// * `bar` - 已完成的基准周期K线的引用
    fn update(&self, bar: PyRawBar) -> PyResult<()> {
        self.inner
            .update_bar(&bar.inner)
            .map_err(|e| PythonError::Utils(e))?;
        Ok(())
    }
}

#[pyclass(name = "FX")]
#[derive(Clone)]
pub struct PyFX {
    inner: FX,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyFX {
    #[new]
    fn new(
        symbol: String,
        dt: DateTime<chrono::Utc>,
        mark: PyMark,
        high: f64,
        low: f64,
        fx: f64,
        elements: Vec<PyNewBar>,
    ) -> Self {
        PyFX {
            inner: FX {
                symbol: symbol.into(),
                dt,
                mark: mark.into(),
                high,
                low,
                fx,
                elements: elements.into_iter().map(|bar| bar.inner).collect(),
            },
        }
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn dt(&self) -> PyObject {
        Python::with_gil(|py| {
            let pandas = py.import("pandas").expect("无法导入pandas");
            let timestamp = pandas.getattr("Timestamp").expect("无法获取Timestamp类");
            let dt = self.inner.dt.naive_utc();
            // 使用ISO格式字符串创建带时区的 Timestamp
            let iso_string = dt.format("%Y-%m-%d %H:%M:%S").to_string();
            let tz_kwargs = PyDict::new(py);
            tz_kwargs.set_item("tz", "UTC").expect("无法设置时区");
            timestamp.call((iso_string,), Some(&tz_kwargs)).expect("无法创建Timestamp").into()
        })
    }

    #[getter]
    fn mark(&self) -> PyMark {
        self.inner.mark.clone().into()
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn fx(&self) -> f64 {
        self.inner.fx
    }

    /// 获取构成分型的NewBar列表
    #[getter]
    fn new_bars(&self) -> Vec<PyNewBar> {
        self.inner
            .elements
            .iter()
            .cloned()
            .map(|new_bar| PyNewBar { inner: new_bar })
            .collect()
    }

    /// 获取原始K线列表（从NewBar的elements中提取）
    #[getter]
    fn raw_bars(&self) -> Vec<PyRawBar> {
        self.inner
            .elements
            .iter()
            .flat_map(|new_bar| new_bar.elements.clone())
            .map(|raw_bar| PyRawBar { inner: raw_bar })
            .collect()
    }

    /// 获取分型强度字符串
    #[getter]
    fn power_str(&self) -> String {
        self.inner.power_str().to_string()
    }

    /// 获取成交量力度
    #[getter]
    fn power_volume(&self) -> f64 {
        self.inner.power_volume()
    }

    /// 判断是否有重叠中枢
    #[getter]
    fn has_zs(&self) -> bool {
        self.inner.has_zs()
    }

    /// 获取构成分型的NewBar列表（与new_bars相同，为兼容czsc库）
    #[getter]
    fn elements(&self) -> Vec<PyNewBar> {
        self.new_bars()
    }

    /// 缓存字典（与 czsc 库兼容）
    #[getter]
    fn cache(&self, py: Python) -> PyResult<PyObject> {
        create_ordered_dict(py)
    }

    fn __repr__(&self) -> String {
        format!(
            "FX(symbol={}, dt={}, mark={:?}, fx={})",
            self.inner.symbol,
            self.inner.dt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.mark,
            self.inner.fx
        )
    }
}

impl From<FX> for PyFX {
    fn from(fx: FX) -> Self {
        PyFX { inner: fx }
    }
}

#[pyclass(name = "FakeBI")]
#[derive(Clone)]
pub struct PyFakeBI {
    inner: FakeBI,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyFakeBI {
    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn sdt(&self) -> DateTime<chrono::Utc> {
        self.inner.sdt()
    }

    #[getter]
    fn edt(&self) -> DateTime<chrono::Utc> {
        self.inner.edt()
    }

    #[getter]
    fn direction(&self) -> PyDirection {
        self.inner.direction.into()
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn power(&self) -> f64 {
        self.inner.power
    }

    fn __repr__(&self) -> String {
        format!(
            "FakeBI(symbol={}, sdt={}, edt={}, direction={:?}, high={}, low={}, power={})",
            self.inner.symbol,
            self.inner.sdt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.edt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.direction,
            self.inner.high,
            self.inner.low,
            self.inner.power
        )
    }
}

impl From<FakeBI> for PyFakeBI {
    fn from(fake_bi: FakeBI) -> Self {
        PyFakeBI { inner: fake_bi }
    }
}

#[pyclass(name = "BI")]
#[derive(Clone)]
pub struct PyBI {
    inner: BI,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyBI {
    #[new]
    fn new(
        symbol: String,
        direction: PyDirection,
        fx_a: PyFX,
        fx_b: PyFX,
        fxs: Vec<PyFX>,
        bars: Vec<PyNewBar>,
    ) -> Self {
        PyBI {
            inner: BI {
                symbol: symbol.into(),
                direction: direction.into(),
                fx_a: fx_a.inner,
                fx_b: fx_b.inner,
                fxs: fxs.into_iter().map(|fx| fx.inner).collect(),
                bars: bars.into_iter().map(|bar| bar.inner).collect(),
            },
        }
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn direction(&self) -> PyDirection {
        self.inner.direction.into()
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high()
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low()
    }

    #[getter]
    fn sdt(&self) -> PyObject {
        Python::with_gil(|py| {
            let pandas = py.import("pandas").expect("无法导入pandas");
            let timestamp = pandas.getattr("Timestamp").expect("无法获取Timestamp类");
            let dt = self.inner.sdt().naive_utc();
            // 使用ISO格式字符串创建 Timestamp
            let iso_string = dt.format("%Y-%m-%d %H:%M:%S").to_string();
            let tz_kwargs = PyDict::new(py);
            tz_kwargs.set_item("tz", "UTC").expect("无法设置时区");
            timestamp.call((iso_string,), Some(&tz_kwargs)).expect("无法创建Timestamp").into()
        })
    }

    #[getter]
    fn edt(&self) -> PyObject {
        Python::with_gil(|py| {
            let pandas = py.import("pandas").expect("无法导入pandas");
            let timestamp = pandas.getattr("Timestamp").expect("无法获取Timestamp类");
            let dt = self.inner.edt().naive_utc();
            // 使用ISO格式字符串创建 Timestamp
            let iso_string = dt.format("%Y-%m-%d %H:%M:%S").to_string();
            let tz_kwargs = PyDict::new(py);
            tz_kwargs.set_item("tz", "UTC").expect("无法设置时区");
            timestamp.call((iso_string,), Some(&tz_kwargs)).expect("无法创建Timestamp").into()
        })
    }

    #[getter]
    fn fx_a(&self) -> PyFX {
        self.inner.fx_a.clone().into()
    }

    #[getter]
    fn fx_b(&self) -> PyFX {
        self.inner.fx_b.clone().into()
    }

    #[getter]
    fn fxs(&self) -> Vec<PyFX> {
        self.inner.fxs.iter().cloned().map(|fx| fx.into()).collect()
    }

    /// 获取构成笔的NewBar列表
    #[getter]
    fn bars(&self) -> Vec<PyNewBar> {
        self.inner.bars.iter().cloned().map(|bar| bar.into()).collect()
    }

    /// 价差力度
    #[getter]
    fn power(&self) -> f64 {
        self.inner.power()
    }

    /// 价差力度（别名）
    #[getter]
    fn power_price(&self) -> f64 {
        self.inner.power_price()
    }

    /// 成交量力度
    #[getter]
    fn power_volume(&self) -> f64 {
        self.inner.power_volume()
    }

    /// SNR 度量力度
    #[getter]
    fn power_snr(&self) -> f64 {
        self.inner.power_snr()
    }

    /// 笔的涨跌幅
    #[getter]
    fn change(&self) -> f64 {
        self.inner.change()
    }

    /// 笔内部的信噪比
    #[getter]
    fn SNR(&self) -> f64 {
        self.inner.SNR()
    }

    /// 笔内部高低点之间的斜率
    #[getter]
    fn slope(&self) -> f64 {
        self.inner.slope()
    }

    /// 笔内部价格的加速度
    #[getter]
    fn acceleration(&self) -> f64 {
        self.inner.acceleration()
    }

    /// 笔的无包含关系K线数量
    #[getter]
    fn length(&self) -> usize {
        self.inner.length()
    }

    /// 笔的原始K线close单变量线性回归拟合优度
    #[getter]
    fn rsq(&self) -> f64 {
        self.inner.rsq()
    }

    /// 笔的斜边长度
    #[getter]
    fn hypotenuse(&self) -> f64 {
        self.inner.hypotenuse()
    }

    /// 笔的斜边与竖直方向的夹角，角度越大，力度越大
    #[getter]
    fn angle(&self) -> f64 {
        self.inner.angle()
    }

    /// 构成笔的原始K线序列，不包含首尾分型的首根K线
    #[getter]
    fn raw_bars(&self) -> Vec<PyRawBar> {
        self.inner.raw_bars().into_iter().map(|bar| PyRawBar { inner: bar }).collect()
    }

    /// 笔的内部分型连接得到近似次级别笔列表
    #[getter]
    fn fake_bis(&self) -> Vec<PyFakeBI> {
        self.inner.fake_bis().into_iter().map(|fake_bi| fake_bi.into()).collect()
    }

    /// 缓存字典（与 czsc 库兼容）
    #[getter]
    fn cache(&self, py: Python) -> PyResult<PyObject> {
        create_ordered_dict(py)
    }

    /// 获取缓存值，如果不存在则返回默认值（与 czsc 库兼容）
    fn get_cache_with_default(&self, _key: &str, default_value: f64) -> f64 {
        default_value // 暂时返回默认值，因为我们的缓存是空的
    }

    /// 获取线性价格（与 czsc 库兼容）
    fn get_price_linear(&self, n: usize) -> f64 {
        // 简单实现：基于笔的高低点进行线性插值
        if n == 0 {
            if matches!(self.inner.direction, Direction::Up) {
                self.inner.low()
            } else {
                self.inner.high()
            }
        } else {
            if matches!(self.inner.direction, Direction::Up) {
                self.inner.high()
            } else {
                self.inner.low()
            }
        }
    }

    fn __repr__(&self) -> String {
        format!(
            "BI(symbol={}, sdt={}, edt={}, direction={:?}, high={}, low={})",
            self.inner.symbol,
            self.inner.sdt().format("%Y-%m-%d %H:%M:%S"),
            self.inner.edt().format("%Y-%m-%d %H:%M:%S"),
            self.inner.direction,
            self.inner.high(),
            self.inner.low()
        )
    }
}

impl From<BI> for PyBI {
    fn from(bi: BI) -> Self {
        PyBI { inner: bi }
    }
}

#[pyclass(name = "NewBar")]
#[derive(Clone)]
pub struct PyNewBar {
    inner: NewBar,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyNewBar {
    #[new]
    #[pyo3(signature = (symbol, dt, freq, id, open, close, high, low, vol, amount, elements=None))]
    fn new(
        _py: Python,
        symbol: &str,
        dt: &Bound<PyAny>,
        freq: PyFreq,
        id: i32,
        open: f64,
        close: f64,
        high: f64,
        low: f64,
        vol: f64,
        amount: f64,
        elements: Option<Vec<PyRawBar>>,
    ) -> PyResult<Self> {
        // 尝试解析dt参数，支持多种输入格式
        let datetime_utc = if dt.hasattr("timestamp")? {
            // 如果是Python datetime对象（有timestamp方法）
            let timestamp = dt.call_method0("timestamp")?;
            let timestamp_f64: f64 = timestamp.extract()?;
            DateTime::from_timestamp(timestamp_f64 as i64, (timestamp_f64.fract() * 1_000_000_000.0) as u32)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid datetime for building NewBar")))?
        } else if dt.hasattr("tz_localize")? {
            // 如果是pandas Timestamp，可能没有时区信息
            let localized_dt = if dt.getattr("tz")?.is_none() {
                // 如果没有时区，添加UTC时区
                dt.call_method1("tz_localize", ("UTC",))?
            } else {
                dt.clone()
            };
            let timestamp = localized_dt.call_method0("timestamp")?;
            let timestamp_f64: f64 = timestamp.extract()?;
            DateTime::from_timestamp(timestamp_f64 as i64, (timestamp_f64.fract() * 1_000_000_000.0) as u32)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid datetime for building NewBar")))?
        } else if let Ok(timestamp) = dt.extract::<i64>() {
            // 如果是时间戳（保持向后兼容）
            DateTime::from_timestamp(timestamp, 0)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid timestamp for building NewBar")))?
        } else if let Ok(timestamp_f64) = dt.extract::<f64>() {
            // 如果是浮点数时间戳
            DateTime::from_timestamp(timestamp_f64 as i64, (timestamp_f64.fract() * 1_000_000_000.0) as u32)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid timestamp for building NewBar")))?
        } else {
            return Err(PythonError::Unexpected(anyhow!(
                "dt parameter must be a Python datetime object, pandas Timestamp, integer timestamp, or float timestamp"
            )).into());
        };

        let raw_elements: Vec<RawBar> = elements
            .unwrap_or_default()
            .into_iter()
            .map(|py_bar| py_bar.inner)
            .collect();

        Ok(PyNewBar {
            inner: NewBarBuilder::default()
                .symbol(symbol)
                .dt(datetime_utc)
                .freq(Freq::from(freq))
                .id(id)
                .open(open)
                .close(close)
                .high(high)
                .low(low)
                .vol(vol)
                .amount(amount)
                .elements(raw_elements)
                .build()
                .map_err(|e| PythonError::Unexpected(anyhow!("{:?}", e)))?,
        })
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn dt(&self) -> PyObject {
        Python::with_gil(|py| {
            let pandas = py.import("pandas").expect("无法导入pandas");
            let timestamp = pandas.getattr("Timestamp").expect("无法获取Timestamp类");
            let dt = self.inner.dt.naive_utc();
            // 使用ISO格式字符串创建带时区的 Timestamp
            let iso_string = dt.format("%Y-%m-%d %H:%M:%S").to_string();
            let tz_kwargs = PyDict::new(py);
            tz_kwargs.set_item("tz", "UTC").expect("无法设置时区");
            timestamp.call((iso_string,), Some(&tz_kwargs)).expect("无法创建Timestamp").into()
        })
    }

    #[getter]
    fn freq(&self) -> PyFreq {
        match self.inner.freq {
            Freq::Tick => PyFreq::Tick,
            Freq::F1 => PyFreq::F1,
            Freq::F2 => PyFreq::F2,
            Freq::F3 => PyFreq::F3,
            Freq::F4 => PyFreq::F4,
            Freq::F5 => PyFreq::F5,
            Freq::F6 => PyFreq::F6,
            Freq::F10 => PyFreq::F10,
            Freq::F12 => PyFreq::F12,
            Freq::F15 => PyFreq::F15,
            Freq::F20 => PyFreq::F20,
            Freq::F30 => PyFreq::F30,
            Freq::F60 => PyFreq::F60,
            Freq::F120 => PyFreq::F120,
            Freq::F240 => PyFreq::F240,
            Freq::F360 => PyFreq::F360,
            Freq::D => PyFreq::D,
            Freq::W => PyFreq::W,
            Freq::M => PyFreq::M,
            Freq::S => PyFreq::S,
            Freq::Y => PyFreq::Y,
        }
    }

    #[getter]
    fn id(&self) -> i32 {
        self.inner.id
    }

    #[getter]
    fn open(&self) -> f64 {
        self.inner.open
    }

    #[getter]
    fn close(&self) -> f64 {
        self.inner.close
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn vol(&self) -> f64 {
        self.inner.vol
    }

    #[getter]
    fn amount(&self) -> f64 {
        self.inner.amount
    }

    /// 获取原始K线列表
    #[getter]
    fn elements(&self) -> Vec<PyRawBar> {
        self.inner
            .elements
            .iter()
            .cloned()
            .map(|raw_bar| PyRawBar { inner: raw_bar })
            .collect()
    }

    /// 获取原始K线列表（别名方法）
    #[getter]
    fn raw_bars(&self) -> Vec<PyRawBar> {
        self.elements()
    }

    /// 缓存字典（与 czsc 库兼容）
    #[getter]
    fn cache(&self, py: Python) -> PyResult<PyObject> {
        create_ordered_dict(py)
    }

    /// 支持深拷贝
    fn __deepcopy__(&self, _memo: &Bound<PyAny>) -> PyResult<Self> {
        Ok(self.clone())
    }

    fn __repr__(&self) -> String {
        format!(
            "NewBar(symbol={}, dt={}, freq={:?}, id={}, open={}, close={}, high={}, low={}, vol={}, amount={}, elements_count={})",
            self.inner.symbol,
            self.inner.dt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.freq,
            self.inner.id,
            self.inner.open,
            self.inner.close,
            self.inner.high,
            self.inner.low,
            self.inner.vol,
            self.inner.amount,
            self.inner.elements.len()
        )
    }
}

impl From<NewBar> for PyNewBar {
    fn from(new_bar: NewBar) -> Self {
        PyNewBar { inner: new_bar }
    }
}

#[pyclass(name = "RawBar")]
#[repr(transparent)]
#[derive(Clone)]
pub struct PyRawBar {
    inner: RawBar,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyRawBar {
    #[new]
    #[pyo3(signature = (symbol, dt, freq, open, close, high, low, vol, amount, id=0))]
    fn new(
        _py: Python,
        symbol: &str,
        dt: &Bound<PyAny>,
        freq: PyFreq,
        open: f64,
        close: f64,
        high: f64,
        low: f64,
        vol: f64,
        amount: f64,
        id: i32,
    ) -> PyResult<Self> {
        // 尝试解析dt参数，支持多种输入格式
        let datetime_utc = if dt.hasattr("timestamp")? {
            // 如果是Python datetime对象（有timestamp方法）
            let timestamp = dt.call_method0("timestamp")?;
            let timestamp_f64: f64 = timestamp.extract()?;
            DateTime::from_timestamp(timestamp_f64 as i64, (timestamp_f64.fract() * 1_000_000_000.0) as u32)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid datetime for building RawBar")))?
        } else if dt.hasattr("tz_localize")? {
            // 如果是pandas Timestamp，可能没有时区信息
            let localized_dt = if dt.getattr("tz")?.is_none() {
                // 如果没有时区，添加UTC时区
                dt.call_method1("tz_localize", ("UTC",))?
            } else {
                dt.clone()
            };
            let timestamp = localized_dt.call_method0("timestamp")?;
            let timestamp_f64: f64 = timestamp.extract()?;
            DateTime::from_timestamp(timestamp_f64 as i64, (timestamp_f64.fract() * 1_000_000_000.0) as u32)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid datetime for building RawBar")))?
        } else if let Ok(timestamp) = dt.extract::<i64>() {
            // 如果是时间戳（保持向后兼容）
            DateTime::from_timestamp(timestamp, 0)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid timestamp for building RawBar")))?
        } else if let Ok(timestamp_f64) = dt.extract::<f64>() {
            // 如果是浮点数时间戳
            DateTime::from_timestamp(timestamp_f64 as i64, (timestamp_f64.fract() * 1_000_000_000.0) as u32)
                .ok_or(PythonError::Unexpected(anyhow!("Invalid timestamp for building RawBar")))?
        } else {
            return Err(PythonError::Unexpected(anyhow!(
                "dt parameter must be a Python datetime object, pandas Timestamp, integer timestamp, or float timestamp"
            )).into());
        };

        Ok(PyRawBar {
            inner: RawBarBuilder::default()
                .symbol(symbol)
                .dt(datetime_utc)
                .freq(Freq::from(freq))
                .open(open)
                .close(close)
                .high(high)
                .low(low)
                .vol(vol)
                .id(id)
                .amount(amount)
                .build()
                .map_err(|e| PythonError::Unexpected(anyhow!("{:?}", e)))?,
        })
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn dt(&self) -> PyObject {
        Python::with_gil(|py| {
            let pandas = py.import("pandas").expect("无法导入pandas");
            let timestamp = pandas.getattr("Timestamp").expect("无法获取Timestamp类");
            let dt = self.inner.dt.naive_utc();
            // 使用ISO格式字符串创建带时区的 Timestamp
            let iso_string = dt.format("%Y-%m-%d %H:%M:%S").to_string();
            let tz_kwargs = PyDict::new(py);
            tz_kwargs.set_item("tz", "UTC").expect("无法设置时区");
            timestamp.call((iso_string,), Some(&tz_kwargs)).expect("无法创建Timestamp").into()
        })
    }

    #[getter]
    fn freq(&self) -> PyFreq {
        match self.inner.freq {
            Freq::Tick => PyFreq::Tick,
            Freq::F1 => PyFreq::F1,
            Freq::F2 => PyFreq::F2,
            Freq::F3 => PyFreq::F3,
            Freq::F4 => PyFreq::F4,
            Freq::F5 => PyFreq::F5,
            Freq::F6 => PyFreq::F6,
            Freq::F10 => PyFreq::F10,
            Freq::F12 => PyFreq::F12,
            Freq::F15 => PyFreq::F15,
            Freq::F20 => PyFreq::F20,
            Freq::F30 => PyFreq::F30,
            Freq::F60 => PyFreq::F60,
            Freq::F120 => PyFreq::F120,
            Freq::F240 => PyFreq::F240,
            Freq::F360 => PyFreq::F360,
            Freq::D => PyFreq::D,
            Freq::W => PyFreq::W,
            Freq::M => PyFreq::M,
            Freq::S => PyFreq::S,
            Freq::Y => PyFreq::Y,
        }
    }

    #[getter]
    fn id(&self) -> i32 {
        self.inner.id
    }

    #[getter]
    fn open(&self) -> f64 {
        self.inner.open
    }

    #[getter]
    fn close(&self) -> f64 {
        self.inner.close
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn vol(&self) -> f64 {
        self.inner.vol
    }

    #[getter]
    fn amount(&self) -> f64 {
        self.inner.amount
    }

    /// 上影线长度
    #[getter]
    fn upper(&self) -> f64 {
        self.inner.upper()
    }

    /// 下影线长度
    #[getter]
    fn lower(&self) -> f64 {
        self.inner.lower()
    }

    /// 实体长度
    #[getter]
    fn solid(&self) -> f64 {
        self.inner.solid()
    }

    /// 缓存字典（与 czsc 库兼容）
    #[getter]
    fn cache(&self, py: Python) -> PyResult<PyObject> {
        create_ordered_dict(py)
    }

    /// 支持 __dict__ 属性访问
    fn __getattr__(&self, py: Python, name: &str) -> PyResult<PyObject> {
        match name {
            "__dict__" => {
                let dict = PyDict::new(py);
                dict.set_item("symbol", self.inner.symbol.as_ref())?;
                dict.set_item("dt", self.inner.dt)?;
                dict.set_item("freq", match self.inner.freq {
                    Freq::Tick => "Tick",
                    Freq::F1 => "1分钟",
                    Freq::F2 => "2分钟",
                    Freq::F3 => "3分钟",
                    Freq::F4 => "4分钟",
                    Freq::F5 => "5分钟",
                    Freq::F6 => "6分钟",
                    Freq::F10 => "10分钟",
                    Freq::F12 => "12分钟",
                    Freq::F15 => "15分钟",
                    Freq::F20 => "20分钟",
                    Freq::F30 => "30分钟",
                    Freq::F60 => "60分钟",
                    Freq::F120 => "120分钟",
                    Freq::F240 => "240分钟",
                    Freq::F360 => "360分钟",
                    Freq::D => "日线",
                    Freq::W => "周线",
                    Freq::M => "月线",
                    Freq::S => "季线",
                    Freq::Y => "年线",
                })?;
                dict.set_item("id", self.inner.id)?;
                dict.set_item("open", self.inner.open)?;
                dict.set_item("close", self.inner.close)?;
                dict.set_item("high", self.inner.high)?;
                dict.set_item("low", self.inner.low)?;
                dict.set_item("vol", self.inner.vol)?;
                dict.set_item("amount", self.inner.amount)?;
                Ok(dict.into())
            }
            _ => Err(PyErr::new::<pyo3::exceptions::PyAttributeError, _>(
                format!("'RawBar' object has no attribute '{}'", name)
            ))
        }
    }


    /// 支持深拷贝
    fn __deepcopy__(&self, _memo: &Bound<PyAny>) -> PyResult<Self> {
        Ok(self.clone())
    }

    fn __repr__(&self) -> String {
        format!(
            "RawBar(symbol={}, dt={}, freq={:?}, id={}, open={}, close={}, high={}, low={}, vol={}, amount={})",
            self.inner.symbol,
            self.inner.dt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.freq,
            self.inner.id,
            self.inner.open,
            self.inner.close,
            self.inner.high,
            self.inner.low,
            self.inner.vol,
            self.inner.amount
        )
    }
}

#[pyclass(name = "CZSC")]
pub struct PyCZSC {
    inner: CZSC,
}
#[gen_stub_pymethods]
#[pymethods]
impl PyCZSC {
    #[new]
    #[pyo3(signature = (bars_raw, max_bi_num=50))]
    fn new(bars_raw: Vec<PyRawBar>, max_bi_num: usize) -> PyResult<Self> {
        let rust_bars: Vec<RawBar> = bars_raw.into_iter().map(|b| b.inner).collect();
        Ok(PyCZSC {
            inner: CZSC::new(rust_bars, max_bi_num),
        })
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn freq(&self) -> PyFreq {
        match self.inner.freq {
            Freq::Tick => PyFreq::Tick,
            Freq::F1 => PyFreq::F1,
            Freq::F2 => PyFreq::F2,
            Freq::F3 => PyFreq::F3,
            Freq::F4 => PyFreq::F4,
            Freq::F5 => PyFreq::F5,
            Freq::F6 => PyFreq::F6,
            Freq::F10 => PyFreq::F10,
            Freq::F12 => PyFreq::F12,
            Freq::F15 => PyFreq::F15,
            Freq::F20 => PyFreq::F20,
            Freq::F30 => PyFreq::F30,
            Freq::F60 => PyFreq::F60,
            Freq::F120 => PyFreq::F120,
            Freq::F240 => PyFreq::F240,
            Freq::F360 => PyFreq::F360,
            Freq::D => PyFreq::D,
            Freq::W => PyFreq::W,
            Freq::M => PyFreq::M,
            Freq::S => PyFreq::S,
            Freq::Y => PyFreq::Y,
        }
    }

    #[getter]
    fn max_bi_num(&self) -> usize {
        self.inner.max_bi_num
    }

    #[getter]
    fn bi_list(&self) -> Vec<PyBI> {
        self.inner.bi_list.iter().cloned().map(|bi| bi.into()).collect()
    }

    /// 获取原始K线序列
    #[getter]
    fn bars_raw(&self) -> Vec<PyRawBar> {
        self.inner.bars_raw.iter().cloned().map(|bar| PyRawBar { inner: bar }).collect()
    }

    /// 获取无包含关系K线序列
    #[getter]
    fn bars_ubi(&self) -> Vec<PyNewBar> {
        self.inner.bars_ubi.iter().cloned().map(|bar| PyNewBar { inner: bar }).collect()
    }

    /// 获取已完成的笔列表（与 bi_list 相同，为兼容 czsc 库）
    #[getter]
    fn finished_bis(&self) -> Vec<PyBI> {
        self.inner.bi_list.iter().cloned().map(|bi| bi.into()).collect()
    }

    /// 获取分型列表（属性，与 czsc 库兼容）
    #[getter]
    fn fx_list(&self) -> Vec<PyFX> {
        self.inner.fx_list().into_iter().map(|fx| fx.into()).collect()
    }



    /// 缓存字典（与 czsc 库兼容）
    #[getter]
    fn cache(&self, py: Python) -> PyResult<PyObject> {
        create_ordered_dict(py)
    }

    /// 信号字典（与 czsc 库兼容）
    #[getter]
    fn signals(&self, py: Python) -> PyResult<PyObject> {
        create_ordered_dict(py)
    }

    /// 无包含关系K线分型列表（与 czsc 库兼容）
    /// CZSC 库中 ubi_fxs 通常只包含已确认的分型
    #[getter]
    fn ubi_fxs(&self) -> Vec<PyFX> {
        let fx_list = self.fx_list();
        // 只返回已确认的分型（排除最后两个可能未确认的）
        if fx_list.len() > 2 {
            fx_list[..fx_list.len()-2].to_vec()
        } else {
            vec![]
        }
    }

    /// 无包含关系K线（与 czsc 库兼容）
    /// 返回未完成的笔信息，格式与 Python 版本保持一致
    #[getter]
    fn ubi(&self, py: Python) -> PyResult<PyObject> {
        let ubi_fxs = self.ubi_fxs();
        
        if self.inner.bars_ubi.is_empty() || self.inner.bi_list.is_empty() || ubi_fxs.is_empty() {
            return Ok(py.None());
        }

        // 获取所有原始K线
        let bars_raw: Vec<PyRawBar> = self.inner.bars_ubi
            .iter()
            .flat_map(|x| &x.elements)
            .cloned()
            .map(|raw_bar| PyRawBar { inner: raw_bar })
            .collect();

        if bars_raw.is_empty() {
            return Ok(py.None());
        }

        // 获取最高点和最低点
        let high_bar = bars_raw
            .iter()
            .max_by(|a, b| a.inner.high.partial_cmp(&b.inner.high).unwrap())
            .unwrap()
            .clone();
        
        let low_bar = bars_raw
            .iter()
            .min_by(|a, b| a.inner.low.partial_cmp(&b.inner.low).unwrap())
            .unwrap()
            .clone();

        // 确定方向：与最后一笔相反
        let direction = if self.inner.bi_list.last().unwrap().direction == czsc::core::objects::direction::Direction::Down {
            PyDirection::Up
        } else {
            PyDirection::Down
        };

        // 创建字典
        let dict = PyDict::new(py);
        dict.set_item("symbol", self.inner.symbol.as_ref())?;
        dict.set_item("direction", direction)?;
        dict.set_item("high", high_bar.inner.high)?;
        dict.set_item("low", low_bar.inner.low)?;
        dict.set_item("high_bar", high_bar)?;
        dict.set_item("low_bar", low_bar)?;
        dict.set_item("bars", self.bars_ubi())?;
        dict.set_item("raw_bars", bars_raw)?;
        dict.set_item("fxs", ubi_fxs.clone())?;
        dict.set_item("fx_a", ubi_fxs.first().unwrap().clone())?;

        Ok(dict.into())
    }

    /// 是否显示详细信息（与 czsc 库兼容）
    #[getter]
    fn verbose(&self) -> bool {
        false // 默认不显示详细信息
    }

    /// 最后一笔延伸情况（与 czsc 库兼容）
    /// 判断最后一笔是否在延伸中，True 表示延伸中
    #[getter]
    fn last_bi_extend(&self) -> bool {
        // 如果没有笔，返回 false
        if self.inner.bi_list.is_empty() {
            return false;
        }
        
        // 如果没有无包含关系K线，返回 false
        if self.inner.bars_ubi.is_empty() {
            return false;
        }
        
        let last_bi = &self.inner.bi_list[self.inner.bi_list.len() - 1];
        
        match last_bi.direction {
            Direction::Up => {
                // 向上笔：检查当前所有无包含K线的最高价是否 > 最后一笔的高点
                let max_high = self.inner.bars_ubi.iter()
                    .map(|bar| bar.high)
                    .max_by(|a, b| a.partial_cmp(b).unwrap())
                    .unwrap_or(0.0);
                
                max_high > last_bi.high()
            },
            Direction::Down => {
                // 向下笔：检查当前所有无包含K线的最低价是否 < 最后一笔的低点
                let min_low = self.inner.bars_ubi.iter()
                    .map(|bar| bar.low)
                    .min_by(|a, b| a.partial_cmp(b).unwrap())
                    .unwrap_or(f64::MAX);
                
                min_low < last_bi.low()
            }
        }
    }



    /// 在浏览器中打开（与 czsc 库兼容）
    #[pyo3(signature = (_renderer=None))]
    fn open_in_browser(&self, _renderer: Option<&str>) -> PyResult<String> {
        Ok("Browser opening not implemented in Rust version".to_string())
    }

    /// 转换为 ECharts 格式（与 czsc 库兼容）
    fn to_echarts(&self) -> PyResult<String> {
        Ok("ECharts export not implemented in Rust version".to_string())
    }

    /// 转换为 Plotly 格式（与 czsc 库兼容）
    fn to_plotly(&self) -> PyResult<String> {
        Ok("Plotly export not implemented in Rust version".to_string())
    }

    /// 更新K线数据
    fn update(&mut self, bar: PyRawBar) -> PyResult<()> {
        self.inner.update(bar.inner);
        Ok(())
    }

    fn __repr__(&self) -> String {
        format!(
            "CZSC(symbol={}, freq={:?}, max_bi_num={}, bi_count={})",
            self.inner.symbol,
            self.inner.freq,
            self.inner.max_bi_num,
            self.inner.bi_list.len()
        )
    }
}

#[pyfunction]
pub fn print_it(dt_utc_timestamp: i64) -> PyResult<()> {
    let dt = DateTime::from_timestamp(dt_utc_timestamp, 0).ok_or(PythonError::Unexpected(
        anyhow!("Invalid timestamp for building raw bar"),
    ))?;
    let dt_naive = dt.naive_utc();
    println!("{}", dt_naive);
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_print_it() {
        // print_it(1734003461);
        let res = print_it(-99999999999999999);
        println!("{:?}", res.err());
    }
}
