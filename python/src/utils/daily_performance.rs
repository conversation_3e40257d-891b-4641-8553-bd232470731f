use crate::errors::PythonError;
use numpy::PyReadonlyArray1;
use pyo3::{prelude::*, types::PyDict};
use pyo3_stub_gen::derive::gen_stub_pyfunction;

/// 采用单利计算日收益数据的各项指标
///
/// 函数计算逻辑：
///
/// 1. 首先，将传入的日收益率数据转换为NumPy数组，并指定数据类型为float64。
/// 2. 然后，进行一系列判断：如果日收益率数据为空或标准差为零或全部为零，则返回字典，其中所有指标的值都为零。
/// 3. 如果日收益率数据满足要求，则进行具体的指标计算：
///
///     - 年化收益率 = 日收益率列表的和 / 日收益率列表的长度 * 252
///     - 夏普比率 = 日收益率的均值 / 日收益率的标准差 * 标准差的根号252
///     - 最大回撤 = 累计日收益率的最高累积值 - 累计日收益率
///     - 卡玛比率 = 年化收益率 / 最大回撤（如果最大回撤不为零，则除以最大回撤；否则为10）
///     - 日胜率 = 大于零的日收益率的个数 / 日收益率的总个数
///     - 年化波动率 = 日收益率的标准差 * 标准差的根号252
///     - 下行波动率 = 日收益率中小于零的日收益率的标准差 * 标准差的根号252
///     - 非零覆盖 = 非零的日收益率个数 / 日收益率的总个数
///     - 回撤风险 = 最大回撤 / 年化波动率；一般认为 1 以下为低风险，1-2 为中风险，2 以上为高风险
///
/// 4. 将所有指标的值存储在字典中，其中键为指标名称，值为相应的计算结果。
///
/// :param daily_returns: 日收益率数据，样例：
///     [0.01, 0.02, -0.01, 0.03, 0.02, -0.02, 0.01, -0.01, 0.02, 0.01]
/// :param yearly_days: 一年的交易日数，默认为 252
/// :return: dict，输出样例如下
///
///     {'绝对收益': 1.0595,
///     '年化': 0.1419,
///     '夏普': 0.7358,
///     '最大回撤': 0.3803,
///     '卡玛': 0.3732,
///     '日胜率': 0.5237,
///     '日盈亏比': 1.0351,
///     '日赢面': 0.0658,
///     '年化波动率': 0.1929,
///     '下行波动率': 0.1409,
///     '非零覆盖': 1.0,
///     '盈亏平衡点': 0.9846,
///     '新高间隔': 312.0,
///     '新高占比': 0.0579,
///     '回撤风险': 1.9712,
///     '回归年度回报率': 0.1515,
///     '长度调整平均最大回撤': 0.446}
#[gen_stub_pyfunction]
#[pyfunction]
#[pyo3(signature = (daily_returns, yearly_days=None))]
pub fn daily_performance<'py>(
    py: Python<'py>,
    daily_returns: PyReadonlyArray1<'py, f64>,
    yearly_days: Option<usize>,
) -> PyResult<PyObject> {
    let daily_returns = daily_returns.as_slice().map_err(PythonError::from)?;
    let dp = czsc::utils::daily_performance::daily_performance(daily_returns, yearly_days)
        .map_err(PythonError::from)?;

    let py_dict = PyDict::new(py);

    py_dict.set_item("绝对收益", dp.absolute_return)?;
    py_dict.set_item("年化", dp.annual_returns)?;
    py_dict.set_item("夏普", dp.sharpe_ratio)?;
    py_dict.set_item("最大回撤", dp.max_drawdown)?;
    py_dict.set_item("卡玛", dp.calmar_ratio)?;
    py_dict.set_item("日胜率", dp.daily_win_rate)?;
    py_dict.set_item("日盈亏比", dp.daily_profit_loss_ratio)?;
    py_dict.set_item("日赢面", dp.daily_win_probability)?;
    py_dict.set_item("年化波动率", dp.annual_volatility)?;
    py_dict.set_item("下行波动率", dp.downside_volatility)?;
    py_dict.set_item("非零覆盖", dp.non_zero_coverage)?;
    py_dict.set_item("盈亏平衡点", dp.break_even_point)?;
    py_dict.set_item("新高间隔", dp.new_high_interval)?;
    py_dict.set_item("新高占比", dp.new_high_ratio)?;
    py_dict.set_item("回撤风险", dp.drawdown_risk)?;
    py_dict.set_item("回归年度回报率", dp.annual_lin_reg_cumsum_return)?;
    py_dict.set_item(
        "长度调整平均最大回撤",
        dp.length_adjusted_average_max_drawdown,
    )?;

    Ok(py_dict.into())
}
