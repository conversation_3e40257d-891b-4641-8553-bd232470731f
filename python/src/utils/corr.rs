use super::df_convert::{df_to_pyarrow, pyarrow_to_df};
use crate::errors::PythonError;
use czsc::core;
use pyo3::{
    prelude::*,
    types::{PyBytes, PyBytesMethods, PyString, PyStringMethods},
};
use pyo3_stub_gen::derive::gen_stub_pyfunction;

#[gen_stub_pyfunction]
#[pyfunction]
#[pyo3(signature = (data, x_col, q))]
pub fn normalize_feature<'py>(
    py: Python<'py>,
    data: Bound<'py, PyBytes>,
    x_col: Bound<'py, PyString>,
    q: f64,
) -> PyResult<Bound<'py, PyBytes>> {
    let df = pyarrow_to_df(data.as_bytes()).map_err(PythonError::from)?;

    let x_col = x_col.to_cow()?;

    let mut df =
        core::utils::normalize_ic::normalize_feature(df, &x_col, q).map_err(PythonError::from)?;

    let df_bytes = df_to_pyarrow(&mut df)?;
    Ok(PyBytes::new(py, &df_bytes))
}
