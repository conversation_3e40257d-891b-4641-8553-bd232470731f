use super::df_convert::{df_to_pyarrow, pyarrow_to_df};
use crate::errors::PythonError;
use anyhow::anyhow;
use czsc::utils;
use polars::{frame::DataFrame, prelude::DataType};
use pyo3::{prelude::*, types::{PyBytesMethods, PyBytes}};
use pyo3_stub_gen::derive::gen_stub_pyfunction;

#[gen_stub_pyfunction]
#[pyfunction]
#[pyo3(signature = (returns, top))]
pub fn top_drawdowns<'py>(
    py: Python<'py>,
    returns: Bound<'py, PyBytes>,
    top: usize,
) -> PyResult<Bound<'py, PyBytes>> {
    let data = returns.as_bytes();
    let mut df = inner_top_drawdowns(data, top)?;

    let df_bytes = df_to_pyarrow(&mut df)?;
    Ok(PyBytes::new(py, &df_bytes))
}

fn inner_top_drawdowns<'py>(returns: &[u8], top: usize) -> Result<DataFrame, PythonError> {
    let df = pyarrow_to_df(returns).map_err(PythonError::from)?;

    let dt_col = df.column("date")?;
    let dt_type = dt_col.dtype();
    let dates = match dt_type {
        DataType::Datetime(_, _) => dt_col
            .datetime()?
            .as_datetime_iter()
            .filter_map(|d| d)
            .map(|d| d.date())
            .collect::<Vec<_>>(),
        DataType::Date => dt_col
            .date()?
            .as_date_iter()
            .filter_map(|d| d)
            .collect::<Vec<_>>(),
        _ => {
            return Err(PythonError::Unexpected(anyhow!(
                "Unsupported datetime type: {:?}",
                dt_type
            )));
        }
    };

    let returns = df
        .column("returns")?
        .f64()?
        .into_no_null_iter()
        .collect::<Vec<_>>();

    let df = utils::top_drawdowns::top_drawdowns(&returns, &dates, Some(top))?;
    Ok(df)
}
