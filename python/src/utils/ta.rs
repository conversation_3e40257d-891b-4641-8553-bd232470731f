use czsc_ta::{mixed, pure};
use pyo3::prelude::*;
use pyo3_stub_gen::derive::gen_stub_pyfunction;

/// 终极平滑器 - 低滞后平滑技术
/// 返回平滑后的序列
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn ultimate_smoother(close: Vec<f64>, period: f64) -> Vec<f64> {
    pure::ultimate_smoother(&close, period)
}

/// 滚动排名函数
/// 计算每个数据点在其滚动窗口内的排名
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn rolling_rank(series: Vec<f64>, window: usize) -> Vec<Option<usize>> {
    pure::rolling_rank(&series, window)
}

/// 单均线多空信号
/// 返回每个点的多空信号（-1.0, 0.0, 1.0）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn single_sma_positions(series: Vec<f64>, n: usize) -> Vec<f64> {
    pure::single_sma_positions(&series, n)
}

/// 单指数移动平均多空信号
/// 返回每个点的多空信号（-1.0, 0.0, 1.0）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn single_ema_positions(series: Vec<f64>, n: usize) -> Vec<f64> {
    pure::single_ema_positions(&series, n)
}

/// 取窗口内的中间值作为中轴的多空信号
/// 返回每个点的多空信号（-1.0, 0.0, 1.0）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn mid_positions(series: Vec<f64>, n: usize) -> Vec<f64> {
    pure::mid_positions(&series, n)
}

/// 双均线多空信号
/// 返回每个点的多空信号（-1.0, 0.0, 1.0）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn double_sma_positions(series: Vec<f64>, n: usize, m: usize) -> Vec<f64> {
    pure::double_sma_positions(&series, n, m)
}

/// 三均线系统持仓信号
/// 返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn triple_sma_positions(series: Vec<f64>, m1: usize, m2: usize, m3: usize) -> Vec<i32> {
    pure::triple_sma_positions(&series, m1, m2, m3)
}

/// 布林线多空信号
/// 返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn boll_positions(series: Vec<f64>, n: usize, k: f64) -> Vec<i32> {
    pure::boll_positions(&series, n, k)
}

/// 布林带反转策略的多空持仓信号
/// 返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn boll_reverse_positions(series: Vec<f64>, n: usize, k: f64) -> Vec<i32> {
    pure::boll_reverse_positions(&series, n, k)
}

/// 均线的最大最小值归一化
/// 返回归一化后的值，范围在 [-1, 1] 之间
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn mms_positions(series: Vec<f64>, timeperiod: usize, window: usize) -> Vec<f64> {
    pure::mms_positions(&series, timeperiod, window)
}

/// RSI 反转策略的多空持仓信号
/// 返回每个点的持仓信号（-1: 空头, 0: 空仓, 1: 多头）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn rsi_reverse_positions(
    series: Vec<f64>,
    n: usize,
    rsi_upper: f64,
    rsi_lower: f64,
    rsi_exit: f64,
) -> Vec<i32> {
    pure::rsi_reverse_positions(&series, n, rsi_upper, rsi_lower, rsi_exit)
}

/// tanh 多空策略
/// 返回每个点的持仓信号（-1 到 1 之间的值）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn tanh_positions(series: Vec<f64>, n: usize) -> Vec<f64> {
    pure::tanh_positions(&series, n)
}

/// rank 多空策略
/// 返回每个点的持仓信号（-1 到 1 之间的值）
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn rank_positions(series: Vec<f64>, n: usize) -> Vec<f64> {
    pure::rank_positions(&series, n)
}

/// 计算指数移动平均 (EMA)
/// 返回每个点的 EMA 值
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn ema(series: Vec<f64>, period: usize) -> Vec<f64> {
    pure::ema(&series, period)
}

/// 计算真实波幅 (True Range)
/// 返回每个点的真实波幅值
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn true_range(high: Vec<f64>, low: Vec<f64>, close_prev: Vec<f64>) -> Vec<f64> {
    pure::true_range(&high, &low, &close_prev)
}

/// RSX-SS2 - 自适应平滑的RSI变体
/// 返回每个点的 RSX-SS2 值
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn rsx_ss2(close: Vec<f64>, period: usize, smooth_period: usize) -> Vec<f64> {
    pure::rsx_ss2(&close, period, smooth_period)
}

/// Jurik波动平滑器 - 低噪声波动指标
/// 返回平滑波动率值
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn jurik_volty(close: Vec<f64>, period: usize, power: f64) -> Vec<f64> {
    pure::jurik_volty(&close, period, power)
}

/// 终极通道 - 基于终极平滑器的通道指标
/// 返回 (中线, 上轨, 下轨)
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn ultimate_channel(
    high: Vec<f64>,
    low: Vec<f64>,
    close: Vec<f64>,
    period: usize,
    multiplier: f64,
) -> (Vec<f64>, Vec<f64>, Vec<f64>) {
    pure::ultimate_channel(&high, &low, &close, period, multiplier)
}

/// 终极带 - 基于终极平滑器的布林带变体
/// 返回 (中线, 上轨, 下轨)
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn ultimate_bands(
    close: Vec<f64>,
    period: usize,
    std_multiplier: f64,
    smooth_period: usize,
) -> (Vec<f64>, Vec<f64>, Vec<f64>) {
    pure::ultimate_bands(&close, period, std_multiplier, smooth_period)
}

/// 终极波动指标 (UOS) - 多周期融合振荡器
/// 返回 UOS 值
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn ultimate_oscillator(
    high: Vec<f64>,
    low: Vec<f64>,
    close: Vec<f64>,
    short_period: usize,
    med_period: usize,
    long_period: usize,
) -> Vec<f64> {
    pure::ultimate_oscillator(&high, &low, &close, short_period, med_period, long_period)
}

/// 指数平滑 - 基础时间序列平滑技术
/// 返回平滑后的序列
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn exponential_smoothing(series: Vec<f64>, alpha: f64) -> Vec<f64> {
    pure::exponential_smoothing(&series, alpha)
}

/// Holt-Winters三参数平滑 - 支持趋势和季节性的平滑方法
/// 返回平滑后的序列
#[pyfunction]
#[gen_stub_pyfunction]
pub(crate) fn holt_winters(
    series: Vec<f64>,
    season_length: usize,
    alpha: f64,
    beta: f64,
    gamma: f64,
) -> Vec<f64> {
    pure::holt_winters(&series, season_length, alpha, beta, gamma)
}

pub(crate) use mixed::chip_dist::chip_distribution_triangle;
