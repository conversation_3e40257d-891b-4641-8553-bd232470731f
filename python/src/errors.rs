use czsc::{
    core::utils::errors::CoreUtilsErorr, trader::weight_backtest::errors::WeightBackTestError,
    utils::errors::UtilsError,
};
use error_macros::CZSCErrorDerive;
use error_support::expand_error_chain;
use numpy::NotContiguousError;
use polars::error::PolarsError;
use pyo3::{create_exception, exceptions::PyException, PyErr};
use thiserror::Error;

create_exception!(_internal, CZSCError, PyException);

#[derive(Debug, Error, CZSCErrorDerive)]
pub enum PythonError {
    #[error("WeightBackTest: {0}")]
    WeightBackTest(#[from] WeightBackTestError),

    #[error("Utils: {0}")]
    Utils(#[from] UtilsError),

    #[error("Polars: {0}")]
    Polars(#[from] PolarsError),

    #[error("{}", expand_error_chain(.0))]
    Unexpected(anyhow::Error),

    #[error("CoreUtils: {0}")]
    CoreUtils(#[from] CoreUtilsErorr),

    #[error("Numpy: {0}")]
    NotContiguous(#[from] NotContiguousError),
}

impl From<PythonError> for PyErr {
    fn from(e: PythonError) -> Self {
        PyException::new_err(e.to_string())
    }
}
