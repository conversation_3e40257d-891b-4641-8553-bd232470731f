# 测试指南

本文档介绍如何使用 rs_czsc 项目中配置的 pytest 测试环境。

## 环境配置

### 安装测试依赖

```bash
# 进入 python 目录
cd python

# 安装开发依赖
pip install -r requirements-dev.txt
```

## 运行测试

### 基本命令

```bash
# 运行所有测试
pytest tests/
```

### 常用测试选项

```bash
# 运行快速测试（跳过slow标记的测试）
pytest tests/ -m "not slow"

# 运行所有测试包括慢速测试
pytest tests/ --runslow

# 运行特定测试文件
pytest tests/test_czsc.py -v

# 运行特定测试函数
pytest tests/test_czsc.py::test_czsc_creation -v

# 并行运行测试
pytest tests/ -n auto
```

### 覆盖率测试

```bash
# 运行测试并生成覆盖率报告
pytest tests/ --cov=rs_czsc --cov-report=html --cov-report=term-missing
```

### 性能测试

```bash
# 运行性能测试
pytest tests/ -m benchmark --benchmark-only
```

## 测试标记

项目中使用了以下测试标记：

- `@pytest.mark.slow` - 标记慢速测试
- `@pytest.mark.integration` - 标记集成测试
- `@pytest.mark.unit` - 标记单元测试
- `@pytest.mark.benchmark` - 标记性能测试

### 使用示例

```python
import pytest

@pytest.mark.slow
def test_large_dataset():
    """这是一个慢速测试"""
    pass

@pytest.mark.unit
def test_basic_function():
    """这是一个单元测试"""
    pass

@pytest.mark.benchmark
def test_performance(benchmark):
    """这是一个性能测试"""
    result = benchmark(some_function)
    assert result is not None
```

## 测试文件组织

```
tests/
├── __init__.py
├── conftest.py          # pytest 配置和公共 fixtures
├── test_czsc.py         # CZSC 核心功能测试
├── test_czsc_v1.py      # CZSC v1 版本测试
├── benchmark/           # 性能测试目录
└── data/               # 测试数据目录
```

## 常用 Fixtures

项目中提供了以下公共 fixtures：

- `sample_kline_data` - 示例K线数据（DataFrame格式）
- `raw_bars` - 示例K线数据（RawBar列表格式）
- `test_data_dir` - 测试数据目录路径
- `output_dir` - 临时输出目录

### 使用示例

```python
def test_with_sample_data(raw_bars):
    """使用示例数据进行测试"""
    assert len(raw_bars) == 100
    assert raw_bars[0].symbol == 'TEST.SZ'

def test_with_output_dir(output_dir):
    """使用临时输出目录"""
    output_file = output_dir / "test_output.txt"
    output_file.write_text("test content")
    assert output_file.exists()
```

## 编写测试的最佳实践

### 1. 测试函数命名

- 测试函数名称以 `test_` 开头
- 使用描述性的名称说明测试的功能
- 使用中文注释说明测试目的

```python
def test_czsc_creation():
    """测试CZSC对象创建功能"""
    pass

def test_bi_list_generation():
    """测试笔列表生成功能"""
    pass
```

### 2. 测试组织

- 每个模块对应一个测试文件
- 相关的测试放在同一个测试类中
- 使用 fixtures 共享测试数据

```python
class TestCZSC:
    """CZSC 核心功能测试类"""
    
    def test_creation(self, raw_bars):
        """测试对象创建"""
        pass
    
    def test_update(self, raw_bars):
        """测试数据更新"""
        pass
```

### 3. 断言编写

- 使用清晰的断言消息
- 测试预期的行为和边界条件
- 使用 pytest 的高级断言功能

```python
def test_bi_properties(raw_bars):
    """测试笔的属性"""
    czsc = CZSC(raw_bars)
    bi_list = czsc.bi_list
    
    assert len(bi_list) > 0, "应该生成至少一个笔"
    
    for bi in bi_list:
        assert bi.high > bi.low, f"笔的高点应该大于低点: {bi}"
        assert bi.sdt <= bi.edt, f"笔的开始时间应该早于结束时间: {bi}"
```

## 调试测试

### 使用 pytest 调试功能

```bash
# 在第一个失败处停止
pytest tests/ -x

# 显示详细输出
pytest tests/ -v -s

# 显示最慢的10个测试
pytest tests/ --durations=10

# 使用 pdb 调试
pytest tests/ --pdb
```

### 使用 IDE 调试

大多数现代 IDE（如 PyCharm、VSCode）都支持 pytest 集成，可以直接在 IDE 中运行和调试测试。

## 持续集成

项目配置了 GitHub Actions 来自动运行测试，每次推送代码或创建 Pull Request 时都会自动触发测试。

测试报告和覆盖率信息会自动上传到相应的服务中。

## 故障排除

### 常见问题

1. **导入错误**: 确保已正确安装项目依赖
2. **测试数据问题**: 检查测试数据文件是否存在
3. **权限问题**: 确保有写入临时目录的权限

### 清理缓存

```bash
# 手动清理缓存文件
rm -rf .pytest_cache __pycache__ .coverage htmlcov
``` 