#!/usr/bin/env python3
"""
调试枚举对象内部结构
"""

import rs_czsc
import pandas as pd
import numpy as np

# 创建测试数据
np.random.seed(42)
dates = pd.date_range(start='2024-01-01', periods=50, freq='D')

base_price = 50.0
trend = np.linspace(0, 5, 50)
cycle = 2 * np.sin(np.linspace(0, 4*np.pi, 50))
noise = np.cumsum(np.random.normal(0, 0.5, 50))
close_prices = base_price + trend + cycle + noise

opens = []
highs = []
lows = []

for i, close in enumerate(close_prices):
    if i == 0:
        open_price = close + np.random.normal(0, 0.2)
    else:
        open_price = close_prices[i-1] + np.random.normal(0, 0.1)
    
    high = max(open_price, close) + abs(np.random.normal(0, 0.3))
    low = min(open_price, close) - abs(np.random.normal(0, 0.3))
    
    opens.append(open_price)
    highs.append(high)
    lows.append(low)

df = pd.DataFrame({
    'dt': dates,
    'symbol': ['000001.SZ'] * 50,
    'open': opens,
    'close': close_prices,
    'high': highs,
    'low': lows,
    'vol': np.random.randint(100000, 1000000, 50),
    'amount': np.random.randint(1000000, 10000000, 50)
})

# 格式化为 K线数据
bars = rs_czsc.format_standard_kline(df, freq=rs_czsc.Freq.D)
rs_obj = rs_czsc.CZSC(bars, max_bi_num=50)

print("调试枚举对象:")
print("="*50)

if len(rs_obj.bi_list) > 0:
    bi = rs_obj.bi_list[0]
    print(f"BI.direction 原始字符串: {object.__str__(bi.direction)}")
    print(f"BI.direction type: {type(bi.direction)}")
    print(f"BI.direction id: {id(bi.direction)}")
    print(f"BI.direction vars: {vars(bi.direction) if hasattr(bi.direction, '__dict__') else 'No __dict__'}")

if len(rs_obj.get_fx_list()) > 0:
    fx = rs_obj.get_fx_list()[0] 
    print(f"\nFX.mark 原始字符串: {object.__str__(fx.mark)}")
    print(f"FX.mark type: {type(fx.mark)}")
    print(f"FX.mark id: {id(fx.mark)}")
    print(f"FX.mark vars: {vars(fx.mark) if hasattr(fx.mark, '__dict__') else 'No __dict__'}")

freq = rs_obj.freq
print(f"\nFreq 原始字符串: {object.__str__(freq)}")
print(f"Freq type: {type(freq)}")
print(f"Freq id: {id(freq)}")
print(f"Freq vars: {vars(freq) if hasattr(freq, '__dict__') else 'No __dict__'}")

# 列出前几笔的方向
if len(rs_obj.bi_list) >= 3:
    for i, bi in enumerate(rs_obj.bi_list[:3]):
        print(f"\n笔{i+1}: {object.__str__(bi.direction)} -> {str(bi.direction)}")