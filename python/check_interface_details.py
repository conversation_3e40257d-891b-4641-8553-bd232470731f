#!/usr/bin/env python3
"""
专门检查关键接口细节
"""

import pandas as pd
import numpy as np
import czsc
import rs_czsc
from datetime import datetime

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
    
    base_price = 50.0
    trend = np.linspace(0, 5, 50)
    cycle = 2 * np.sin(np.linspace(0, 4*np.pi, 50))
    noise = np.cumsum(np.random.normal(0, 0.5, 50))
    close_prices = base_price + trend + cycle + noise
    
    opens = []
    highs = []
    lows = []
    
    for i, close in enumerate(close_prices):
        if i == 0:
            open_price = close + np.random.normal(0, 0.2)
        else:
            open_price = close_prices[i-1] + np.random.normal(0, 0.1)
        
        high = max(open_price, close) + abs(np.random.normal(0, 0.3))
        low = min(open_price, close) - abs(np.random.normal(0, 0.3))
        
        opens.append(open_price)
        highs.append(high)
        lows.append(low)
    
    df = pd.DataFrame({
        'dt': dates,
        'symbol': ['000001.SZ'] * 50,
        'open': opens,
        'close': close_prices,
        'high': highs,
        'low': lows,
        'vol': np.random.randint(100000, 1000000, 50),
        'amount': np.random.randint(1000000, 10000000, 50)
    })
    
    return df

def compare_interfaces():
    """对比接口细节"""
    print("="*60)
    print("接口对比分析")
    print("="*60)
    
    df = create_test_data()
    
    # 创建两个对象
    czsc_bars = czsc.format_standard_kline(df, freq="日线")
    czsc_obj = czsc.CZSC(czsc_bars, max_bi_num=50)
    
    rs_bars = rs_czsc.format_standard_kline(df, freq=rs_czsc.Freq.D)
    rs_obj = rs_czsc.CZSC(rs_bars, max_bi_num=50)
    
    print(f"czsc对象: {czsc_obj}")
    print(f"rs_czsc对象: {rs_obj}")
    
    # 1. 检查时间戳返回类型
    print(f"\n🕐 时间戳返回类型对比:")
    if len(czsc_obj.bi_list) > 0 and len(rs_obj.bi_list) > 0:
        czsc_bi = czsc_obj.bi_list[0]
        rs_bi = rs_obj.bi_list[0]
        
        print(f"  czsc BI.sdt: {czsc_bi.sdt} (type: {type(czsc_bi.sdt)})")
        print(f"  rs_czsc BI.sdt: {rs_bi.sdt} (type: {type(rs_bi.sdt)})")
        print(f"  czsc BI.edt: {czsc_bi.edt} (type: {type(czsc_bi.edt)})")
        print(f"  rs_czsc BI.edt: {rs_bi.edt} (type: {type(rs_bi.edt)})")
    
    if len(czsc_obj.fx_list) > 0 and len(rs_obj.fx_list) > 0:
        czsc_fx = czsc_obj.fx_list[0]
        rs_fx = rs_obj.fx_list[0]
        
        print(f"  czsc FX.dt: {czsc_fx.dt} (type: {type(czsc_fx.dt)})")
        print(f"  rs_czsc FX.dt: {rs_fx.dt} (type: {type(rs_fx.dt)})")
    
    # 2. 检查枚举返回类型
    print(f"\n🏷️  枚举返回类型对比:")
    if len(czsc_obj.bi_list) > 0 and len(rs_obj.bi_list) > 0:
        czsc_bi = czsc_obj.bi_list[0]
        rs_bi = rs_obj.bi_list[0]
        
        print(f"  czsc BI.direction: {czsc_bi.direction} (type: {type(czsc_bi.direction)}) - str: '{str(czsc_bi.direction)}'")
        print(f"  rs_czsc BI.direction: {rs_bi.direction} (type: {type(rs_bi.direction)}) - str: '{str(rs_bi.direction)}'")
    
    if len(czsc_obj.fx_list) > 0 and len(rs_obj.fx_list) > 0:
        czsc_fx = czsc_obj.fx_list[0]
        rs_fx = rs_obj.fx_list[0]
        
        print(f"  czsc FX.mark: {czsc_fx.mark} (type: {type(czsc_fx.mark)}) - str: '{str(czsc_fx.mark)}'")
        print(f"  rs_czsc FX.mark: {rs_fx.mark} (type: {type(rs_fx.mark)}) - str: '{str(rs_fx.mark)}'")
    
    print(f"  czsc freq: {czsc_obj.freq} (type: {type(czsc_obj.freq)}) - str: '{str(czsc_obj.freq)}'")
    print(f"  rs_czsc freq: {rs_obj.freq} (type: {type(rs_obj.freq)}) - str: '{str(rs_obj.freq)}'")
    
    # 3. 检查属性访问
    print(f"\n📋 属性访问对比:")
    
    # 检查 fx_list 属性
    try:
        czsc_fx_attr = czsc_obj.fx_list
        print(f"  czsc.fx_list (属性): 成功, 类型: {type(czsc_fx_attr)}, 长度: {len(czsc_fx_attr)}")
    except Exception as e:
        print(f"  czsc.fx_list (属性): 失败 - {e}")
    
    try:
        rs_fx_attr = rs_obj.fx_list
        print(f"  rs_czsc.fx_list (属性): 成功, 类型: {type(rs_fx_attr)}, 长度: {len(rs_fx_attr)}")
    except Exception as e:
        print(f"  rs_czsc.fx_list (属性): 失败 - {e}")
    
    # get_fx_list 方法已被 fx_list 属性替代
    
    # 4. 检查其他重要属性
    print(f"\n🔍 其他属性对比:")
    
    attrs_to_check = ['symbol', 'max_bi_num', 'verbose', 'last_bi_extend', 'cache', 'signals']
    
    for attr in attrs_to_check:
        try:
            czsc_val = getattr(czsc_obj, attr)
            print(f"  czsc.{attr}: {type(czsc_val).__name__} - {repr(czsc_val)}")
        except Exception as e:
            print(f"  czsc.{attr}: ERROR - {e}")
        
        try:
            rs_val = getattr(rs_obj, attr)
            print(f"  rs_czsc.{attr}: {type(rs_val).__name__} - {repr(rs_val)}")
        except Exception as e:
            print(f"  rs_czsc.{attr}: ERROR - {e}")
        print()

def main():
    compare_interfaces()

if __name__ == "__main__":
    main()