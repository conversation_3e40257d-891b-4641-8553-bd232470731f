[build-system]
requires = ["maturin>=1.7,<2.0"]
build-backend = "maturin"

[project]
name = "rs_czsc"
requires-python = ">=3.9"
description = "A Rust and Python integration project for CZSC"
readme = "README.md"
keywords = ["rust", "python", "czsc", "quant"]
license = { text = "MIT" }
authors = [
    { name = "schwartx", email = "<EMAIL>" },
    { name = "J<PERSON>", email = "<EMAIL>" },
    { name = "zengbin93", email = "<EMAIL>" },
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Rust",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

dynamic = ["version"]
dependencies = [
    "czsc>=0.9.68",
    "pandas",
    "pyarrow>=18",
]

[tool.maturin]
features = ["pyo3/extension-module"]
module-name = "rs_czsc._rs_czsc"

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-benchmark>=5.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "mypy>=1.0.0",
]

[tool.pytest.ini_options]
minversion = "8.0"
addopts = [
    "-ra",
    "-q",
    "--strict-markers",
    "--strict-config",
    "--cov=rs_czsc",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "benchmark: marks tests as benchmark tests",
]

[tool.coverage.run]
source = ["rs_czsc"]
omit = ["*/tests/*", "*/test_*", "*/__pycache__/*", "*/.*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[dependency-groups]
dev = [
    "ipython>=8.18.1",
    "maturin>=1.9.2",
    "polars>=1.31.0",
    "pytest",
]
