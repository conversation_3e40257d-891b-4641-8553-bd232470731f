#!/usr/bin/env python3
"""
测试枚举字符串表示
"""

import rs_czsc
import pandas as pd
import numpy as np

# 创建测试数据
np.random.seed(42)
dates = pd.date_range(start='2024-01-01', periods=50, freq='D')

base_price = 50.0
trend = np.linspace(0, 5, 50)
cycle = 2 * np.sin(np.linspace(0, 4*np.pi, 50))
noise = np.cumsum(np.random.normal(0, 0.5, 50))
close_prices = base_price + trend + cycle + noise

opens = []
highs = []
lows = []

for i, close in enumerate(close_prices):
    if i == 0:
        open_price = close + np.random.normal(0, 0.2)
    else:
        open_price = close_prices[i-1] + np.random.normal(0, 0.1)
    
    high = max(open_price, close) + abs(np.random.normal(0, 0.3))
    low = min(open_price, close) - abs(np.random.normal(0, 0.3))
    
    opens.append(open_price)
    highs.append(high)
    lows.append(low)

df = pd.DataFrame({
    'dt': dates,
    'symbol': ['000001.SZ'] * 50,
    'open': opens,
    'close': close_prices,
    'high': highs,
    'low': lows,
    'vol': np.random.randint(100000, 1000000, 50),
    'amount': np.random.randint(1000000, 10000000, 50)
})

# 格式化为 K线数据
bars = rs_czsc.format_standard_kline(df, freq=rs_czsc.Freq.D)
rs_obj = rs_czsc.CZSC(bars, max_bi_num=50)

print("测试枚举字符串表示:")
print("="*50)

if len(rs_obj.bi_list) > 0:
    bi = rs_obj.bi_list[0]
    print(f"BI.direction 对象: {repr(bi.direction)}")
    print(f"BI.direction.__str__(): {bi.direction.__str__()}")
    print(f"str(BI.direction): {str(bi.direction)}")
    print(f"type(BI.direction): {type(bi.direction)}")

if len(rs_obj.get_fx_list()) > 0:
    fx = rs_obj.get_fx_list()[0]
    print(f"\nFX.mark 对象: {repr(fx.mark)}")
    print(f"FX.mark.__str__(): {fx.mark.__str__()}")
    print(f"str(FX.mark): {str(fx.mark)}")
    print(f"type(FX.mark): {type(fx.mark)}")

freq = rs_obj.freq
print(f"\nFreq 对象: {repr(freq)}")
print(f"Freq.__str__(): {freq.__str__()}")
print(f"str(Freq): {str(freq)}")
print(f"type(Freq): {type(freq)}")