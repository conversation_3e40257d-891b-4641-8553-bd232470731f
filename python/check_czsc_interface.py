#!/usr/bin/env python3
"""
检查 czsc 库的接口，确保完全对齐
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

try:
    import czsc
    print("✓ 导入 czsc 成功")
except ImportError as e:
    print(f"✗ 导入 czsc 失败: {e}")
    sys.exit(1)

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    
    base_price = 50.0
    trend = np.linspace(0, 10, 100)
    cycle = 3 * np.sin(np.linspace(0, 4*np.pi, 100))
    noise = np.cumsum(np.random.normal(0, 0.8, 100))
    close_prices = base_price + trend + cycle + noise
    
    opens = []
    highs = []
    lows = []
    
    for i, close in enumerate(close_prices):
        if i == 0:
            open_price = close + np.random.normal(0, 0.3)
        else:
            open_price = close_prices[i-1] + np.random.normal(0, 0.2)
        
        high = max(open_price, close) + abs(np.random.normal(0, 0.5))
        low = min(open_price, close) - abs(np.random.normal(0, 0.5))
        
        opens.append(open_price)
        highs.append(high)
        lows.append(low)
    
    df = pd.DataFrame({
        'dt': dates,
        'symbol': ['000001.SZ'] * 100,
        'open': opens,
        'close': close_prices,
        'high': highs,
        'low': lows,
        'vol': np.random.randint(100000, 2000000, 100),
        'amount': np.random.randint(1000000, 20000000, 100)
    })
    
    return df

def check_czsc_interface():
    """检查 czsc 库的接口"""
    print("\n" + "="*60)
    print("检查 czsc 库的接口")
    print("="*60)
    
    # 创建测试数据
    df = create_test_data()
    bars = czsc.format_standard_kline(df, freq="日线")
    c = czsc.CZSC(bars, max_bi_num=50)
    
    print(f"CZSC对象: {c}")
    print(f"类型: {type(c)}")
    
    # 检查CZSC对象的所有属性和方法
    print(f"\n🔍 CZSC对象属性和方法:")
    attrs = [attr for attr in dir(c) if not attr.startswith('_')]
    for attr in sorted(attrs):
        try:
            val = getattr(c, attr)
            if callable(val):
                print(f"  {attr}(): 方法 - {type(val)}")
            else:
                print(f"  {attr}: {type(val).__name__} - {repr(val) if not isinstance(val, list) else f'list[{len(val)}]'}")
        except Exception as e:
            print(f"  {attr}: ERROR - {e}")
    
    # 重点检查主要属性
    print(f"\n📊 主要属性详情:")
    
    # symbol
    print(f"symbol: {repr(c.symbol)} (type: {type(c.symbol)})")
    
    # freq  
    print(f"freq: {repr(c.freq)} (type: {type(c.freq)})")
    
    # bi_list
    print(f"bi_list: 长度={len(c.bi_list)} (type: {type(c.bi_list)})")
    if len(c.bi_list) > 0:
        bi = c.bi_list[0]
        print(f"  第一笔: {bi}")
        print(f"  笔类型: {type(bi)}")
        
        # 检查BI对象的属性
        print(f"  📈 BI对象属性:")
        bi_attrs = [attr for attr in dir(bi) if not attr.startswith('_')]
        for attr in sorted(bi_attrs):
            try:
                val = getattr(bi, attr)
                if callable(val):
                    continue  # 跳过方法
                print(f"    {attr}: {type(val).__name__} - {repr(val)}")
            except Exception as e:
                print(f"    {attr}: ERROR - {e}")
    
    # fx_list
    print(f"fx_list: 长度={len(c.fx_list)} (type: {type(c.fx_list)})")
    if len(c.fx_list) > 0:
        fx = c.fx_list[0]
        print(f"  第一分型: {fx}")
        print(f"  分型类型: {type(fx)}")
        
        # 检查FX对象的属性
        print(f"  🎯 FX对象属性:")
        fx_attrs = [attr for attr in dir(fx) if not attr.startswith('_')]
        for attr in sorted(fx_attrs):
            try:
                val = getattr(fx, attr)
                if callable(val):
                    continue  # 跳过方法
                print(f"    {attr}: {type(val).__name__} - {repr(val)}")
            except Exception as e:
                print(f"    {attr}: ERROR - {e}")
    
    # bars_raw
    print(f"bars_raw: 长度={len(c.bars_raw)} (type: {type(c.bars_raw)})")
    if len(c.bars_raw) > 0:
        bar = c.bars_raw[0]
        print(f"  第一根K线: {bar}")
        print(f"  K线类型: {type(bar)}")
        
        # 检查RawBar对象的属性
        print(f"  📊 RawBar对象属性:")
        bar_attrs = [attr for attr in dir(bar) if not attr.startswith('_')]
        for attr in sorted(bar_attrs):
            try:
                val = getattr(bar, attr)
                if callable(val):
                    continue  # 跳过方法
                print(f"    {attr}: {type(val).__name__} - {repr(val)}")
            except Exception as e:
                print(f"    {attr}: ERROR - {e}")
    
    return c

def check_return_types(c):
    """检查具体的返回类型"""
    print(f"\n🔬 详细返回类型检查:")
    
    # 检查时间相关返回值
    if len(c.bi_list) > 0:
        bi = c.bi_list[0]
        print(f"BI.sdt: {bi.sdt} (type: {type(bi.sdt)})")
        print(f"BI.edt: {bi.edt} (type: {type(bi.edt)})")
    
    if len(c.fx_list) > 0:
        fx = c.fx_list[0]
        print(f"FX.dt: {fx.dt} (type: {type(fx.dt)})")
    
    if len(c.bars_raw) > 0:
        bar = c.bars_raw[0]
        print(f"RawBar.dt: {bar.dt} (type: {type(bar.dt)})")
    
    # 检查枚举类型
    if len(c.bi_list) > 0:
        bi = c.bi_list[0]
        print(f"BI.direction: {bi.direction} (type: {type(bi.direction)})")
    
    if len(c.fx_list) > 0:
        fx = c.fx_list[0]
        print(f"FX.mark: {fx.mark} (type: {type(fx.mark)})")
    
    if len(c.bars_raw) > 0:
        bar = c.bars_raw[0]
        print(f"RawBar.freq: {bar.freq} (type: {type(bar.freq)})")

def main():
    """主函数"""
    print("开始检查 czsc 库接口...")
    
    c = check_czsc_interface()
    check_return_types(c)
    
    print(f"\n{'='*60}")
    print("检查完成！")

if __name__ == "__main__":
    main()