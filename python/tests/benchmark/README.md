# 基准性能测试指南

本目录包含了 rs_czsc 项目的性能基准测试，用于测量和比较 Rust 版本与 Python 版本函数的执行性能。

## 概述

性能测试使用 `pytest-benchmark` 插件来自动化测量函数执行时间、内存使用等性能指标。通过建立性能基准线，我们可以：

- 对比 Rust 版本和 Python 版本的性能差异
- 确保代码优化后性能没有退化
- 为持续集成提供性能监控
- 建立性能基准数据供后续比较

## 环境准备

确保已安装必要依赖：

```bash
# 安装开发依赖（包含 pytest-benchmark）
uv sync --extra dev
```

## 基准测试编写

### 基本结构

```python
import pytest
from rs_czsc._rs_czsc import rust_function
from rs_czsc._utils.utils import python_function


def test_function_performance(benchmark, test_data_fixture):
    """测试函数性能"""
    # benchmark 会自动多次执行并收集性能数据
    result = benchmark(rust_function, test_data_fixture)
    # 可以添加结果验证
    assert result is not None
```

### 关键组件说明

#### `benchmark` 参数

`benchmark` 是由 `pytest-benchmark` 插件自动注入的特殊 fixture，具有以下功能：

1. **自动重复执行**：多次运行被测函数，收集统计数据
2. **性能数据收集**：测量执行时间、内存使用等指标
3. **统计分析**：计算平均值、标准差、最小/最大值等
4. **结果报告**：生成详细的性能报告

#### 使用方式

```python
# 基本用法：测试单个函数
benchmark(function_to_test, *args, **kwargs)

# 高级用法：使用 pedantic 模式（更精确但更慢）
benchmark.pedantic(function_to_test, args=(arg1, arg2), rounds=10, iterations=100)
```

#### pytest 参数传递机制

在测试函数中：

```python
def test_daily_performance(benchmark, random_list_float):
    benchmark(daily_performance, random_list_float)
```

pytest 会自动解析函数签名并注入相应的 fixtures：

1. **`benchmark`**: 由 `pytest-benchmark` 插件提供
2. **`random_list_float`**: 在 `conftest.py` 中定义的测试数据 fixture

### 数据 Fixtures

#### 现有 Fixtures

- **`random_list_float`**: 包含 750 个随机浮点数的列表（模拟250天的交易数据）

#### 创建自定义 Fixtures

在 `conftest.py` 中添加新的测试数据：

```python
@pytest.fixture(scope="session")
def large_price_data():
    """大规模价格数据用于性能测试"""
    import random
    return [100 + random.gauss(0, 5) for _ in range(10000)]

@pytest.fixture(scope="session")
def sample_kline_bars():
    """样本K线数据"""
    # 返回 RawBar 对象列表
    pass
```

### 测试示例

#### 基本性能测试

```python
def test_rust_vs_python_performance(benchmark, random_list_float):
    """比较 Rust 和 Python 版本性能"""
    # 测试 Rust 版本
    rust_result = benchmark(rust_daily_performance, random_list_float)
    
    # 验证结果正确性
    assert isinstance(rust_result, dict)
    assert "年化收益率" in rust_result
```

#### 对比测试

```python
class TestPerformanceComparison:
    """性能对比测试类"""
    
    def test_rust_version(self, benchmark, test_data):
        result = benchmark(rust_function, test_data)
        return result
    
    def test_python_version(self, benchmark, test_data):
        result = benchmark(python_function, test_data)
        return result
```

#### 参数化性能测试

```python
@pytest.mark.parametrize("data_size", [100, 1000, 10000])
def test_scalability(benchmark, data_size):
    """测试不同数据规模下的性能"""
    test_data = [random.random() for _ in range(data_size)]
    result = benchmark(target_function, test_data)
    assert result is not None
```

## 运行性能测试

### 基本命令

```bash
# 运行所有性能测试
pytest tests/benchmark/

# 只运行性能测试，跳过常规测试
pytest tests/benchmark/ --benchmark-only

# 运行特定的性能测试文件
pytest tests/benchmark/test_daily_performance.py --benchmark-only
```

### 高级选项

```bash
# 保存性能基准数据
pytest tests/benchmark/ --benchmark-save=baseline_v1.0

# 与之前的基准比较
pytest tests/benchmark/ --benchmark-compare=baseline_v1.0

# 生成性能报告
pytest tests/benchmark/ --benchmark-json=performance_report.json

# 设置更严格的测试参数
pytest tests/benchmark/ --benchmark-min-rounds=10
```

### 输出解析

性能测试报告包含以下指标：

- **Name**: 测试名称
- **Min**: 最小执行时间
- **Max**: 最大执行时间
- **Mean**: 平均执行时间
- **StdDev**: 标准差
- **Median**: 中位数执行时间
- **IQR**: 四分位距
- **Outliers**: 异常值统计
- **Rounds**: 执行轮数
- **Iterations**: 每轮迭代次数

## 最佳实践

### 1. 测试数据设计

- 使用具有代表性的数据规模
- 包含边界情况（空数据、单点数据等）
- 使用 `scope="session"` 的 fixtures 减少数据生成开销

### 2. 性能测试组织

- 按功能模块组织测试文件
- 使用描述性的测试函数名
- 添加适当的文档字符串

### 3. 基准管理

- 定期保存性能基准数据
- 在重大更新前后进行性能对比
- 设置性能回归的警告阈值

### 4. CI/CD 集成

```yaml
# GitHub Actions 示例
- name: Run Performance Tests
  run: |
    pytest tests/benchmark/ --benchmark-only --benchmark-json=perf.json
    
- name: Compare with Baseline
  run: |
    pytest tests/benchmark/ --benchmark-compare=baseline --benchmark-compare-fail=min:5%
```

## 注意事项

1. **环境一致性**: 确保测试环境的一致性，避免系统负载影响结果
2. **数据预热**: 考虑 JIT 编译和缓存预热对性能的影响
3. **统计意义**: 确保足够的测试轮数以获得统计学意义上的结果
4. **内存考虑**: 大数据量测试时注意内存使用情况

## 故障排除

### 常见问题

1. **Import 错误**: 确保 Rust 扩展已正确编译安装
2. **Fixture 未找到**: 检查 `conftest.py` 中的 fixture 定义
3. **性能异常**: 检查系统资源使用情况和后台进程

### 调试技巧

```python
# 使用 pedantic 模式进行更精确的测量
def test_precise_measurement(benchmark):
    result = benchmark.pedantic(
        target_function,
        args=(test_data,),
        rounds=5,
        iterations=10,
        warmup_rounds=2
    )
```

## 贡献指南

添加新的性能测试时，请：

1. 在相应的模块目录下创建测试文件
2. 使用 `@pytest.mark.benchmark` 标记性能测试
3. 提供清晰的测试文档和预期性能指标
4. 包含必要的数据 fixtures
5. 更新本 README 文档
