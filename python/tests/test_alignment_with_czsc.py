"""
完整对齐测试：验证 rs_czsc 与 czsc 库的完全一致性
"""
import sys
import os
import pandas as pd
import pytest
from datetime import datetime
# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
try:
    from rs_czsc import RawBar, NewBar, FX, BI, CZSC, Freq, Direction, Mark, format_standard_kline
    RS_CZSC_AVAILABLE = True
except ImportError:
    RS_CZSC_AVAILABLE = False

try:
    import czsc
    from czsc import format_standard_kline as czsc_format_kline
    CZSC_AVAILABLE = True
except ImportError:
    CZSC_AVAILABLE = False

def load_test_data():
    """加载测试数据"""
    try:
        df = pd.read_feather("python/tests/k_line.feather")
    except:
        # 如果没有测试数据，创建模拟数据
        dates = pd.date_range(start='2025-01-01', periods=100, freq='D')
        df = pd.DataFrame({
            'dt': dates,
            'symbol': ['TEST.SZ'] * 100,
            'open': 10.0 + pd.Series(range(100)) * 0.1,
            'close': 10.1 + pd.Series(range(100)) * 0.1,
            'high': 10.2 + pd.Series(range(100)) * 0.1,
            'low': 9.9 + pd.Series(range(100)) * 0.1,
            'vol': [1000000] * 100,
            'amount': [10000000] * 100
        })
    return df


@pytest.mark.skipif(not (RS_CZSC_AVAILABLE and CZSC_AVAILABLE), 
                   reason="需要同时安装 rs_czsc 和 czsc 库")
class TestAlignmentWithCZSC:
    """测试与 czsc 库的完全对齐"""
    
    def setup_method(self):
        """设置测试数据"""
        self.df = load_test_data()
        
        # 准备 rs_czsc 数据
        self.rs_bars = format_standard_kline(self.df.copy(), freq=Freq.D)
        self.rs_czsc = CZSC(self.rs_bars, max_bi_num=50)
        
        # 准备 czsc 数据  
        self.czsc_bars = czsc_format_kline(self.df.copy(), freq="日线")
        self.czsc_obj = czsc.CZSC(self.czsc_bars, max_bi_num=50)

    def test_rawbar_alignment(self):
        """测试 RawBar 对象对齐"""
        if len(self.rs_bars) == 0 or len(self.czsc_bars) == 0:
            pytest.skip("测试数据为空")
            
        rs_bar = self.rs_bars[0]
        czsc_bar = self.czsc_bars[0]
        
        # 基本属性对齐
        assert rs_bar.symbol == czsc_bar.symbol, f"symbol 不一致: {rs_bar.symbol} vs {czsc_bar.symbol}"
        assert abs(rs_bar.open - czsc_bar.open) < 1e-6, f"open 不一致: {rs_bar.open} vs {czsc_bar.open}"
        assert abs(rs_bar.close - czsc_bar.close) < 1e-6, f"close 不一致: {rs_bar.close} vs {czsc_bar.close}"
        assert abs(rs_bar.high - czsc_bar.high) < 1e-6, f"high 不一致: {rs_bar.high} vs {czsc_bar.high}"
        assert abs(rs_bar.low - czsc_bar.low) < 1e-6, f"low 不一致: {rs_bar.low} vs {czsc_bar.low}"
        assert abs(rs_bar.vol - czsc_bar.vol) < 1e-6, f"vol 不一致: {rs_bar.vol} vs {czsc_bar.vol}"
        
        # 计算属性对齐
        assert abs(rs_bar.upper - czsc_bar.upper) < 1e-6, f"upper 不一致: {rs_bar.upper} vs {czsc_bar.upper}"
        assert abs(rs_bar.lower - czsc_bar.lower) < 1e-6, f"lower 不一致: {rs_bar.lower} vs {czsc_bar.lower}"
        assert abs(rs_bar.solid - czsc_bar.solid) < 1e-6, f"solid 不一致: {rs_bar.solid} vs {czsc_bar.solid}"

    def test_czsc_object_alignment(self):
        """测试 CZSC 对象对齐"""
        # 基本属性
        assert self.rs_czsc.symbol == self.czsc_obj.symbol, f"symbol 不一致: {self.rs_czsc.symbol} vs {self.czsc_obj.symbol}"
        assert self.rs_czsc.max_bi_num == self.czsc_obj.max_bi_num, f"max_bi_num 不一致"
        
        # 笔列表数量
        rs_bi_count = len(self.rs_czsc.bi_list)
        czsc_bi_count = len(self.czsc_obj.bi_list)
        assert rs_bi_count == czsc_bi_count, f"笔数量不一致: {rs_bi_count} vs {czsc_bi_count}"
        
        # 分型列表数量
        rs_fx_count = len(self.rs_czsc.fx_list)
        czsc_fx_count = len(self.czsc_obj.fx_list)
        assert rs_fx_count == czsc_fx_count, f"分型数量不一致: {rs_fx_count} vs {czsc_fx_count}"

    def test_bi_alignment(self):
        """测试笔(BI)对象对齐"""
        if len(self.rs_czsc.bi_list) == 0 or len(self.czsc_obj.bi_list) == 0:
            pytest.skip("没有笔数据进行测试")
            
        # 逐个比较每一笔
        for i, (rs_bi, czsc_bi) in enumerate(zip(self.rs_czsc.bi_list, self.czsc_obj.bi_list)):
            # 基本属性
            assert rs_bi.symbol == czsc_bi.symbol, f"笔{i} symbol 不一致"
            
            # 时间对齐 (转换为时间戳进行比较)
            assert abs(rs_bi.sdt - czsc_bi.sdt) < 1, f"笔{i} 开始时间不一致"
            assert abs(rs_bi.edt - czsc_bi.edt) < 1, f"笔{i} 结束时间不一致"
            
            # 方向对齐
            rs_direction_str = "向上" if rs_bi.direction == Direction.Up else "向下"
            czsc_direction_str = str(czsc_bi.direction)
            assert rs_direction_str == czsc_direction_str, f"笔{i} 方向不一致: {rs_direction_str} vs {czsc_direction_str}"
            
            # 高低点对齐
            assert abs(rs_bi.high - czsc_bi.high) < 1e-6, f"笔{i} 高点不一致: {rs_bi.high} vs {czsc_bi.high}"
            assert abs(rs_bi.low - czsc_bi.low) < 1e-6, f"笔{i} 低点不一致: {rs_bi.low} vs {czsc_bi.low}"
            
            # 力度指标对齐
            assert abs(rs_bi.power - czsc_bi.power) < 1e-6, f"笔{i} 力度不一致: {rs_bi.power} vs {czsc_bi.power}"
            assert abs(rs_bi.power_volume - czsc_bi.power_volume) < 1e-6, f"笔{i} 成交量力度不一致"
            
            # 技术指标对齐
            assert abs(rs_bi.change - czsc_bi.change) < 1e-6, f"笔{i} 涨跌幅不一致: {rs_bi.change} vs {czsc_bi.change}"
            assert abs(rs_bi.SNR - czsc_bi.SNR) < 1e-4, f"笔{i} 信噪比不一致: {rs_bi.SNR} vs {czsc_bi.SNR}"
            assert abs(rs_bi.slope - czsc_bi.slope) < 1e-6, f"笔{i} 斜率不一致: {rs_bi.slope} vs {czsc_bi.slope}"
            
            # 长度对齐
            assert rs_bi.length == czsc_bi.length, f"笔{i} 长度不一致: {rs_bi.length} vs {czsc_bi.length}"

    def test_fx_alignment(self):
        """测试分型(FX)对象对齐"""
        if len(self.rs_czsc.fx_list) == 0 or len(self.czsc_obj.fx_list) == 0:
            pytest.skip("没有分型数据进行测试")
            
        # 逐个比较每个分型
        for i, (rs_fx, czsc_fx) in enumerate(zip(self.rs_czsc.fx_list, self.czsc_obj.fx_list)):
            # 基本属性
            assert rs_fx.symbol == czsc_fx.symbol, f"分型{i} symbol 不一致"
            
            # 时间对齐
            assert abs(rs_fx.dt - czsc_fx.dt) < 1, f"分型{i} 时间不一致"
            
            # 标记对齐
            rs_mark_str = "顶分型" if rs_fx.mark == Mark.G else "底分型"
            czsc_mark_str = str(czsc_fx.mark)
            assert rs_mark_str == czsc_mark_str, f"分型{i} 标记不一致: {rs_mark_str} vs {czsc_mark_str}"
            
            # 高低点和分型值对齐
            assert abs(rs_fx.high - czsc_fx.high) < 1e-6, f"分型{i} 高点不一致: {rs_fx.high} vs {czsc_fx.high}"
            assert abs(rs_fx.low - czsc_fx.low) < 1e-6, f"分型{i} 低点不一致: {rs_fx.low} vs {czsc_fx.low}"
            assert abs(rs_fx.fx - czsc_fx.fx) < 1e-6, f"分型{i} 分型值不一致: {rs_fx.fx} vs {czsc_fx.fx}"
            
            # 分型力度指标对齐
            assert rs_fx.power_str == czsc_fx.power_str, f"分型{i} 力度字符串不一致: {rs_fx.power_str} vs {czsc_fx.power_str}"
            assert abs(rs_fx.power_volume - czsc_fx.power_volume) < 1e-6, f"分型{i} 成交量力度不一致"

    def test_precision_alignment(self):
        """测试数值精度对齐"""
        # 测试所有笔的数值计算精度
        for i, (rs_bi, czsc_bi) in enumerate(zip(self.rs_czsc.bi_list, self.czsc_obj.bi_list)):
            # 高精度数值对齐测试
            assert abs(rs_bi.power_price - czsc_bi.power_price) < 1e-8, f"笔{i} 价格力度精度不匹配"
            assert abs(rs_bi.power_snr - czsc_bi.power_snr) < 1e-8, f"笔{i} SNR力度精度不匹配" 
            assert abs(rs_bi.rsq - czsc_bi.rsq) < 1e-8, f"笔{i} 拟合优度精度不匹配"
            assert abs(rs_bi.acceleration - czsc_bi.acceleration) < 1e-8, f"笔{i} 加速度精度不匹配"
            assert abs(rs_bi.angle - czsc_bi.angle) < 1e-6, f"笔{i} 角度精度不匹配"

    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空数据
        empty_bars = []
        rs_empty = CZSC(empty_bars, max_bi_num=50)
        czsc_empty = czsc.CZSC(empty_bars, max_bi_num=50) if empty_bars else None
        
        if czsc_empty is not None:
            assert len(rs_empty.bi_list) == len(czsc_empty.bi_list)
            assert len(rs_empty.fx_list) == len(czsc_empty.fx_list)

    def test_update_alignment(self):
        """测试动态更新的对齐性"""
        # 创建初始对象
        initial_bars = self.rs_bars[:20]  # 使用前20根K线
        initial_czsc_bars = self.czsc_bars[:20]
        
        rs_obj = CZSC(initial_bars.copy(), max_bi_num=50)
        czsc_obj = czsc.CZSC(initial_czsc_bars.copy(), max_bi_num=50)
        
        # 逐步添加新的K线
        for i in range(20, min(30, len(self.rs_bars))):
            rs_obj.update(self.rs_bars[i])
            czsc_obj.update(self.czsc_bars[i])
            
            # 验证每次更新后的一致性
            assert len(rs_obj.bi_list) == len(czsc_obj.bi_list), f"更新后笔数量不一致 at step {i}"
            assert len(rs_obj.fx_list) == len(czsc_obj.fx_list), f"更新后分型数量不一致 at step {i}"


@pytest.mark.skipif(not RS_CZSC_AVAILABLE, reason="需要安装 rs_czsc 库")
def test_rs_czsc_basic_functionality():
    """测试 rs_czsc 基本功能"""
    df = load_test_data()
    bars = format_standard_kline(df.copy(), freq=Freq.D)
    czsc_obj = CZSC(bars, max_bi_num=50)
    
    # 基本断言
    assert isinstance(czsc_obj, CZSC)
    assert len(bars) > 0
    assert czsc_obj.symbol is not None
    assert czsc_obj.freq == Freq.D


if __name__ == "__main__":
    # 运行所有测试
    pytest.main([__file__, "-v"])