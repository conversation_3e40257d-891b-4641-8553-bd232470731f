import numpy as np
import pandas as pd
from typing import List, Optional
import time
import talib
def ultimate_smoother(price, period):
    a1 = np.exp(-1.414 * np.pi / period)
    b1 = 2 * a1 * np.cos(np.radians(1.414 * 180 / period))
    c2 = b1
    c3 = -a1 * a1
    c1 = (1 + c2 - c3) / 4
    us = np.zeros(len(price))
    for i in range(len(price)):
        if i < 4:
            us[i] = price[i]
        else:
            us[i] = (1 - c1) * price[i] + (2 * c1 - c2) * price[i - 1] \
                    - (c1 + c3) * price[i - 2] + c2 * us[i - 1] + c3 * us[i - 2]
    return us
def rolling_rank(series, window):
    
    s = pd.Series(series)
    return s.rolling(window).apply(lambda x: x.rank().iloc[-1], raw=False).tolist()

def SINGLE_SMA_POSITIONS(series: pd.Series, n=5, **kwargs):
    """单均线多空

    :param series: str, 数据源字段
    :param n: int, 短周期
    """
    ms = series.rolling(window=n).mean()
    return np.sign(series - ms.rolling(window=n).mean()).fillna(0)
def SINGLE_EMA_POSITIONS(series: pd.Series, n=5, **kwargs):
    """单指数移动平均多空

    :param series: str, 数据源字段
    :param n: int, 短周期
    """
    ms = series.rolling(window=n).mean()
    ema = talib.EMA(ms, timeperiod=n)
    diff = series - ema
    result = np.sign(diff).fillna(0)
    
    
    return result
def MID_POSITIONS(series: pd.Series, n=13, **kwargs):
    """取窗口内的中间值作为中轴，中轴上方做多，下方做空

    中间值 = (最大值 + 最小值) / 2
    
    :param series: str, 数据源字段
    :param n: int, 短周期
    """
    ms = series.rolling(window=n).mean()
    high = ms.rolling(window=n).max()
    low = ms.rolling(window=n).min()
    mid = (high + low) / 2
    return np.sign(ms - mid).fillna(0)
def DOUBLE_SMA_POSITIONS(series: pd.Series, n=5, m=20, **kwargs):
    """双均线多空

    :param series: str, 数据源字段
    :param n: int, 短周期
    :param m: int, 长周期
    """
    assert n < m, "短周期必须小于长周期"
    return np.sign(series.rolling(window=n).mean() - series.rolling(window=m).mean()).fillna(0)
def TRIPLE_SMA_POSITIONS(series: pd.Series, m1=5, m2=10, m3=20):
    """
    三均线系统持仓信号生成函数。

    多头：factor > m3 的时候，m1 > m2
    空头：factor < m3 的时候，m1 < m2

    :param series: pd.Series, 通常为收盘价
    :param n: int, 计算 RSI 的周期，默认 14
    :param rsi_upper: float, RSI 超买阈值，超过则尝试开空
    :param rsi_lower: float, RSI 超卖阈值，低于则尝试做多
    :param rsi_exit: float, 用于多空头平仓的 RSI 值(如 50)

    :return: np.ndarray, 与 series 等长的持仓信号序列: 0(空仓), +1(多头), -1(空头)
    """
    series = series.copy()
    series = series.rolling(window=m1).mean()
    assert len(series) > m3, "series 长度必须大于 m3"
    assert m3 > m2 > m1, "m3 必须大于 m2 大于 m1"

    ma3 = series.rolling(m3).mean()
    ma2 = series.rolling(m2).mean()
    ma1 = series.rolling(m1).mean()

    positions = np.zeros(len(series), dtype=np.int32)
    for i in range(len(series)):
        if series.iloc[i] > ma3.iloc[i] and ma1.iloc[i] > ma2.iloc[i]:
            positions[i] = 1
        elif series.iloc[i] < ma3.iloc[i] and ma1.iloc[i] < ma2.iloc[i]:
            positions[i] = -1

    return positions
def BOLL_POSITIONS(series: pd.Series, n=5, k=0.1, **kwargs):
    """布林线多空

    series 大于 n 周期均线 + s * n周期标准差，做多；小于 n 周期均线 - s * n周期标准差，做空

    :param series: str, 数据源字段
    :param n: int, 短周期
    :param k: int, 波动率的倍数，默认为 0.1
    """
    series = series.copy()
    series = series.rolling(window=n).mean()
    sm = series.rolling(window=n).mean()
    sd = series.rolling(window=n).std()
    return np.where(series > sm + k * sd, 1, np.where(series < sm - k * sd, -1, 0))
def BOLL_REVERSE_POSITIONS(series: pd.Series, n=20, k=2.0):
    """
    布林带反转策略的多空持仓信号生成函数

    策略逻辑：
      1. 计算布林带：中轨为 MA(n), 上轨 = MA(n) + k*STD(n), 下轨 = MA(n) - k*STD(n)
      2. 开多：当价格 < 下轨时，开多 (pos=+1)，一直持有至价格 > 中轨 => 平多 (pos=0)
      3. 开空：当价格 > 上轨时，开空 (pos=-1)，一直持有至价格 < 中轨 => 平空 (pos=0)

    :param series: pd.Series, 通常为收盘价
    :param n: int, 滚动窗口大小, 默认 20
    :param k: float, 布林带标准差倍数, 默认 2.0
    :return: np.ndarray, 与 series 等长的持仓信号：0(空仓), +1(多头), -1(空头)
    """
    series = series.copy()
    series = series.rolling(window=n).mean()

    # --- 1) 计算布林带 ---
    upper, mid, lower = talib.BBANDS(series.values, timeperiod=n, nbdevup=k, nbdevdn=k)
    series = series.values

    # --- 2) 生成持仓信号 ---
    positions = np.zeros(len(series), dtype=np.int32)
    current_pos = 0  # 当前持仓：0=空仓，+1=多头，-1=空头

    for i in range(len(series)):
        # 若尚未计算出 mid/upper/lower，跳过（最前面的 n-1 个数据点）
        if np.isnan(mid[i]) or np.isnan(upper[i]) or np.isnan(lower[i]):
            positions[i] = 0
            continue

        price = series[i]

        # 若当前空仓
        if current_pos == 0:
            # 价格 > 上轨 => 开空
            if price > upper[i]:
                current_pos = -1
            # 价格 < 下轨 => 开多
            elif price < lower[i]:
                current_pos = +1

        # 若当前持有多头
        elif current_pos == +1:
            # 当价格 > 中轨 => 平多
            if price > mid[i]:
                current_pos = 0

        # 若当前持有空头
        elif current_pos == -1:
            # 当价格 < 中轨 => 平空
            if price < mid[i]:
                current_pos = 0

        positions[i] = current_pos

    return positions
def MMS_POSITIONS(series: pd.Series, timeperiod=5, window=5, **kwargs):
    """均线的最大最小值归一化

    :param series: str, 数据源字段
    :param timeperiod: int, 均线周期
    :param window: int, 窗口大小
    """
    sm = series.rolling(window=timeperiod).mean()
    sm_min = sm.rolling(window=window).min()
    sm_max = sm.rolling(window=window).max()
    res = (sm - sm_min) / (sm_max - sm_min)
    res = (res * 2 - 1).fillna(0)
    return res
def RSI_REVERSE_POSITIONS(series: pd.Series, n=14, rsi_upper=70, rsi_lower=30, rsi_exit=50):
    """
    同时包含多头与空头逻辑的 RSI 反转策略持仓信号生成函数。

    逻辑概述：
      1. 当当前无持仓 (pos=0) 时：
         - 若 RSI < rsi_lower，则开多 (pos=+1)，直至 RSI 升回 rsi_exit 以上则平仓 (pos=0)。
         - 若 RSI > rsi_upper，则开空 (pos=-1)，直至 RSI 跌回 rsi_exit 以下则平仓 (pos=0)。
      2. 若当前已有持仓，则等待满足平仓条件后回到空仓状态 (pos=0)。

    :param series: pd.Series, 通常为收盘价
    :param n: int, 计算 RSI 的周期，默认 14
    :param rsi_upper: float, RSI 超买阈值，超过则尝试开空
    :param rsi_lower: float, RSI 超卖阈值，低于则尝试做多
    :param rsi_exit: float, 用于多空头平仓的 RSI 值(如 50)

    :return: np.ndarray, 与 series 等长的持仓信号序列: 0(空仓), +1(多头), -1(空头)
    """
    series = series.copy()
    series = series.rolling(window=n).mean()

    # --- 1. 计算 RSI ---
    rsi = talib.RSI(series.values, timeperiod=n)

    # --- 2. 生成持仓信号 ---
    positions = np.zeros(len(rsi), dtype=np.int32)
    current_pos = 0  # 当前持仓状态：0表示空仓, +1表示多头, -1表示空头

    for i in range(len(rsi)):
        if np.isnan(rsi[i]):
            # 在无法计算出 RSI 的前 n-1 个位置上，默认空仓
            positions[i] = 0
            continue

        # 若当前空仓
        if current_pos == 0:
            # 如果 RSI < rsi_lower，则开多
            if rsi[i] < rsi_lower:
                current_pos = +1
            # 如果 RSI > rsi_upper，则开空
            elif rsi[i] > rsi_upper:
                current_pos = -1

        # 若当前持有多头
        elif current_pos == +1:
            # 当 RSI > rsi_exit，则平多 (回到空仓)
            if rsi[i] > rsi_exit:
                current_pos = 0

        # 若当前持有空头
        elif current_pos == -1:
            # 当 RSI < rsi_exit，则平空 (回到空仓)
            if rsi[i] < rsi_exit:
                current_pos = 0

        positions[i] = current_pos

    return positions

def TANH_POSITIONS(series: pd.Series, n=5, **kwargs):
    """tanh 多空

    :param series: str, 数据源字段
    :param n: int, 短周期
    """
    ms = series.rolling(window=n).mean()
    mean = ms.rolling(window=n).mean()
    std = ms.rolling(window=n).std()
    return np.tanh((ms - mean) / std).fillna(0).round(2)

def RANK_POSITIONS(series: pd.Series, n=5, **kwargs):
    """rank 多空

    :param series: str, 数据源字段
    :param n: int, 短周期
    """
    ms = series.rolling(window=n).mean()
    rank = (ms.rolling(window=n).rank(ascending=True, method="min") - 1) / (n - 1)
    x = (rank - 0.5).fillna(0) * 2
    return x.round(2)
# 基础函数：计算指数移动平均 (EMA)
def ema(series, period):
    return series.ewm(span=period, adjust=False).mean()
# 基础函数：计算真实波幅 (True Range)
def true_range(high, low, close_prev):
    tr1 = high - low
    tr2 = abs(high - close_prev)
    tr3 = abs(low - close_prev)
    return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
def rsx_ss2(close, period=14, smooth_period=20):
    """
    RSX-SS2 - 自适应平滑的RSI变体
    输入: close - 收盘价序列, period - RSI周期, smooth_period - 平滑周期
    返回: RSX-SS2值
    """
    # 计算价格变化
    delta = close.diff()
    
    # 计算增益和损失
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # 计算平均增益和平均损失
    avg_gain = gain.ewm(alpha=1/period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/period, adjust=False).mean()
    
    # 计算相对强度
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    # 使用终极平滑器进行平滑
    return ultimate_smoother(rsi, smooth_period)
def jurik_volty(close, period=20, power=1.0):
    """
    Jurik波动平滑器 - 低噪声波动指标
    输入: close - 收盘价序列, period - 平滑周期, power - 波动率幂次调整
    返回: 平滑波动率值
    """
    # 计算价格变化
    price_change = pd.Series([float('nan')] * len(close))
    for i in range(1, len(close)):
        price_change.iloc[i] = abs(close.iloc[i] - close.iloc[i-1])
    
    # 初步平滑
    span1 = period // 2
    alpha1 = 2.0 / (span1 + 1)
    
    # 第一次EMA
    smooth1 = pd.Series([float('nan')] * len(close))
    first_valid_idx = price_change.first_valid_index()
    if first_valid_idx is not None:
        first_value = price_change.iloc[first_valid_idx]
        all_same = True
        for i in range(first_valid_idx, len(price_change)):
            if not pd.isna(price_change.iloc[i]) and price_change.iloc[i] != first_value:
                all_same = False
                break
        
        if all_same:
            smooth1 = price_change.copy()
        else:
            smooth1.iloc[first_valid_idx] = price_change.iloc[first_valid_idx]
            for i in range(first_valid_idx + 1, len(price_change)):
                if not pd.isna(price_change.iloc[i]):
                    smooth1.iloc[i] = alpha1 * price_change.iloc[i] + (1.0 - alpha1) * smooth1.iloc[i-1]
    
    # 第二次EMA
    smooth2 = pd.Series([float('nan')] * len(close))
    first_valid_idx = smooth1.first_valid_index()
    if first_valid_idx is not None:
        first_value = smooth1.iloc[first_valid_idx]
        all_same = True
        for i in range(first_valid_idx, len(smooth1)):
            if not pd.isna(smooth1.iloc[i]) and smooth1.iloc[i] != first_value:
                all_same = False
                break
        
        if all_same:
            smooth2 = smooth1.copy()
        else:
            smooth2.iloc[first_valid_idx] = smooth1.iloc[first_valid_idx]
            for i in range(first_valid_idx + 1, len(smooth1)):
                if not pd.isna(smooth1.iloc[i]):
                    smooth2.iloc[i] = alpha1 * smooth1.iloc[i] + (1.0 - alpha1) * smooth2.iloc[i-1]
    
    # Jurik特定平滑公式
    jv = pd.Series([0.0] * len(close))
    for i in range(2, len(close)):
        if not pd.isna(smooth2.iloc[i]) and not pd.isna(smooth2.iloc[i-1]):
            jv.iloc[i] = (smooth2.iloc[i] + (smooth2.iloc[i] - smooth2.iloc[i-1]) * 0.5) * power
    
    # 最终平滑
    span3 = period // 3
    alpha3 = 2.0 / (span3 + 1)
    result = pd.Series([0.0] * len(close))
    
    first_valid_idx = (jv != 0.0).idxmax() if (jv != 0.0).any() else None
    if first_valid_idx is not None:
        first_value = jv.iloc[first_valid_idx]
        all_same = True
        for i in range(first_valid_idx, len(jv)):
            if jv.iloc[i] != 0.0 and jv.iloc[i] != first_value:
                all_same = False
                break
        
        if all_same:
            result = jv.copy()
        else:
            result.iloc[first_valid_idx] = jv.iloc[first_valid_idx]
            for i in range(first_valid_idx + 1, len(jv)):
                result.iloc[i] = alpha3 * jv.iloc[i] + (1.0 - alpha3) * result.iloc[i-1]
    
    return result

def ultimate_channel(high, low, close, period=20, multiplier=2.0):
    """
    终极通道 - 基于终极平滑器的通道指标
    输入: high, low, close - K线数据, period - 平滑周期, multiplier - 通道宽度乘数
    返回: (中线, 上轨, 下轨)
    """
    # 计算终极平滑中线
    midline = ultimate_smoother(close, period)
    
    # 计算平滑真实波幅 (STR)
    tr = true_range(high, low, close.shift(1))
    atr = tr.rolling(period).mean()
    str_smooth = ultimate_smoother(atr, period//2)
    
    # 计算通道
    upper = midline + multiplier * str_smooth
    lower = midline - multiplier * str_smooth
    return midline, upper, lower
def ultimate_bands(close, period=20, std_multiplier=2.0, smooth_period=5):
    """
    终极带 - 基于终极平滑器的布林带变体
    输入: close - 收盘价序列, period - 平滑周期, std_multiplier - 标准差乘数
    返回: (中线, 上轨, 下轨)
    """
    # 计算终极平滑中线
    midline = ultimate_smoother(close, period)
    
    # 计算标准差并平滑
    std = close.rolling(period).std()
    smooth_std = ultimate_smoother(std, smooth_period)
    
    # 计算通道
    upper = midline + std_multiplier * smooth_std
    lower = midline - std_multiplier * smooth_std
    return midline, upper, lower
def ultimate_oscillator(high, low, close, short_period=7, med_period=14, long_period=28):
    """
    终极波动指标 (UOS) - 多周期融合振荡器
    输入: high, low, close - K线数据, short_period/med_period/long_period - 计算周期
    返回: UOS值
    """
    # 计算买方压力和真实波幅
    buying_pressure = close - pd.concat([low, close.shift(1)], axis=1).min(axis=1)
    tr = true_range(high, low, close.shift(1))
    
    # 计算不同周期的平均值
    avg7 = buying_pressure.rolling(short_period).sum() / tr.rolling(short_period).sum()
    avg14 = buying_pressure.rolling(med_period).sum() / tr.rolling(med_period).sum()
    avg28 = buying_pressure.rolling(long_period).sum() / tr.rolling(long_period).sum()
    
    # 计算UOS
    uos = 100 * ((4 * avg7) + (2 * avg14) + avg28) / (4 + 2 + 1)
    return uos
def exponential_smoothing(series, alpha=0.3):
    """
    指数平滑 - 基础时间序列平滑技术
    输入: series - 输入序列, alpha - 平滑因子(0-1)
    返回: 平滑后的序列
    """
    result = [series.iloc[0]]
    for i in range(1, len(series)):
        result.append(alpha * series.iloc[i] + (1 - alpha) * result[i-1])
    return pd.Series(result, index=series.index)
def holt_winters(series, season_length=5, alpha=0.3, beta=0.1, gamma=0.2):
    """
    Holt-Winters三参数平滑 - 支持趋势和季节性的平滑方法
    输入: series - 输入序列, season_length - 季节周期长度
          alpha - 水平平滑因子, beta - 趋势平滑因子, gamma - 季节性平滑因子
    返回: 平滑后的序列
    """
    n = len(series)
    level = np.zeros(n)
    trend = np.zeros(n)
    season = np.zeros(n)
    forecast = np.zeros(n)
    
    # 初始化
    level[:season_length] = series.iloc[:season_length].mean()
    trend[:season_length] = 0
    season[:season_length] = series.iloc[:season_length] - level[:season_length]
    
    # 三重指数平滑
    for i in range(season_length, n):
        level[i] = alpha * (series.iloc[i] - season[i-season_length]) + (1 - alpha) * (level[i-1] + trend[i-1])
        trend[i] = beta * (level[i] - level[i-1]) + (1 - beta) * trend[i-1]
        season[i] = gamma * (series.iloc[i] - level[i]) + (1 - gamma) * season[i-season_length]
        forecast[i] = level[i] + trend[i] + season[i]
    
    # 前 season_length 个值设为原始值
    for i in range(season_length):
        forecast[i] = series.iloc[i]
    
    return pd.Series(forecast, index=series.index)
  