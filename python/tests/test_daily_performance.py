import numpy as np
import pandas as pd
import pytest
from rs_czsc._utils.utils import daily_performance


@pytest.fixture
def sample_returns():
    """测试用的日收益率数据"""
    return [0.01, 0.02, -0.01, 0.03, 0.02, -0.02, 0.01, -0.01, 0.02, 0.01]


@pytest.fixture
def expected_keys():
    """期望的输出字段"""
    return {
        "绝对收益", "年化", "夏普", "最大回撤", "卡玛", "日胜率", 
        "日盈亏比", "日赢面", "年化波动率", "下行波动率", "非零覆盖", 
        "盈亏平衡点", "新高间隔", "新高占比", "回撤风险", "回归年度回报率", 
        "长度调整平均最大回撤"
    }


class TestDailyPerformanceWrapper:
    """测试 daily_performance 包装函数的各种输入类型"""
    
    def test_with_list_input(self, sample_returns, expected_keys):
        """测试 list 类型输入"""
        result = daily_performance(sample_returns)
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
    
    def test_with_numpy_array_input(self, sample_returns, expected_keys):
        """测试 numpy array 类型输入"""
        np_returns = np.array(sample_returns)
        result = daily_performance(np_returns)
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
    
    def test_with_pandas_series_input(self, sample_returns, expected_keys):
        """测试 pandas Series 类型输入"""
        pd_returns = pd.Series(sample_returns)
        result = daily_performance(pd_returns)
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
    
    def test_with_float32_array(self, sample_returns, expected_keys):
        """测试 float32 numpy array 会被转换为 float64"""
        np_returns = np.array(sample_returns, dtype=np.float32)
        result = daily_performance(np_returns)
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
    
    def test_with_custom_yearly_days(self, sample_returns, expected_keys):
        """测试自定义年化天数参数"""
        result = daily_performance(sample_returns, yearly_days=365)
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
    
    def test_with_empty_list(self, expected_keys):
        """测试空列表输入"""
        result = daily_performance([])
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
        # 空数据应该返回全零指标
        assert result["年化"] == 0.0
    
    def test_with_zero_returns(self, expected_keys):
        """测试全零收益率"""
        zero_returns = [0.0] * 10
        result = daily_performance(zero_returns)
        assert isinstance(result, dict)
        assert set(result.keys()) == expected_keys
        assert result["年化"] == 0.0
        assert result["年化波动率"] == 0.0
    
    def test_invalid_input_type(self):
        """测试不支持的输入类型"""
        with pytest.raises(TypeError, match="Unsupported type for daily_returns"):
            daily_performance("invalid_input")
    
    def test_results_consistency(self, sample_returns):
        """测试不同输入类型的结果一致性"""
        list_result = daily_performance(sample_returns)
        np_result = daily_performance(np.array(sample_returns))
        pd_result = daily_performance(pd.Series(sample_returns))
        
        # 所有输入类型应该产生相同的结果
        assert list_result == np_result == pd_result
    
    def test_output_value_types(self, sample_returns):
        """测试输出值的类型"""
        result = daily_performance(sample_returns)
        
        # 大部分字段应该是 float
        float_fields = [
            "绝对收益", "年化", "夏普", "最大回撤", "卡玛", "日胜率", 
            "日盈亏比", "日赢面", "年化波动率", "下行波动率", "非零覆盖", 
            "盈亏平衡点", "新高间隔", "新高占比", "回撤风险", 
            "长度调整平均最大回撤"
        ]
        
        for field in float_fields:
            assert isinstance(result[field], float), f"{field} should be float"
        
        # 回归年度回报率可能为 None
        assert result["回归年度回报率"] is None or isinstance(result["回归年度回报率"], float)
    
    def test_logical_constraints(self, sample_returns):
        """测试输出值的逻辑约束"""
        result = daily_performance(sample_returns)
        
        # 日胜率、非零覆盖、新高占比应该在 [0, 1] 区间
        assert 0 <= result["日胜率"] <= 1
        assert 0 <= result["非零覆盖"] <= 1  
        assert 0 <= result["新高占比"] <= 1
        assert 0 <= result["盈亏平衡点"] <= 1
        
        # 最大回撤应该为正值
        assert result["最大回撤"] >= 0
        
        # 年化波动率、下行波动率应该为正值
        assert result["年化波动率"] >= 0
        assert result["下行波动率"] >= 0