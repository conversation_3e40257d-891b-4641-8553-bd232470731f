# -*- coding: utf-8 -*-
"""
pytest 配置文件
包含通用的 fixtures 和测试设置
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from rs_czsc import RawBar, Freq


@pytest.fixture
def sample_kline_data():
    """创建示例K线数据"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5min')
    data = []
    base_price = 100.0
    
    for i, dt in enumerate(dates):
        # 模拟价格变动
        change = (i % 10 - 5) * 0.1
        open_price = base_price + change
        close_price = open_price + (i % 7 - 3) * 0.05
        high_price = max(open_price, close_price) + abs(change) * 0.1
        low_price = min(open_price, close_price) - abs(change) * 0.1
        volume = 1000 + i * 10
        amount = volume * (high_price + low_price) / 2
        
        data.append({
            'dt': dt,
            'symbol': 'TEST.SZ',
            'open': round(open_price, 2),
            'close': round(close_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'vol': volume,
            'amount': round(amount, 2)
        })
        
        base_price = close_price
    
    return pd.DataFrame(data)


@pytest.fixture
def raw_bars(sample_kline_data):
    """将K线数据转换为RawBar列表"""
    bars = []
    for i, row in sample_kline_data.iterrows():
        bar = RawBar(
            id=i,
            symbol=row["symbol"],
            dt=row["dt"],
            open=row["open"],
            close=row["close"],
            high=row["high"],
            low=row["low"],
            vol=row["vol"],
            amount=row["amount"],
            freq=Freq.F5,
        )
        bars.append(bar)
    return bars


@pytest.fixture
def test_data_dir():
    """测试数据目录"""
    return Path(__file__).parent / "data"


@pytest.fixture
def output_dir(tmp_path):
    """临时输出目录"""
    return tmp_path / "output"


def pytest_configure(config):
    """pytest 配置"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "benchmark: marks tests as benchmark tests"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试项目收集"""
    skip_slow = pytest.mark.skip(reason="需要使用 --runslow 选项运行")
    for item in items:
        if "slow" in item.keywords:
            if not config.getoption("--runslow"):
                item.add_marker(skip_slow)


def pytest_addoption(parser):
    """添加命令行选项"""
    parser.addoption(
        "--runslow", action="store_true", default=False, help="运行标记为slow的测试"
    ) 