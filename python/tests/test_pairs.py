#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 WeightBacktest 中新添加的 pairs 方法
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from rs_czsc._trader.weight_backtest import WeightBacktest


def create_sample_data():
    """创建测试用的样例数据"""
    # 创建时间序列
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(days=i) for i in range(100)]
    
    # 创建多个品种的数据
    symbols = ['DL2401', 'AG2401', 'CU2401']
    
    data = []
    for symbol in symbols:
        for i, dt in enumerate(dates):
            # 模拟价格波动
            base_price = 1000 + np.random.normal(0, 50)
            
            # 模拟权重变化
            if i % 20 < 10:  # 前10天持多仓
                weight = 0.3
            elif i % 20 < 15:  # 中间5天持空仓
                weight = -0.2
            else:  # 后5天无仓位
                weight = 0.0
            
            data.append({
                'dt': dt.strftime('%Y-%m-%d %H:%M:%S'),
                'symbol': symbol,
                'weight': weight,
                'price': base_price
            })
    
    return pd.DataFrame(data)

def test_pairs_method():
    """测试 pairs 方法"""
    try:
        # 创建测试数据
        print("创建测试数据...")
        dfw = create_sample_data()
        print(f"测试数据形状: {dfw.shape}")
        print(f"包含品种: {dfw['symbol'].unique()}")
        
        # 创建回测实例
        print("\n创建回测实例...")
        wb = WeightBacktest(
            dfw=dfw,
            digits=2,
            fee_rate=0.0002,
            n_jobs=1,
            weight_type='ts',
            yearly_days=252
        )
        
        # 测试 pairs 属性
        print("\n获取交易对数据...")
        pairs_df = wb.pairs
        
        print(f"交易对数据形状: {pairs_df.shape}")
        if not pairs_df.empty:
            print(f"交易对数据列名: {list(pairs_df.columns)}")
            print("\n前5行交易对数据:")
            print(pairs_df.head())
        else:
            print("交易对数据为空")
            
        print("\n测试完成！pairs 方法已成功添加到 PyWeightBacktest 中。")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_pairs_method() 