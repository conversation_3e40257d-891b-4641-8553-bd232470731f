[build]
# 设置并行构建任务数
jobs = 4

# 针对 Windows 的优化配置
[target.x86_64-pc-windows-msvc]
rustflags = ["-C", "target-cpu=native"]

# 开发时的优化配置
[profile.dev]
# 启用部分优化以加快编译速度
opt-level = 1
# 保持调试信息
debug = true
# 减少二进制文件大小
strip = false

# 发布配置
[profile.release]
# 最大优化
opt-level = 3
# 链接时优化
lto = true
# 代码生成单元数
codegen-units = 1
# 去除调试符号
strip = true

# 针对 PyO3 的特殊配置 - 注释掉让 PyO3 自动检测
# [env]
# PYO3_PYTHON = { value = "python", relative = true }
