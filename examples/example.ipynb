{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('O')"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "\n", "df1 = pd.read_csv(\"weights_250314.csv\")\n", "df1[\"dt\"].dtype"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('<M8[ns]')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df1[\"dt\"] = pd.to_datetime(df1[\"dt\"])\n", "df1[\"dt\"].dtype"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>symbol</th>\n", "      <th>weight</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2018-03-26 09:01:00</td>\n", "      <td>SEsc9001</td>\n", "      <td>0.0</td>\n", "      <td>442.900000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2018-03-26 09:02:00</td>\n", "      <td>SEsc9001</td>\n", "      <td>0.0</td>\n", "      <td>441.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2018-03-26 09:03:00</td>\n", "      <td>SEsc9001</td>\n", "      <td>0.0</td>\n", "      <td>440.400000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2018-03-26 09:04:00</td>\n", "      <td>SEsc9001</td>\n", "      <td>0.0</td>\n", "      <td>439.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2018-03-26 09:05:00</td>\n", "      <td>SEsc9001</td>\n", "      <td>0.0</td>\n", "      <td>440.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3938508</th>\n", "      <td>2023-07-31 14:55:00</td>\n", "      <td>ZZUR9001</td>\n", "      <td>0.4</td>\n", "      <td>3523.304960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3938509</th>\n", "      <td>2023-07-31 14:56:00</td>\n", "      <td>ZZUR9001</td>\n", "      <td>0.4</td>\n", "      <td>3520.270244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3938510</th>\n", "      <td>2023-07-31 14:57:00</td>\n", "      <td>ZZUR9001</td>\n", "      <td>0.4</td>\n", "      <td>3520.270244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3938511</th>\n", "      <td>2023-07-31 14:58:00</td>\n", "      <td>ZZUR9001</td>\n", "      <td>0.4</td>\n", "      <td>3517.235528</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3938512</th>\n", "      <td>2023-07-31 14:59:00</td>\n", "      <td>ZZUR9001</td>\n", "      <td>0.4</td>\n", "      <td>3517.235528</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3938513 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                         dt    symbol  weight        price\n", "0       2018-03-26 09:01:00  SEsc9001     0.0   442.900000\n", "1       2018-03-26 09:02:00  SEsc9001     0.0   441.200000\n", "2       2018-03-26 09:03:00  SEsc9001     0.0   440.400000\n", "3       2018-03-26 09:04:00  SEsc9001     0.0   439.500000\n", "4       2018-03-26 09:05:00  SEsc9001     0.0   440.700000\n", "...                     ...       ...     ...          ...\n", "3938508 2023-07-31 14:55:00  ZZUR9001     0.4  3523.304960\n", "3938509 2023-07-31 14:56:00  ZZUR9001     0.4  3520.270244\n", "3938510 2023-07-31 14:57:00  ZZUR9001     0.4  3520.270244\n", "3938511 2023-07-31 14:58:00  ZZUR9001     0.4  3517.235528\n", "3938512 2023-07-31 14:59:00  ZZUR9001     0.4  3517.235528\n", "\n", "[3938513 rows x 4 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "\n", "df = pd.read_feather(\"run_backtest/data/weight_example.feather\")\n", "df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from rs_czsc import WeightBacktest\n", "wb = WeightBacktest(dfw=df[['dt', 'symbol', 'weight', 'price']], fee_rate=0)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'开始日期': '2017-01-03',\n", " '结束日期': '2023-07-31',\n", " '绝对收益': 0.8109,\n", " '年化': 0.1086,\n", " '夏普': 1.407,\n", " '最大回撤': 0.1288,\n", " '卡玛': 0.8432,\n", " '日胜率': 0.5489,\n", " '日盈亏比': 1.0781,\n", " '日赢面': 0.1407,\n", " '年化波动率': 0.0772,\n", " '下行波动率': 0.0547,\n", " '非零覆盖': 0.9665,\n", " '盈亏平衡点': 0.9724,\n", " '新高间隔': 227.0,\n", " '新高占比': 0.1063,\n", " '回撤风险': 1.6684,\n", " '回归年度回报率': 0.1217,\n", " '长度调整平均最大回撤': 0.1714,\n", " '交易胜率': 0.3717,\n", " '单笔收益': 25.59,\n", " '持仓K线数': 972.81,\n", " '持仓天数': 3.68,\n", " '多头占比': 0.5028,\n", " '空头占比': 0.4611,\n", " '与基准相关性': 0.0724,\n", " '与基准空头相关性': -0.1507,\n", " '波动比': 0.5851,\n", " '与基准波动相关性': 0.2081,\n", " '品种数量': 9}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# {'开始日期': '2017-01-03',\n", "#  '结束日期': '2023-07-31',\n", "#  '绝对收益': 0.8109,\n", "#  '年化': 0.1086,\n", "#  '夏普': 1.407,\n", "#  '最大回撤': 0.1288,\n", "#  '卡玛': 0.8432,\n", "#  '日胜率': 0.5489,\n", "#  '日盈亏比': 1.0781,\n", "#  '日赢面': 0.1407,\n", "#  '年化波动率': 0.0772,\n", "#  '下行波动率': 0.0547,\n", "#  '非零覆盖': 0.9665,\n", "#  '盈亏平衡点': 0.9724,\n", "#  '新高间隔': 227.0,\n", "#  '新高占比': 0.1063,\n", "#  '回撤风险': 1.6684,\n", "#  '回归年度回报率': 0.1217,\n", "#  '长度调整平均最大回撤': 0.1714,\n", "#  '交易胜率': 0.3717,\n", "#  '单笔收益': 25.59,\n", "#  '持仓K线数': 972.81,\n", "#  '持仓天数': 3.68,\n", "#  '多头占比': 0.5028,\n", "#  '空头占比': 0.4611,\n", "#  '与基准相关性': 0.0724,\n", "#  '与基准空头相关性': -0.1507,\n", "#  '波动比': 0.5851,\n", "#  '与基准波动相关性': 0.2081,\n", "#  '品种数量': 9}\n", "\n", "wb.stats"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'total'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32me:\\Projects\\czscflow\\apps\\web-antd\\rs_czsc\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'total'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 20\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mrs_czsc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m daily_performance\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# {'绝对收益': 0.6895,\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m#  '年化': 0.0923,\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;66;03m#  '夏普': 1.194,\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;66;03m#  '回撤风险': 1.7801,\u001b[39;00m\n\u001b[0;32m     18\u001b[0m \u001b[38;5;66;03m#  '回归年度回报率': 0.1046}\u001b[39;00m\n\u001b[1;32m---> 20\u001b[0m daily_returns \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtotal\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m     21\u001b[0m daily_performance(daily_returns) \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n", "File \u001b[1;32me:\\Projects\\czscflow\\apps\\web-antd\\rs_czsc\\venv\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32me:\\Projects\\czscflow\\apps\\web-antd\\rs_czsc\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'total'"]}], "source": ["from rs_czsc import daily_performance\n", "\n", "# {'绝对收益': 0.6895,\n", "#  '年化': 0.0923,\n", "#  '夏普': 1.194,\n", "#  '最大回撤': 0.1376,\n", "#  '卡玛': 0.6708,\n", "#  '日胜率': 0.5228,\n", "#  '日盈亏比': 1.1487,\n", "#  '日赢面': 0.1233,\n", "#  '年化波动率': 0.0773,\n", "#  '下行波动率': 0.0548,\n", "#  '非零覆盖': 1.0,\n", "#  '盈亏平衡点': 0.9782,\n", "#  '新高间隔': 229.0,\n", "#  '新高占比': 0.0866,\n", "#  '回撤风险': 1.7801,\n", "#  '回归年度回报率': 0.1046}\n", "\n", "daily_returns = df[\"total\"]\n", "daily_performance(daily_returns) # type: ignore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PY311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}