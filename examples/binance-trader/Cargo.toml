[package]
name = "binance-trader"
version.workspace = true
edition = "2024"

[dependencies]
serde_json.workspace = true
binance = { path = "../../crates/binance" }
czsc-binance = { path = "../../crates/czsc-binance" }
czsc-utils = { path = "../../crates/czsc-utils", features = ["axon_util"] }
serde.workspace = true

tokio = { version = "1.41.0", features = ["full"] }
# colored = "3"
tracing-subscriber = { version = "0.3.19", features = [] }
tracing.workspace = true
clap = { version = "4.5", features = ["derive", "env"] }
