# 币安U本位合约自动交易程序示例

分为手动模式和自动模式。

手动模式必须通过 `--weigthts` 参数提供目标权重JSON文件,例如:

```json
{
    "BTCUSDT": -0.9,
    "ETHUSDT": 0.1
}

```

自动模式通过服务器获取权重自动调仓

## 程序命令行参数

```
通用参数

Usage: binance-trader.exe [OPTIONS] <COMMAND>

Commands:
  man   手动模式
  auto  自动模式
  help  Print this message or the help of the given subcommand(s)

Options:
      --proxy <PROXY>
          HTTP 代理地址(例如: http://127.0.0.1:10001), 不填则不使用代理
      --testnet
          使用测试网络 (可选)
      --key <KEY>
          API 密钥 (可选，可从环境变量获取) [env: BINANCE_API_KEY=]
      --secret <SECRET>
          API 密钥 (可选，可从环境变量获取) [env: BINANCE_API_SECRET=]
      --attempts <NUM>
          最大尝试次数 (默认: 20) [default: 20]
      --wait <WAIT>
          挂单尝试等待时间，单位秒 (默认: 5) [default: 5]
      --ticker-timeout <TICKER_TIMEOUT>
          等待时间，单位秒 (默认: 60) [default: 60]
      --log-level <LEVEL>
          设置日志输出级别 [default: info] [possible values: trace, debug, info, warn, error]
  -h, --help
          Print help
  -V, --version
          Print version
```


### 手动模式: cargo run -- man -h

```txt
手动模式

Usage: binance-trader.exe man [OPTIONS] --weights <FILE>

Options:
      --weights <FILE>     JSON 文件路径 (必须)
      --log-level <LEVEL>  设置日志输出级别 [default: info] [possible values: trace, debug, info, warn, error]
  -h, --help               Print help
```

### 自动模式: cargo run -- man -h

```txt
自动模式

Usage: binance-trader.exe auto [OPTIONS] --strat <STRAT>...

Options:
      --interval <INTERVAL>
          自动调仓间隔，单位秒 (默认: 60) [default: 60]
      --czsc-api <CZSC_API>
          CZSC API (可选，可从环境变量获取) [env: CZSC_DATA_API=]
      --czsc-token <CZSC_TOKEN>
          CZSC TOKEN (可选，可从环境变量获取) [env: CZSC_TOKEN=]
      --long-leverage <LONG_LEVERAGE>
          做多杠杆，(默认: 1) [default: 1]
      --short-leverage <SHORT_LEVERAGE>
          做空杠杆，(默认: 1) [default: 1]
      --strat <STRAT>...
          策略参数格式示例: BTC_1H_P04:0.3 BTC_2H_P03:0.7
      --log-level <LEVEL>
          设置日志输出级别 [default: info] [possible values: trace, debug, info, warn, error]
  -h, --help
          Print help
```

自动模式可以使用Ctrl+c中断退出

## 示例

手动执行
```
cargo run -- --proxy http://127.0.0.1:20171 --wait 3 --testnet man --weights target_weights.json
```
