use crate::cmd::{AutoArgs, CommonArgs, ManualArgs};
use binance::config::Config;
use czsc_binance::futures_trader::trader::BinanceFuturesTrader;
use czsc_utils::{
    axon_util::object_helpers::{StrategyConfig, calculate_portfolio},
    data_client::{DEFAULT_CZSC_URL, DataClient},
};
use std::fs;
use std::{collections::HashMap, time::Duration};
use tokio::{
    signal,
    time::{self, Instant, MissedTickBehavior},
};

/// 自动执行
pub(crate) async fn run_automation(common_args: CommonArgs, args: AutoArgs) -> Option<()> {
    let czsc_token = args.czsc_token.or_else(|| {
        tracing::error!("必须通过 --czsc-token 参数或 CZSC_TOKEN 环境变量提供 API 令牌");
        None
    })?;

    let czscs_api = args.czsc_api.or_else(|| {
        let url = DEFAULT_CZSC_URL.to_string();
        tracing::warn!("未提供CZSC API, 使用默认API: {}", url);
        Some(url)
    })?;

    let attempts = common_args.attempts;
    let wait = common_args.wait;
    let ticker_timeout = common_args.ticker_timeout;

    let trader = create_trader(common_args).await?;

    let dc = DataClient::new(Some(czscs_api), czsc_token);

    let long_leverage = args.long_leverage as f64;
    let short_leverage = args.short_leverage as f64;
    let strategy_config = args.strat;

    let strategy_config_json = serde_json::to_string_pretty(&strategy_config).unwrap();
    tracing::warn!(
        "策略使用: {strategy_config_json} \
        做多杠杆: {long_leverage}倍, 做空杠杆: {short_leverage}倍 \
        挂单等待超时: {wait:?}, 挂单最大重试次数: {attempts} Ticker 数据等待超时: {ticker_timeout:?}\
        自动调仓间隔: {interval:?} ",
        strategy_config_json = strategy_config_json,
        long_leverage = long_leverage,
        short_leverage = short_leverage,
        interval = args.interval,
        attempts = attempts,
        wait = wait,
        ticker_timeout = ticker_timeout,
    );

    // 设置定时器
    let mut interval = time::interval(args.interval);
    // 动态调整策略: 以每次实际执行时刻为基准，按 `lastTickTime + period` 安排下次执行
    // 无论单次任务耗时多少，两次任务开始时刻的间隔始终 ≥ 20秒
    interval.set_missed_tick_behavior(MissedTickBehavior::Delay);

    loop {
        tokio::select! {
            // 处理Ctrl+C信号
            _ = signal::ctrl_c() => {
                tracing::warn!("接收到Ctrl+C, 退出...");
                break;
            }
            // 定期执行任务
            _ = interval.tick() => {
                auto_adjust(&dc, &trader, &strategy_config, long_leverage, short_leverage, attempts, wait, ticker_timeout).await?;
            }
        }
    }

    None
}

async fn auto_adjust<'a>(
    dc: &DataClient<'a>,
    trader: &BinanceFuturesTrader,
    strategies: &'a Vec<StrategyConfig>,
    long_leverage: f64,
    short_leverage: f64,
    attempts: usize,
    wait: Duration,
    ticker_timeout: Duration,
) -> Option<()> {
    let start = Instant::now();
    tracing::warn!("正在从服务器获取权重数据..");
    let target_weights = calculate_portfolio(&dc, strategies, long_leverage, short_leverage)
        .await
        .map_err(|e| {
            tracing::error!("权重数据获取失败: {}", e);
        })
        .ok()?;
    let duration = start.elapsed();
    tracing::info!("获取权重执行时长: {:?}", duration);

    // 调仓
    let _ = trader
        .run(&target_weights, attempts, wait, ticker_timeout)
        .await
        .map_err(|e| {
            tracing::error!("执行交易失败: {}", e);
            e
        });

    tracing::info!("本次调仓结束，等待下一次间隔执行调仓..");
    Some(())
}

pub(crate) async fn run_manual(common_args: CommonArgs, args: ManualArgs) -> Option<()> {
    // 读取并解析权重文件
    let weights_file = fs::read_to_string(&args.weights)
        .map_err(|e| {
            tracing::error!("无法读取文件: {}", args.weights.display());
            e
        })
        .ok()?;

    let target_weights: HashMap<String, f64> =
        serde_json::from_str(&weights_file).expect("JSON 解析失败");

    let attempts = common_args.attempts;
    let wait = common_args.wait;
    let ticker_timeout = common_args.ticker_timeout;

    let trader = create_trader(common_args).await?;

    tracing::warn!(
        "目标权重: {weights_file}\
        挂单等待超时: {wait:?}, 挂单最大重试次数: {attempts} Ticker 数据等待超时: {ticker_timeout:?}",
        weights_file = weights_file,
        attempts = attempts,
        wait = wait,
        ticker_timeout = ticker_timeout,
    );

    let _ = trader
        .run(&target_weights, attempts, wait, ticker_timeout)
        .await
        .map_err(|e| {
            tracing::error!("执行交易失败: {}", e);
            e
        });
    None
}

/// # 从通用参数创建 trader
///
/// 返回: 交易程序实例，如果失败返回None
pub(crate) async fn create_trader(common_args: CommonArgs) -> Option<BinanceFuturesTrader> {
    let api_key = common_args.key.or_else(|| {
        tracing::error!("必须通过 --key 参数或 BINANCE_API_KEY 环境变量提供 API 密钥");
        None
    })?;

    let api_secret = common_args.secret.or_else(|| {
        tracing::error!("必须通过 --secret 参数或 BINANCE_API_SECRET 环境变量提供 API 密钥");
        None
    })?;

    let binance_config = if common_args.testnet {
        tracing::info!("使用测试网络");
        Config::testnet()
    } else {
        tracing::info!("使用实盘网络");
        Config::default()
    };

    BinanceFuturesTrader::new(api_key, api_secret, binance_config, common_args.proxy)
        .await
        .map_err(|e| {
            tracing::error!("创建交易程序实例失败: {}", e);
            e
        })
        .ok()
}
