use clap::Parser;
use cmd::Cli;
use cmd::SubCommand;
use util::run_automation;
use util::run_manual;

mod cmd;
mod util;

#[tokio::main]
async fn main() {
    let cli = Cli::parse();

    // 初始化日志系统
    tracing_subscriber::fmt()
        .with_max_level(cli.common.log_level.to_level_filter())
        .init();

    if let Some(ref p) = cli.common.proxy {
        tracing::info!("使用 HTTP 代理 {} ", p);
    } else {
        tracing::info!("不使用代理");
    }

    let common_args = cli.common;
    match cli.command {
        SubCommand::Manual(args) => {
            tracing::info!("使用手动调仓模式");
            run_manual(common_args, args).await;
        }
        SubCommand::Auto(args) => {
            tracing::info!("使用自动调仓模式");

            run_automation(common_args, args).await;
        }
    }
}
