//! 命令行参数处理
//!
use clap::{Args, Parser, Subcommand, ValueEnum};
use czsc_utils::axon_util::object_helpers::StrategyConfig;
use std::path::PathBuf;
use std::time::Duration;
use tracing::metadata::LevelFilter;

#[derive(Debug, Parser)]
#[command(author, version, about, long_about = None)]
pub(crate) struct Cli {
    #[clap(subcommand)]
    pub(crate) command: SubCommand,

    #[clap(flatten)]
    pub(crate) common: CommonArgs,
}

#[derive(Debug, Subcommand)]
pub(crate) enum SubCommand {
    /// 手动模式
    #[clap(name = "man")]
    Manual(ManualArgs),
    /// 自动模式
    #[clap(name = "auto")]
    Auto(AutoArgs),
}

#[derive(Debug, Args)]
pub(crate) struct ManualArgs {
    /// 必须指定包含权重配置的 JSON 文件
    #[arg(
        long,
        required = true,
        value_name = "FILE",
        help = "JSON 文件路径 (必须)"
    )]
    pub(crate) weights: PathBuf,
}

#[derive(Debug, Args)]
pub(crate) struct AutoArgs {
    /// 自动调仓间隔
    #[arg(
        long,
        default_value = "60",
        value_parser = parse_duration,
        value_name = "INTERVAL",
        help = "自动调仓间隔，单位秒 (默认: 60)"
    )]
    pub(crate) interval: Duration,

    /// CZSC API (从环境变量 CZSC_DATA_API 获取，如果未提供)
    #[arg(
        long,
        env = "CZSC_DATA_API",
        help = "CZSC API (可选，可从环境变量获取)"
    )]
    pub(crate) czsc_api: Option<String>,

    /// CZSC TOKEN (从环境变量 CZSC_TOKEN 获取，如果未提供)
    #[arg(long, env = "CZSC_TOKEN", help = "CZSC TOKEN (可选，可从环境变量获取)")]
    pub(crate) czsc_token: Option<String>,

    #[arg(
        long,
        default_value = "1",
        value_name = "LONG_LEVERAGE",
        help = "做多杠杆，(默认: 1)"
    )]
    pub(crate) long_leverage: u32,

    #[arg(
        long,
        default_value = "1",
        value_name = "SHORT_LEVERAGE",
        help = "做空杠杆，(默认: 1)"
    )]
    pub(crate) short_leverage: u32,

    #[arg(
        long,
        required = true,
        value_parser = parse_strategy_config,
        num_args = 1..,
        help = "策略参数格式示例: BTC_1H_P04:0.3 BTC_2H_P03:0.7"
    )]
    pub(crate) strat: Vec<StrategyConfig>,
}

/// 通用参数
#[derive(Debug, Args)]
pub(crate) struct CommonArgs {
    /// HTTP 代理地址
    #[arg(
        long,
        value_name = "PROXY",
        help = "HTTP 代理地址(例如: http://127.0.0.1:10001), 不填则不使用代理"
    )]
    pub(crate) proxy: Option<String>,

    /// 是否使用测试网络
    #[arg(
        long,
        action = clap::ArgAction::SetTrue,
        help = "使用测试网络 (可选)"
    )]
    pub(crate) testnet: bool,

    /// API 密钥 (从环境变量 BINANCE_API_KEY 获取，如果未提供)
    #[arg(
        long,
        env = "BINANCE_API_KEY",
        help = "API 密钥 (可选，可从环境变量获取)"
    )]
    pub(crate) key: Option<String>,

    /// API 密钥 (从环境变量 BINANCE_API_SECRET 获取，如果未提供)
    #[arg(
        long,
        env = "BINANCE_API_SECRET",
        help = "API 密钥 (可选，可从环境变量获取)"
    )]
    pub(crate) secret: Option<String>,

    /// 最大尝试次数
    #[arg(
        long,
        default_value_t = 20,
        value_name = "NUM",
        help = "最大尝试次数 (默认: 20)"
    )]
    pub(crate) attempts: usize,

    /// 操作等待时间
    #[arg(
        long,
        default_value = "5",
        value_parser = parse_duration,
        value_name = "WAIT",
        help = "挂单尝试等待时间，单位秒 (默认: 5)"
    )]
    pub(crate) wait: Duration,


    /// Ticker数据等待时间
    #[arg(
        long,
        default_value = "60",
        value_parser = parse_duration,
        value_name = "TICKER_TIMEOUT",
        help = "等待时间，单位秒 (默认: 60)"
    )]
    pub(crate) ticker_timeout: Duration,

    /// 设置日志输出级别
    #[arg(
        long,
        value_enum,
        default_value = "info",
        global = true,
        value_name = "LEVEL"
    )]
    pub(crate) log_level: LogLevel,
}

fn parse_strategy_config(s: &str) -> Result<StrategyConfig, String> {
    let mut parts = s.splitn(2, ':');

    let name = parts
        .next()
        .ok_or_else(|| format!("参数格式错误: '{}' (格式应为 名称:权重)", s))?;

    let weight_str = parts
        .next()
        .ok_or_else(|| format!("参数缺少权重部分: '{}' (格式应为 名称:权重)", s))?;

    let weight = weight_str
        .parse::<f64>()
        .map_err(|_| format!("无效的权重值: '{}' (必须为数字)", weight_str))?;

    if name.is_empty() {
        return Err("策略名称不能为空".into());
    }

    Ok(StrategyConfig {
        base_freq: None,
        outsample_sdt: None,
        description: None,
        name: name.to_string(),
        weight,
    })
}

/// 持续时间解析器
fn parse_duration(s: &str) -> Result<Duration, String> {
    s.parse::<u64>()
        .map(Duration::from_secs)
        .map_err(|_| format!("无效的持续时间: {}", s))
}

/// 日志级别枚举
#[derive(Debug, Clone, ValueEnum)]
pub(crate) enum LogLevel {
    #[value(name = "trace")]
    Trace,
    #[value(name = "debug")]
    Debug,
    #[value(name = "info")]
    Info,
    #[value(name = "warn")]
    Warn,
    #[value(name = "error")]
    Error,
}

impl LogLevel {
    /// 转换为日志库的等级过滤
    pub(crate) fn to_level_filter(&self) -> LevelFilter {
        match self {
            LogLevel::Trace => LevelFilter::TRACE,
            LogLevel::Debug => LevelFilter::DEBUG,
            LogLevel::Info => LevelFilter::INFO,
            LogLevel::Warn => LevelFilter::WARN,
            LogLevel::Error => LevelFilter::ERROR,
        }
    }
}
