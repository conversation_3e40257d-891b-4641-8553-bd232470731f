use czsc::trader::weight_backtest::{WeightBacktest, WeightType};
use polars::prelude::*;
use std::{env, fs::File, time::Instant};

fn main() {
    // 从命令行参数获取文件路径和并行作业数量
    let args: Vec<String> = env::args().collect();

    // 获取文件路径参数，默认值为 "data/weight_example.feather"
    let file_path = args
        .iter()
        .position(|x| x == "--file")
        .and_then(|idx| args.get(idx + 1))
        .map(|s| s.as_str())
        .unwrap_or("data/weight_example.feather");

    // 获取 jobs 参数，默认值为 1
    let jobs = args
        .iter()
        .position(|x| x == "--jobs")
        .and_then(|idx| args.get(idx + 1))
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(1);

    // 打开文件
    let file = File::open(file_path).expect("Failed to open the file");
    let df = IpcReader::new(file)
        .finish()
        .expect("Failed to read DataFrame");

    // 开始计时
    let start = Instant::now();

    // 初始化回测
    let backtest = WeightBacktest::new(df, 2, None);
    if backtest.is_err() {
        println!("FOUND ERROR WHEN INIT: {:?}", backtest.err());
        return;
    }
    let mut backtest = backtest.unwrap();

    let res = backtest.backtest(Some(jobs), WeightType::TS, 252).unwrap();
    let duration = start.elapsed();

    println!("{}", backtest.report.unwrap().symbols[0].pair);

    // // 打印耗时
    // println!("Backtest completed in: {duration:.2?}");
    // if res.is_err() {
    //     println!("FOUND ERROR: {:?}", res.err());
    // } else {
    //     let stats = backtest.report.unwrap().stats;
    //     // 打印基本信息
    //     println!("回测期间: {} 至 {}", stats.start_date, stats.end_date);
    //     println!("交易品种数: {}", stats.symbols_count);

    //     // 打印收益指标
    //     println!("夏普比率: {:.2}", stats.daily_performance.sharpe_ratio);

    //     // 打印交易指标
    //     println!("胜率: {:.2}%", stats.evaluate_pairs.win_rate * 100.0);
    //     println!(
    //         "盈亏比: {:.2}",
    //         stats.evaluate_pairs.total_profit_loss_ratio
    //     );
    // }
}
