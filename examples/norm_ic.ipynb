{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>F#EXAMPLE#DEFAULT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-08</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-09</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-10</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-11</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-14</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-10-15</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-10-16</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-10-17</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-10-18</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-10-21</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-10-22</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-10-23</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-10-24</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-10-25</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-10-28</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-10-29</td>\n", "      <td>-0.992906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-10-30</td>\n", "      <td>-1.412831</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-10-31</td>\n", "      <td>-1.412923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-10-08</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-10-09</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-10-10</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-10-11</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-10-14</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-10-15</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-10-16</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-10-17</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-10-18</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-10-21</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-10-22</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-10-23</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-10-24</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-10-25</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-10-28</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-10-29</td>\n", "      <td>1.368579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2024-10-30</td>\n", "      <td>0.760563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2024-10-31</td>\n", "      <td>0.758782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2024-10-08</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2024-10-09</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2024-10-10</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2024-10-11</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2024-10-14</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2024-10-15</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2024-10-16</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2024-10-17</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>2024-10-18</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2024-10-21</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2024-10-22</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2024-10-23</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>2024-10-24</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>2024-10-25</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>2024-10-28</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>2024-10-29</td>\n", "      <td>-0.375673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>2024-10-30</td>\n", "      <td>0.652268</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>2024-10-31</td>\n", "      <td>0.654141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            dt  F#EXAMPLE#DEFAULT\n", "0   2024-10-08          -0.992906\n", "1   2024-10-09          -0.992906\n", "2   2024-10-10          -0.992906\n", "3   2024-10-11          -0.992906\n", "4   2024-10-14          -0.992906\n", "5   2024-10-15          -0.992906\n", "6   2024-10-16          -0.992906\n", "7   2024-10-17          -0.992906\n", "8   2024-10-18          -0.992906\n", "9   2024-10-21          -0.992906\n", "10  2024-10-22          -0.992906\n", "11  2024-10-23          -0.992906\n", "12  2024-10-24          -0.992906\n", "13  2024-10-25          -0.992906\n", "14  2024-10-28          -0.992906\n", "15  2024-10-29          -0.992906\n", "16  2024-10-30          -1.412831\n", "17  2024-10-31          -1.412923\n", "18  2024-10-08           1.368579\n", "19  2024-10-09           1.368579\n", "20  2024-10-10           1.368579\n", "21  2024-10-11           1.368579\n", "22  2024-10-14           1.368579\n", "23  2024-10-15           1.368579\n", "24  2024-10-16           1.368579\n", "25  2024-10-17           1.368579\n", "26  2024-10-18           1.368579\n", "27  2024-10-21           1.368579\n", "28  2024-10-22           1.368579\n", "29  2024-10-23           1.368579\n", "30  2024-10-24           1.368579\n", "31  2024-10-25           1.368579\n", "32  2024-10-28           1.368579\n", "33  2024-10-29           1.368579\n", "34  2024-10-30           0.760563\n", "35  2024-10-31           0.758782\n", "36  2024-10-08          -0.375673\n", "37  2024-10-09          -0.375673\n", "38  2024-10-10          -0.375673\n", "39  2024-10-11          -0.375673\n", "40  2024-10-14          -0.375673\n", "41  2024-10-15          -0.375673\n", "42  2024-10-16          -0.375673\n", "43  2024-10-17          -0.375673\n", "44  2024-10-18          -0.375673\n", "45  2024-10-21          -0.375673\n", "46  2024-10-22          -0.375673\n", "47  2024-10-23          -0.375673\n", "48  2024-10-24          -0.375673\n", "49  2024-10-25          -0.375673\n", "50  2024-10-28          -0.375673\n", "51  2024-10-29          -0.375673\n", "52  2024-10-30           0.652268\n", "53  2024-10-31           0.654141"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import rs_czsc\n", "import pandas as pd\n", "df2 = pd.DataFrame({\n", "    \"dt\": [\n", "                \"2024-10-08\",\n", "                \"2024-10-09\",\n", "                \"2024-10-10\",\n", "                \"2024-10-11\",\n", "                \"2024-10-14\",\n", "                \"2024-10-15\",\n", "                \"2024-10-16\",\n", "                \"2024-10-17\",\n", "                \"2024-10-18\",\n", "                \"2024-10-21\",\n", "                \"2024-10-22\",\n", "                \"2024-10-23\",\n", "                \"2024-10-24\",\n", "                \"2024-10-25\",\n", "                \"2024-10-28\",\n", "                \"2024-10-29\",\n", "                \"2024-10-30\",\n", "                \"2024-10-31\",\n", "                \"2024-10-08\",\n", "                \"2024-10-09\",\n", "                \"2024-10-10\",\n", "                \"2024-10-11\",\n", "                \"2024-10-14\",\n", "                \"2024-10-15\",\n", "                \"2024-10-16\",\n", "                \"2024-10-17\",\n", "                \"2024-10-18\",\n", "                \"2024-10-21\",\n", "                \"2024-10-22\",\n", "                \"2024-10-23\",\n", "                \"2024-10-24\",\n", "                \"2024-10-25\",\n", "                \"2024-10-28\",\n", "                \"2024-10-29\",\n", "                \"2024-10-30\",\n", "                \"2024-10-31\",\n", "                \"2024-10-08\",\n", "                \"2024-10-09\",\n", "                \"2024-10-10\",\n", "                \"2024-10-11\",\n", "                \"2024-10-14\",\n", "                \"2024-10-15\",\n", "                \"2024-10-16\",\n", "                \"2024-10-17\",\n", "                \"2024-10-18\",\n", "                \"2024-10-21\",\n", "                \"2024-10-22\",\n", "                \"2024-10-23\",\n", "                \"2024-10-24\",\n", "                \"2024-10-25\",\n", "                \"2024-10-28\",\n", "                \"2024-10-29\",\n", "                \"2024-10-30\",\n", "                \"2024-10-31\",\n", "                ],\n", "    \"F#EXAMPLE#DEFAULT\": [\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        0.2901052792187935,\n", "        -2.8929516717878574,\n", "        -3.0094040987856756,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.5203675448649068,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "        0.3502900347871812,\n", "                    ]\n", "})\n", "\n", "x_col = \"F#EXAMPLE#DEFAULT\"\n", "rs_czsc.normalize_feature(df2, x_col)[[\"dt\", x_col]]"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}