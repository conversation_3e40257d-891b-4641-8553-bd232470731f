from rs_czsc import RawBar, Freq
from datetime import datetime, timedelta

from rs_czsc import BarGenerator, Market

bg = BarGenerator(Freq.F1, freqs=[Freq.F15], max_count=10, market=Market.AShare)

dt = datetime.now()

bars = []
count = 10

for i in range(count):
    cur_dt = dt + timedelta(minutes=i)
    unix_timestamp = int(cur_dt.timestamp())
    bar = RawBar("SHSE.000006", unix_timestamp, Freq.F1, 0, 0, 0, 0, 0, 0)
    bars.append(bar)

bg.init_freq_bars(freq=Freq.F1, bars=bars)

cur_dt = dt + timedelta(minutes=count)
unix_timestamp = int(cur_dt.timestamp())
bar = RawBar("SHSE.000006", unix_timestamp, Freq.F1, 0, 0, 0, 0, 0, 0)

bg.update(bar=bar)


print(bg.get_symbol())
print(bg.get_latest_date())
