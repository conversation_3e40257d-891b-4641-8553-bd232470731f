import time
import pandas as pd
from rs_czsc import WeightBacktest

df = pd.read_feather(r"A:\桌面临时数据\全A日线测试_20170101_20250429.feather")
symbols = df.groupby('symbol').size().sort_values(ascending=False).index.tolist()

for n in (500, 800, 1000, 1500, 2000, 3000, 4000, 5000):
    dfx = df[df['symbol'].isin(symbols[:n])].copy()
    start_time = time.time()
    wb = WeightBacktest(dfx[['dt', 'symbol', 'weight', 'price']], digits=2, n_jobs=1, weight_type='ts')
    print(f"\n\n{n} 个股票的回测运行时间: {time.time() - start_time:.2f} 秒\n")
    print(wb.stats)
