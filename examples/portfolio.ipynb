{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def get_daily_df():\n", "    \"\"\"\n", "    日收益数据: STK_001~STK_010, 20240101~20241201\n", "    \"\"\"\n", "    from czsc.connectors import cooperation as coo\n", "\n", "    # 相关策略名称\n", "    stk_strat = [f\"STK_{i:03d}\" for i in range(1, 6)]\n", "\n", "    rows = []\n", "    for s in stk_strat:\n", "        df = coo.dc.sub_strategy_dailys(strategy=s, symbol='A股选股', sdt=\"20240101\", edt=\"20241201\", v=2, ttl=-1)\n", "        df['dt'] = pd.to_datetime(df['dt']).dt.strftime(\"%Y-%m-%d\")\n", "        rows.append(df)\n", "    df = pd.concat(rows)\n", "    # 数据清洗\n", "    df_daily = pd.pivot_table(df, index='dt', columns='strategy', values='returns', aggfunc='sum').fillna(0)\n", "    df_daily = pd.melt(df_daily.reset_index(), id_vars='dt', var_name='strategy', value_name='returns') # type: ignore\n", "\n", "    df_daily[\"dt\"] = pd.to_datetime(df_daily[\"dt\"])\n", "    \n", "    return df_daily[\n", "        (df_daily[\"dt\"] >= \"2024-9-29\") &\n", "        (df_daily[\"dt\"] <= \"2024-11-30\")\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>strategy</th>\n", "      <th>config_weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-08 12:00:00</td>\n", "      <td>STK_001</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-08 12:00:00</td>\n", "      <td>STK_003</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-08 12:00:00</td>\n", "      <td>STK_002</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-11-08 12:00:00</td>\n", "      <td>STK_005</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-11-08 12:00:00</td>\n", "      <td>STK_003</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-11-08 12:00:00</td>\n", "      <td>STK_002</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   dt strategy  config_weight\n", "0 2024-10-08 12:00:00  STK_001            0.5\n", "1 2024-10-08 12:00:00  STK_003            0.3\n", "2 2024-10-08 12:00:00  STK_002            0.2\n", "3 2024-11-08 12:00:00  STK_005            0.5\n", "4 2024-11-08 12:00:00  STK_003            0.3\n", "5 2024-11-08 12:00:00  STK_002            0.2"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["adjust_records = {\n", "    \"2024-10-08T12:00:00\": {\n", "        \"STK_001\": 0.5,\n", "        \"STK_003\": 0.3,\n", "        \"STK_002\": 0.2,\n", "    },\n", "    \"2024-11-08T12:00:00\": {\n", "        \"STK_005\": 0.5,\n", "        \"STK_003\": 0.3,\n", "        \"STK_002\": 0.2,\n", "    },\n", "}\n", "\n", "records = []\n", "for dt, weights in adjust_records.items():\n", "    for strategy, weight in weights.items():\n", "        records.append({\n", "            \"dt\": dt,\n", "            \"strategy\": strategy,\n", "            \"config_weight\": weight,\n", "        })\n", "df = pd.DataFrame(records)\n", "df['dt'] = pd.to_datetime(df['dt'])\n", "df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["strategy             STK_001  STK_002  STK_003  STK_005\n", "dt                                                     \n", "2024-10-08 12:00:00      0.5      0.2      0.3      0.0\n", "2024-11-08 12:00:00      0.0      0.2      0.3      0.5\n"]}], "source": ["# dfc 描述某个*频率*下每个策略的权重变化\n", "dfc = pd.pivot_table(\n", "    df, \n", "    index='dt', # 索引为精确时间（含时分秒）\n", "    columns='strategy', \n", "    values='config_weight', \n", "    aggfunc='sum' # 仅对完全相同的(dt, strategy)求和\n", ").<PERSON>na(0)\n", "dfc.index = pd.to_datetime(dfc.index)\n", "\n", "print(dfc)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>strategy</th>\n", "      <th>STK_001</th>\n", "      <th>STK_002</th>\n", "      <th>STK_003</th>\n", "      <th>STK_005</th>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-08 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-09 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-10 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-12 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["strategy             STK_001  STK_002  STK_003  STK_005\n", "dt                                                     \n", "2024-10-08 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-09 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-10 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-11 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-12 12:00:00      0.5      0.2      0.3      0.0"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 dfc 改成描述*每天(自然日)*每个策略的权重变化\n", "start_date = df[\"dt\"].min()\n", "end_date = df['dt'].max()\n", "\n", "dfc = dfc.reindex(pd.date_range(start=start_date, end=end_date))\n", "dfc.index.name = 'dt'\n", "dfc = dfc.ffill().fillna(0)\n", "dfc.head(5)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>strategy</th>\n", "      <th>STK_001</th>\n", "      <th>STK_002</th>\n", "      <th>STK_003</th>\n", "      <th>STK_005</th>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-09 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-10 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-12 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-13 12:00:00</th>\n", "      <td>0.5</td>\n", "      <td>0.2</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["strategy             STK_001  STK_002  STK_003  STK_005\n", "dt                                                     \n", "2024-10-09 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-10 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-11 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-12 12:00:00      0.5      0.2      0.3      0.0\n", "2024-10-13 12:00:00      0.5      0.2      0.3      0.0"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# # 组合配置需要向后移动一天, 第二天开始生效\n", "dfc = dfc.shift(1).iloc[1:].copy()\n", "dfc.head(5)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>strategy</th>\n", "      <th>config_weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_001</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_001</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_001</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-12</td>\n", "      <td>STK_001</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-13</td>\n", "      <td>STK_001</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           dt strategy  config_weight\n", "0  2024-10-09  STK_001            0.5\n", "1  2024-10-10  STK_001            0.5\n", "2  2024-10-11  STK_001            0.5\n", "3  2024-10-12  STK_001            0.5\n", "4  2024-10-13  STK_001            0.5"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dfc = pd.melt(dfc.reset_index(), id_vars='dt', var_name='strategy', value_name='config_weight') # type: ignore\n", "dfc['dt'] = pd.to_datetime(dfc['dt']).dt.strftime(\"%Y-%m-%d\")\n", "dfc.head(5)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ta-lib 没有正确安装，将使用自定义分析函数。建议安装 ta-lib，可以大幅提升计算速度。请参考安装教程 https://blog.csdn.net/qaz2134560/article/details/98484091\n"]}, {"name": "stderr", "output_type": "stream", "text": ["e:\\Projects\\czscflow\\apps\\web-antd\\rs_czsc\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["请设置tushare pro的token凭证码，如果没有请访问https://tushare.pro注册申请\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-03-30 19:08:35.551\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.utils.data_client\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m68\u001b[0m - \u001b[1m数据URL: https://xapi.zbczsc.com:9106 数据缓存路径：E:\\Projects\\pyczsc-data 占用磁盘空间：52.00 MB\u001b[0m\n", "\u001b[32m2025-03-30 19:08:35.552\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.utils.data_client\u001b[0m:\u001b[36mpost_request\u001b[0m:\u001b[36m107\u001b[0m - \u001b[1m缓存命中 | API：sub_strategy_dailys；参数：{'strategy': 'STK_001', 'symbol': 'A股选股', 'sdt': '20240101', 'edt': '20241201', 'v': 2}；数据量：(220, 4)\u001b[0m\n", "\u001b[32m2025-03-30 19:08:35.554\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.utils.data_client\u001b[0m:\u001b[36mpost_request\u001b[0m:\u001b[36m107\u001b[0m - \u001b[1m缓存命中 | API：sub_strategy_dailys；参数：{'strategy': 'STK_002', 'symbol': 'A股选股', 'sdt': '20240101', 'edt': '20241201', 'v': 2}；数据量：(220, 4)\u001b[0m\n", "\u001b[32m2025-03-30 19:08:35.556\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.utils.data_client\u001b[0m:\u001b[36mpost_request\u001b[0m:\u001b[36m107\u001b[0m - \u001b[1m缓存命中 | API：sub_strategy_dailys；参数：{'strategy': 'STK_003', 'symbol': 'A股选股', 'sdt': '20240101', 'edt': '20241201', 'v': 2}；数据量：(220, 4)\u001b[0m\n", "\u001b[32m2025-03-30 19:08:35.558\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.utils.data_client\u001b[0m:\u001b[36mpost_request\u001b[0m:\u001b[36m107\u001b[0m - \u001b[1m缓存命中 | API：sub_strategy_dailys；参数：{'strategy': 'STK_004', 'symbol': 'A股选股', 'sdt': '20240101', 'edt': '20241201', 'v': 2}；数据量：(220, 4)\u001b[0m\n", "\u001b[32m2025-03-30 19:08:35.559\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mczsc.utils.data_client\u001b[0m:\u001b[36mpost_request\u001b[0m:\u001b[36m107\u001b[0m - \u001b[1m缓存命中 | API：sub_strategy_dailys；参数：{'strategy': 'STK_005', 'symbol': 'A股选股', 'sdt': '20240101', 'edt': '20241201', 'v': 2}；数据量：(220, 4)\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>strategy</th>\n", "      <th>returns</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_001</td>\n", "      <td>0.0667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_001</td>\n", "      <td>0.0716</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_001</td>\n", "      <td>-0.1005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0342</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_001</td>\n", "      <td>0.0125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_001</td>\n", "      <td>0.0173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_001</td>\n", "      <td>0.0034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_001</td>\n", "      <td>0.0103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_001</td>\n", "      <td>0.0461</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_001</td>\n", "      <td>0.0303</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_001</td>\n", "      <td>0.0294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_001</td>\n", "      <td>0.0441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_001</td>\n", "      <td>0.0241</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_001</td>\n", "      <td>0.0511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0386</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_001</td>\n", "      <td>0.0185</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_001</td>\n", "      <td>0.0296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>216</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>217</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>218</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_001</td>\n", "      <td>0.0103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>400</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_002</td>\n", "      <td>0.0692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_002</td>\n", "      <td>0.0464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>402</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>403</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>404</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>405</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>406</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>407</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>408</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>409</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>410</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_002</td>\n", "      <td>0.0013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>411</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_002</td>\n", "      <td>0.0278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>412</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_002</td>\n", "      <td>0.0011</td>\n", "    </tr>\n", "    <tr>\n", "      <th>413</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_002</td>\n", "      <td>0.0345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>414</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_002</td>\n", "      <td>0.0208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>415</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>417</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>418</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>419</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>420</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_002</td>\n", "      <td>0.0240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_002</td>\n", "      <td>0.0128</td>\n", "    </tr>\n", "    <tr>\n", "      <th>422</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_002</td>\n", "      <td>0.0029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>423</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_002</td>\n", "      <td>0.0407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>424</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>425</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_002</td>\n", "      <td>0.0080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>426</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>427</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>428</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0386</td>\n", "    </tr>\n", "    <tr>\n", "      <th>429</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>430</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>431</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_002</td>\n", "      <td>0.0083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>432</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_002</td>\n", "      <td>0.0228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>433</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_002</td>\n", "      <td>0.0295</td>\n", "    </tr>\n", "    <tr>\n", "      <th>434</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>435</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_002</td>\n", "      <td>0.0253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>436</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>437</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_002</td>\n", "      <td>0.0028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>438</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_002</td>\n", "      <td>0.0152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_002</td>\n", "      <td>0.0036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>620</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_003</td>\n", "      <td>0.0791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>621</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_003</td>\n", "      <td>0.0325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>622</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>623</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_003</td>\n", "      <td>0.0172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>624</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0163</td>\n", "    </tr>\n", "    <tr>\n", "      <th>625</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>626</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>627</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>628</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>629</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>630</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_003</td>\n", "      <td>0.0045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>631</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_003</td>\n", "      <td>0.0027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>632</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>633</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_003</td>\n", "      <td>0.0055</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_003</td>\n", "      <td>0.0309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>635</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>636</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>637</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>638</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>639</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>640</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_003</td>\n", "      <td>0.0278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>641</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_003</td>\n", "      <td>0.0088</td>\n", "    </tr>\n", "    <tr>\n", "      <th>642</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>643</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_003</td>\n", "      <td>0.0224</td>\n", "    </tr>\n", "    <tr>\n", "      <th>644</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>645</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_003</td>\n", "      <td>0.0074</td>\n", "    </tr>\n", "    <tr>\n", "      <th>646</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>647</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_003</td>\n", "      <td>0.0085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>648</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>649</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_003</td>\n", "      <td>0.0090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>650</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0347</td>\n", "    </tr>\n", "    <tr>\n", "      <th>651</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_003</td>\n", "      <td>0.0266</td>\n", "    </tr>\n", "    <tr>\n", "      <th>652</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_003</td>\n", "      <td>0.0110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>653</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_003</td>\n", "      <td>0.0035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>654</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0538</td>\n", "    </tr>\n", "    <tr>\n", "      <th>655</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_003</td>\n", "      <td>0.0356</td>\n", "    </tr>\n", "    <tr>\n", "      <th>656</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_003</td>\n", "      <td>0.0158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>657</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_003</td>\n", "      <td>0.0045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>658</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_003</td>\n", "      <td>0.0999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>659</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>840</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_004</td>\n", "      <td>0.0830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>841</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_004</td>\n", "      <td>0.0403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>842</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>843</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>844</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>845</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>846</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>847</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>848</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>849</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>850</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_004</td>\n", "      <td>0.0017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>851</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>852</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_004</td>\n", "      <td>0.0990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>853</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_004</td>\n", "      <td>0.0564</td>\n", "    </tr>\n", "    <tr>\n", "      <th>854</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>855</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>856</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>857</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>858</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>860</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_004</td>\n", "      <td>0.0263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>861</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_004</td>\n", "      <td>0.0265</td>\n", "    </tr>\n", "    <tr>\n", "      <th>862</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_004</td>\n", "      <td>0.0036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>863</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_004</td>\n", "      <td>0.0223</td>\n", "    </tr>\n", "    <tr>\n", "      <th>864</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_004</td>\n", "      <td>0.0079</td>\n", "    </tr>\n", "    <tr>\n", "      <th>865</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_004</td>\n", "      <td>0.0172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>866</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>867</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>868</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0254</td>\n", "    </tr>\n", "    <tr>\n", "      <th>869</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>870</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>871</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_004</td>\n", "      <td>0.0222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>872</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_004</td>\n", "      <td>0.0189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>873</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_004</td>\n", "      <td>0.0134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>874</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>875</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_004</td>\n", "      <td>0.0289</td>\n", "    </tr>\n", "    <tr>\n", "      <th>876</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>877</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_004</td>\n", "      <td>0.0132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>878</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0052</td>\n", "    </tr>\n", "    <tr>\n", "      <th>879</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_004</td>\n", "      <td>0.0117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1060</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1061</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1062</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1063</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1064</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1065</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1066</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1067</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1068</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0206</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1069</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_005</td>\n", "      <td>0.0090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1070</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_005</td>\n", "      <td>0.0967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1071</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_005</td>\n", "      <td>0.0990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1072</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_005</td>\n", "      <td>0.0038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1073</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_005</td>\n", "      <td>0.0385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1074</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_005</td>\n", "      <td>0.0992</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1075</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_005</td>\n", "      <td>0.0984</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1076</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_005</td>\n", "      <td>0.0735</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1077</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_005</td>\n", "      <td>0.1009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1078</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_005</td>\n", "      <td>0.0992</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1079</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1080</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_005</td>\n", "      <td>0.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1081</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_005</td>\n", "      <td>0.0101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1082</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_005</td>\n", "      <td>0.0122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1083</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_005</td>\n", "      <td>0.0560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1084</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1085</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1086</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1087</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1088</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1089</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1091</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_005</td>\n", "      <td>0.0226</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1092</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_005</td>\n", "      <td>0.0287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1093</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_005</td>\n", "      <td>0.0054</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1094</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1095</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_005</td>\n", "      <td>0.0264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1096</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_005</td>\n", "      <td>0.0118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1097</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1098</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_005</td>\n", "      <td>0.0212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1099</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_005</td>\n", "      <td>0.0374</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             dt strategy  returns\n", "180  2024-09-30  STK_001   0.0667\n", "181  2024-10-08  STK_001   0.0716\n", "182  2024-10-09  STK_001  -0.1005\n", "183  2024-10-10  STK_001  -0.0234\n", "184  2024-10-11  STK_001  -0.0342\n", "185  2024-10-14  STK_001  -0.0015\n", "186  2024-10-15  STK_001   0.0000\n", "187  2024-10-16  STK_001   0.0000\n", "188  2024-10-17  STK_001   0.0000\n", "189  2024-10-18  STK_001   0.0000\n", "190  2024-10-21  STK_001   0.0125\n", "191  2024-10-22  STK_001   0.0173\n", "192  2024-10-23  STK_001  -0.0102\n", "193  2024-10-24  STK_001   0.0034\n", "194  2024-10-25  STK_001   0.0103\n", "195  2024-10-28  STK_001   0.0461\n", "196  2024-10-29  STK_001  -0.0468\n", "197  2024-10-30  STK_001   0.0303\n", "198  2024-10-31  STK_001   0.0294\n", "199  2024-11-01  STK_001  -0.0252\n", "200  2024-11-04  STK_001  -0.0035\n", "201  2024-11-05  STK_001   0.0441\n", "202  2024-11-06  STK_001   0.0241\n", "203  2024-11-07  STK_001   0.0511\n", "204  2024-11-08  STK_001  -0.0386\n", "205  2024-11-11  STK_001   0.0185\n", "206  2024-11-12  STK_001  -0.0148\n", "207  2024-11-13  STK_001  -0.0102\n", "208  2024-11-14  STK_001  -0.0236\n", "209  2024-11-15  STK_001  -0.0015\n", "210  2024-11-18  STK_001   0.0000\n", "211  2024-11-19  STK_001   0.0000\n", "212  2024-11-20  STK_001   0.0000\n", "213  2024-11-21  STK_001   0.0000\n", "214  2024-11-22  STK_001   0.0000\n", "215  2024-11-25  STK_001   0.0296\n", "216  2024-11-26  STK_001  -0.0125\n", "217  2024-11-27  STK_001   0.0000\n", "218  2024-11-28  STK_001   0.0103\n", "219  2024-11-29  STK_001  -0.0051\n", "400  2024-09-30  STK_002   0.0692\n", "401  2024-10-08  STK_002   0.0464\n", "402  2024-10-09  STK_002  -0.0960\n", "403  2024-10-10  STK_002  -0.0035\n", "404  2024-10-11  STK_002  -0.0200\n", "405  2024-10-14  STK_002  -0.0015\n", "406  2024-10-15  STK_002   0.0000\n", "407  2024-10-16  STK_002   0.0000\n", "408  2024-10-17  STK_002   0.0000\n", "409  2024-10-18  STK_002   0.0000\n", "410  2024-10-21  STK_002   0.0013\n", "411  2024-10-22  STK_002   0.0278\n", "412  2024-10-23  STK_002   0.0011\n", "413  2024-10-24  STK_002   0.0345\n", "414  2024-10-25  STK_002   0.0208\n", "415  2024-10-28  STK_002  -0.0015\n", "416  2024-10-29  STK_002   0.0000\n", "417  2024-10-30  STK_002   0.0000\n", "418  2024-10-31  STK_002   0.0000\n", "419  2024-11-01  STK_002   0.0000\n", "420  2024-11-04  STK_002   0.0240\n", "421  2024-11-05  STK_002   0.0128\n", "422  2024-11-06  STK_002   0.0029\n", "423  2024-11-07  STK_002   0.0407\n", "424  2024-11-08  STK_002  -0.0233\n", "425  2024-11-11  STK_002   0.0080\n", "426  2024-11-12  STK_002  -0.0067\n", "427  2024-11-13  STK_002  -0.0130\n", "428  2024-11-14  STK_002  -0.0386\n", "429  2024-11-15  STK_002  -0.0196\n", "430  2024-11-18  STK_002  -0.0110\n", "431  2024-11-19  STK_002   0.0083\n", "432  2024-11-20  STK_002   0.0228\n", "433  2024-11-21  STK_002   0.0295\n", "434  2024-11-22  STK_002  -0.0299\n", "435  2024-11-25  STK_002   0.0253\n", "436  2024-11-26  STK_002  -0.0120\n", "437  2024-11-27  STK_002   0.0028\n", "438  2024-11-28  STK_002   0.0152\n", "439  2024-11-29  STK_002   0.0036\n", "620  2024-09-30  STK_003   0.0791\n", "621  2024-10-08  STK_003   0.0325\n", "622  2024-10-09  STK_003  -0.0676\n", "623  2024-10-10  STK_003   0.0172\n", "624  2024-10-11  STK_003  -0.0163\n", "625  2024-10-14  STK_003  -0.0015\n", "626  2024-10-15  STK_003   0.0000\n", "627  2024-10-16  STK_003   0.0000\n", "628  2024-10-17  STK_003   0.0000\n", "629  2024-10-18  STK_003   0.0000\n", "630  2024-10-21  STK_003   0.0045\n", "631  2024-10-22  STK_003   0.0027\n", "632  2024-10-23  STK_003  -0.0038\n", "633  2024-10-24  STK_003   0.0055\n", "634  2024-10-25  STK_003   0.0309\n", "635  2024-10-28  STK_003  -0.0015\n", "636  2024-10-29  STK_003   0.0000\n", "637  2024-10-30  STK_003   0.0000\n", "638  2024-10-31  STK_003   0.0000\n", "639  2024-11-01  STK_003   0.0000\n", "640  2024-11-04  STK_003   0.0278\n", "641  2024-11-05  STK_003   0.0088\n", "642  2024-11-06  STK_003   0.0000\n", "643  2024-11-07  STK_003   0.0224\n", "644  2024-11-08  STK_003  -0.0038\n", "645  2024-11-11  STK_003   0.0074\n", "646  2024-11-12  STK_003  -0.0056\n", "647  2024-11-13  STK_003   0.0085\n", "648  2024-11-14  STK_003  -0.0117\n", "649  2024-11-15  STK_003   0.0090\n", "650  2024-11-18  STK_003  -0.0347\n", "651  2024-11-19  STK_003   0.0266\n", "652  2024-11-20  STK_003   0.0110\n", "653  2024-11-21  STK_003   0.0035\n", "654  2024-11-22  STK_003  -0.0538\n", "655  2024-11-25  STK_003   0.0356\n", "656  2024-11-26  STK_003   0.0158\n", "657  2024-11-27  STK_003   0.0045\n", "658  2024-11-28  STK_003   0.0999\n", "659  2024-11-29  STK_003  -0.0250\n", "840  2024-09-30  STK_004   0.0830\n", "841  2024-10-08  STK_004   0.0403\n", "842  2024-10-09  STK_004  -0.0629\n", "843  2024-10-10  STK_004  -0.0170\n", "844  2024-10-11  STK_004  -0.0443\n", "845  2024-10-14  STK_004  -0.0015\n", "846  2024-10-15  STK_004   0.0000\n", "847  2024-10-16  STK_004   0.0000\n", "848  2024-10-17  STK_004   0.0000\n", "849  2024-10-18  STK_004   0.0000\n", "850  2024-10-21  STK_004   0.0017\n", "851  2024-10-22  STK_004  -0.0119\n", "852  2024-10-23  STK_004   0.0990\n", "853  2024-10-24  STK_004   0.0564\n", "854  2024-10-25  STK_004  -0.0194\n", "855  2024-10-28  STK_004  -0.0015\n", "856  2024-10-29  STK_004   0.0000\n", "857  2024-10-30  STK_004   0.0000\n", "858  2024-10-31  STK_004   0.0000\n", "859  2024-11-01  STK_004   0.0000\n", "860  2024-11-04  STK_004   0.0263\n", "861  2024-11-05  STK_004   0.0265\n", "862  2024-11-06  STK_004   0.0036\n", "863  2024-11-07  STK_004   0.0223\n", "864  2024-11-08  STK_004   0.0079\n", "865  2024-11-11  STK_004   0.0172\n", "866  2024-11-12  STK_004  -0.0075\n", "867  2024-11-13  STK_004  -0.0051\n", "868  2024-11-14  STK_004  -0.0254\n", "869  2024-11-15  STK_004  -0.0101\n", "870  2024-11-18  STK_004  -0.0250\n", "871  2024-11-19  STK_004   0.0222\n", "872  2024-11-20  STK_004   0.0189\n", "873  2024-11-21  STK_004   0.0134\n", "874  2024-11-22  STK_004  -0.0450\n", "875  2024-11-25  STK_004   0.0289\n", "876  2024-11-26  STK_004  -0.0032\n", "877  2024-11-27  STK_004   0.0132\n", "878  2024-11-28  STK_004  -0.0052\n", "879  2024-11-29  STK_004   0.0117\n", "1060 2024-09-30  STK_005  -0.0015\n", "1061 2024-10-08  STK_005   0.0000\n", "1062 2024-10-09  STK_005   0.0000\n", "1063 2024-10-10  STK_005   0.0000\n", "1064 2024-10-11  STK_005   0.0000\n", "1065 2024-10-14  STK_005  -0.0015\n", "1066 2024-10-15  STK_005  -0.0114\n", "1067 2024-10-16  STK_005  -0.0159\n", "1068 2024-10-17  STK_005  -0.0206\n", "1069 2024-10-18  STK_005   0.0090\n", "1070 2024-10-21  STK_005   0.0967\n", "1071 2024-10-22  STK_005   0.0990\n", "1072 2024-10-23  STK_005   0.0038\n", "1073 2024-10-24  STK_005   0.0385\n", "1074 2024-10-25  STK_005   0.0992\n", "1075 2024-10-28  STK_005   0.0984\n", "1076 2024-10-29  STK_005   0.0735\n", "1077 2024-10-30  STK_005   0.1009\n", "1078 2024-10-31  STK_005   0.0992\n", "1079 2024-11-01  STK_005  -0.0760\n", "1080 2024-11-04  STK_005   0.0200\n", "1081 2024-11-05  STK_005   0.0101\n", "1082 2024-11-06  STK_005   0.0122\n", "1083 2024-11-07  STK_005   0.0560\n", "1084 2024-11-08  STK_005  -0.0187\n", "1085 2024-11-11  STK_005  -0.0042\n", "1086 2024-11-12  STK_005  -0.0075\n", "1087 2024-11-13  STK_005   0.0000\n", "1088 2024-11-14  STK_005  -0.0236\n", "1089 2024-11-15  STK_005  -0.0143\n", "1090 2024-11-18  STK_005  -0.0123\n", "1091 2024-11-19  STK_005   0.0226\n", "1092 2024-11-20  STK_005   0.0287\n", "1093 2024-11-21  STK_005   0.0054\n", "1094 2024-11-22  STK_005  -0.0309\n", "1095 2024-11-25  STK_005   0.0264\n", "1096 2024-11-26  STK_005   0.0118\n", "1097 2024-11-27  STK_005   0.0000\n", "1098 2024-11-28  STK_005   0.0212\n", "1099 2024-11-29  STK_005   0.0374"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_daily = get_daily_df()\n", "df_daily"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"dt\": \"2024-09-30\",\n", "    \"returns\": 0.0667,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-08\",\n", "    \"returns\": 0.0716,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-09\",\n", "    \"returns\": -0.1005,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-10\",\n", "    \"returns\": -0.0234,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-11\",\n", "    \"returns\": -0.0342,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-14\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-15\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-16\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-17\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-18\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-21\",\n", "    \"returns\": 0.0125,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-22\",\n", "    \"returns\": 0.0173,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-23\",\n", "    \"returns\": -0.0102,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-24\",\n", "    \"returns\": 0.0034,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-25\",\n", "    \"returns\": 0.0103,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-28\",\n", "    \"returns\": 0.0461,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-29\",\n", "    \"returns\": -0.0468,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-30\",\n", "    \"returns\": 0.0303,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-31\",\n", "    \"returns\": 0.0294,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-01\",\n", "    \"returns\": -0.0252,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-04\",\n", "    \"returns\": -0.0035,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-05\",\n", "    \"returns\": 0.0441,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-06\",\n", "    \"returns\": 0.0241,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-07\",\n", "    \"returns\": 0.0511,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-08\",\n", "    \"returns\": -0.0386,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-11\",\n", "    \"returns\": 0.0185,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-12\",\n", "    \"returns\": -0.0148,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-13\",\n", "    \"returns\": -0.0102,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-14\",\n", "    \"returns\": -0.0236,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-15\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-18\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-19\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-20\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-21\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-22\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-25\",\n", "    \"returns\": 0.0296,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-26\",\n", "    \"returns\": -0.0125,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-27\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-28\",\n", "    \"returns\": 0.0103,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-29\",\n", "    \"returns\": -0.0051,\n", "    \"strategy\": \"STK_001\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-09-30\",\n", "    \"returns\": 0.0692,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-08\",\n", "    \"returns\": 0.0464,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-09\",\n", "    \"returns\": -0.096,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-10\",\n", "    \"returns\": -0.0035,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-11\",\n", "    \"returns\": -0.02,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-14\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-15\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-16\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-17\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-18\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-21\",\n", "    \"returns\": 0.0013,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-22\",\n", "    \"returns\": 0.0278,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-23\",\n", "    \"returns\": 0.0011,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-24\",\n", "    \"returns\": 0.0345,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-25\",\n", "    \"returns\": 0.0208,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-28\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-29\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-30\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-31\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-01\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-04\",\n", "    \"returns\": 0.024,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-05\",\n", "    \"returns\": 0.0128,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-06\",\n", "    \"returns\": 0.0029,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-07\",\n", "    \"returns\": 0.0407,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-08\",\n", "    \"returns\": -0.0233,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-11\",\n", "    \"returns\": 0.008,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-12\",\n", "    \"returns\": -0.0067,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-13\",\n", "    \"returns\": -0.013,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-14\",\n", "    \"returns\": -0.0386,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-15\",\n", "    \"returns\": -0.0196,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-18\",\n", "    \"returns\": -0.011,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-19\",\n", "    \"returns\": 0.0083,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-20\",\n", "    \"returns\": 0.0228,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-21\",\n", "    \"returns\": 0.0295,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-22\",\n", "    \"returns\": -0.0299,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-25\",\n", "    \"returns\": 0.0253,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-26\",\n", "    \"returns\": -0.012,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-27\",\n", "    \"returns\": 0.0028,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-28\",\n", "    \"returns\": 0.0152,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-29\",\n", "    \"returns\": 0.0036,\n", "    \"strategy\": \"STK_002\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-09-30\",\n", "    \"returns\": 0.0791,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-08\",\n", "    \"returns\": 0.0325,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-09\",\n", "    \"returns\": -0.0676,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-10\",\n", "    \"returns\": 0.0172,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-11\",\n", "    \"returns\": -0.0163,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-14\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-15\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-16\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-17\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-18\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-21\",\n", "    \"returns\": 0.0045,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-22\",\n", "    \"returns\": 0.0027,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-23\",\n", "    \"returns\": -0.0038,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-24\",\n", "    \"returns\": 0.0055,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-25\",\n", "    \"returns\": 0.0309,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-28\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-29\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-30\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-31\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-01\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-04\",\n", "    \"returns\": 0.0278,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-05\",\n", "    \"returns\": 0.0088,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-06\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-07\",\n", "    \"returns\": 0.0224,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-08\",\n", "    \"returns\": -0.0038,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-11\",\n", "    \"returns\": 0.0074,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-12\",\n", "    \"returns\": -0.0056,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-13\",\n", "    \"returns\": 0.0085,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-14\",\n", "    \"returns\": -0.0117,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-15\",\n", "    \"returns\": 0.009,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-18\",\n", "    \"returns\": -0.0347,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-19\",\n", "    \"returns\": 0.0266,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-20\",\n", "    \"returns\": 0.011,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-21\",\n", "    \"returns\": 0.0035,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-22\",\n", "    \"returns\": -0.0538,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-25\",\n", "    \"returns\": 0.0356,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-26\",\n", "    \"returns\": 0.0158,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-27\",\n", "    \"returns\": 0.0045,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-28\",\n", "    \"returns\": 0.0999,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-29\",\n", "    \"returns\": -0.025,\n", "    \"strategy\": \"STK_003\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-09-30\",\n", "    \"returns\": 0.083,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-08\",\n", "    \"returns\": 0.0403,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-09\",\n", "    \"returns\": -0.0629,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-10\",\n", "    \"returns\": -0.017,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-11\",\n", "    \"returns\": -0.0443,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-14\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-15\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-16\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-17\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-18\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-21\",\n", "    \"returns\": 0.0017,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-22\",\n", "    \"returns\": -0.0119,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-23\",\n", "    \"returns\": 0.099,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-24\",\n", "    \"returns\": 0.0564,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-25\",\n", "    \"returns\": -0.0194,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-28\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-29\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-30\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-31\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-01\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-04\",\n", "    \"returns\": 0.0263,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-05\",\n", "    \"returns\": 0.0265,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-06\",\n", "    \"returns\": 0.0036,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-07\",\n", "    \"returns\": 0.0223,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-08\",\n", "    \"returns\": 0.0079,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-11\",\n", "    \"returns\": 0.0172,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-12\",\n", "    \"returns\": -0.0075,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-13\",\n", "    \"returns\": -0.0051,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-14\",\n", "    \"returns\": -0.0254,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-15\",\n", "    \"returns\": -0.0101,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-18\",\n", "    \"returns\": -0.025,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-19\",\n", "    \"returns\": 0.0222,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-20\",\n", "    \"returns\": 0.0189,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-21\",\n", "    \"returns\": 0.0134,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-22\",\n", "    \"returns\": -0.045,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-25\",\n", "    \"returns\": 0.0289,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-26\",\n", "    \"returns\": -0.0032,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-27\",\n", "    \"returns\": 0.0132,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-28\",\n", "    \"returns\": -0.0052,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-29\",\n", "    \"returns\": 0.0117,\n", "    \"strategy\": \"STK_004\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-09-30\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-08\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-09\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-10\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-11\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-14\",\n", "    \"returns\": -0.0015,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-15\",\n", "    \"returns\": -0.0114,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-16\",\n", "    \"returns\": -0.0159,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-17\",\n", "    \"returns\": -0.0206,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-18\",\n", "    \"returns\": 0.009,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-21\",\n", "    \"returns\": 0.0967,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-22\",\n", "    \"returns\": 0.099,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-23\",\n", "    \"returns\": 0.0038,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-24\",\n", "    \"returns\": 0.0385,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-25\",\n", "    \"returns\": 0.0992,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-28\",\n", "    \"returns\": 0.0984,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-29\",\n", "    \"returns\": 0.0735,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-30\",\n", "    \"returns\": 0.1009,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-10-31\",\n", "    \"returns\": 0.0992,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-01\",\n", "    \"returns\": -0.076,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-04\",\n", "    \"returns\": 0.02,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-05\",\n", "    \"returns\": 0.0101,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-06\",\n", "    \"returns\": 0.0122,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-07\",\n", "    \"returns\": 0.056,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-08\",\n", "    \"returns\": -0.0187,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-11\",\n", "    \"returns\": -0.0042,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-12\",\n", "    \"returns\": -0.0075,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-13\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-14\",\n", "    \"returns\": -0.0236,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-15\",\n", "    \"returns\": -0.0143,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-18\",\n", "    \"returns\": -0.0123,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-19\",\n", "    \"returns\": 0.0226,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-20\",\n", "    \"returns\": 0.0287,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-21\",\n", "    \"returns\": 0.0054,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-22\",\n", "    \"returns\": -0.0309,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-25\",\n", "    \"returns\": 0.0264,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-26\",\n", "    \"returns\": 0.0118,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-27\",\n", "    \"returns\": 0.0,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-28\",\n", "    \"returns\": 0.0212,\n", "    \"strategy\": \"STK_005\"\n", "  },\n", "  {\n", "    \"dt\": \"2024-11-29\",\n", "    \"returns\": 0.0374,\n", "    \"strategy\": \"STK_005\"\n", "  }\n", "]\n"]}], "source": ["df_daily[\"dt\"] = df_daily[\"dt\"].astype(str)\n", "result_list = df_daily[['dt', 'strategy', 'returns']].values.tolist()\n", "\n", "formatted_entries = []\n", "for entry in result_list:\n", "    date, strategy, ret = entry\n", "    \n", "    formatted_entry = (\n", "        \"  {\\n\"\n", "        f'    \"dt\": \"{date}\",\\n'\n", "        f'    \"returns\": {ret},\\n'\n", "        f'    \"strategy\": \"{strategy}\"\\n'\n", "        \"  }\"\n", "    )\n", "    formatted_entries.append(formatted_entry)\n", "\n", "output = \"[\\n\" + \",\\n\".join(formatted_entries) + \"\\n]\"\n", "print(output)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dt</th>\n", "      <th>strategy</th>\n", "      <th>returns</th>\n", "      <th>config_weight</th>\n", "      <th>config_returns</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_001</td>\n", "      <td>0.0667</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_001</td>\n", "      <td>0.0716</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_001</td>\n", "      <td>-0.1005</td>\n", "      <td>0.5</td>\n", "      <td>-0.05025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0234</td>\n", "      <td>0.5</td>\n", "      <td>-0.01170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0342</td>\n", "      <td>0.5</td>\n", "      <td>-0.01710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0015</td>\n", "      <td>0.5</td>\n", "      <td>-0.00075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.5</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.5</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.5</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.5</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_001</td>\n", "      <td>0.0125</td>\n", "      <td>0.5</td>\n", "      <td>0.00625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_001</td>\n", "      <td>0.0173</td>\n", "      <td>0.5</td>\n", "      <td>0.00865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0102</td>\n", "      <td>0.5</td>\n", "      <td>-0.00510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_001</td>\n", "      <td>0.0034</td>\n", "      <td>0.5</td>\n", "      <td>0.00170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_001</td>\n", "      <td>0.0103</td>\n", "      <td>0.5</td>\n", "      <td>0.00515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_001</td>\n", "      <td>0.0461</td>\n", "      <td>0.5</td>\n", "      <td>0.02305</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0468</td>\n", "      <td>0.5</td>\n", "      <td>-0.02340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_001</td>\n", "      <td>0.0303</td>\n", "      <td>0.5</td>\n", "      <td>0.01515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_001</td>\n", "      <td>0.0294</td>\n", "      <td>0.5</td>\n", "      <td>0.01470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0252</td>\n", "      <td>0.5</td>\n", "      <td>-0.01260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0035</td>\n", "      <td>0.5</td>\n", "      <td>-0.00175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_001</td>\n", "      <td>0.0441</td>\n", "      <td>0.5</td>\n", "      <td>0.02205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_001</td>\n", "      <td>0.0241</td>\n", "      <td>0.5</td>\n", "      <td>0.01205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_001</td>\n", "      <td>0.0511</td>\n", "      <td>0.5</td>\n", "      <td>0.02555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0386</td>\n", "      <td>0.5</td>\n", "      <td>-0.01930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_001</td>\n", "      <td>0.0185</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0148</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0102</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0236</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0015</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_001</td>\n", "      <td>0.0296</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0125</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_001</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_001</td>\n", "      <td>0.0103</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_001</td>\n", "      <td>-0.0051</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_002</td>\n", "      <td>0.0692</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_002</td>\n", "      <td>0.0464</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0960</td>\n", "      <td>0.2</td>\n", "      <td>-0.01920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0035</td>\n", "      <td>0.2</td>\n", "      <td>-0.00070</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0200</td>\n", "      <td>0.2</td>\n", "      <td>-0.00400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0015</td>\n", "      <td>0.2</td>\n", "      <td>-0.00030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_002</td>\n", "      <td>0.0013</td>\n", "      <td>0.2</td>\n", "      <td>0.00026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_002</td>\n", "      <td>0.0278</td>\n", "      <td>0.2</td>\n", "      <td>0.00556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_002</td>\n", "      <td>0.0011</td>\n", "      <td>0.2</td>\n", "      <td>0.00022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_002</td>\n", "      <td>0.0345</td>\n", "      <td>0.2</td>\n", "      <td>0.00690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_002</td>\n", "      <td>0.0208</td>\n", "      <td>0.2</td>\n", "      <td>0.00416</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0015</td>\n", "      <td>0.2</td>\n", "      <td>-0.00030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_002</td>\n", "      <td>0.0000</td>\n", "      <td>0.2</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_002</td>\n", "      <td>0.0240</td>\n", "      <td>0.2</td>\n", "      <td>0.00480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_002</td>\n", "      <td>0.0128</td>\n", "      <td>0.2</td>\n", "      <td>0.00256</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_002</td>\n", "      <td>0.0029</td>\n", "      <td>0.2</td>\n", "      <td>0.00058</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_002</td>\n", "      <td>0.0407</td>\n", "      <td>0.2</td>\n", "      <td>0.00814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0233</td>\n", "      <td>0.2</td>\n", "      <td>-0.00466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_002</td>\n", "      <td>0.0080</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0067</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0130</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0386</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0196</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0110</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_002</td>\n", "      <td>0.0083</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_002</td>\n", "      <td>0.0228</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_002</td>\n", "      <td>0.0295</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0299</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_002</td>\n", "      <td>0.0253</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_002</td>\n", "      <td>-0.0120</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_002</td>\n", "      <td>0.0028</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_002</td>\n", "      <td>0.0152</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_002</td>\n", "      <td>0.0036</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_003</td>\n", "      <td>0.0791</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_003</td>\n", "      <td>0.0325</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0676</td>\n", "      <td>0.3</td>\n", "      <td>-0.02028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_003</td>\n", "      <td>0.0172</td>\n", "      <td>0.3</td>\n", "      <td>0.00516</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0163</td>\n", "      <td>0.3</td>\n", "      <td>-0.00489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0015</td>\n", "      <td>0.3</td>\n", "      <td>-0.00045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_003</td>\n", "      <td>0.0045</td>\n", "      <td>0.3</td>\n", "      <td>0.00135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_003</td>\n", "      <td>0.0027</td>\n", "      <td>0.3</td>\n", "      <td>0.00081</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0038</td>\n", "      <td>0.3</td>\n", "      <td>-0.00114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_003</td>\n", "      <td>0.0055</td>\n", "      <td>0.3</td>\n", "      <td>0.00165</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_003</td>\n", "      <td>0.0309</td>\n", "      <td>0.3</td>\n", "      <td>0.00927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0015</td>\n", "      <td>0.3</td>\n", "      <td>-0.00045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_003</td>\n", "      <td>0.0278</td>\n", "      <td>0.3</td>\n", "      <td>0.00834</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_003</td>\n", "      <td>0.0088</td>\n", "      <td>0.3</td>\n", "      <td>0.00264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_003</td>\n", "      <td>0.0000</td>\n", "      <td>0.3</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_003</td>\n", "      <td>0.0224</td>\n", "      <td>0.3</td>\n", "      <td>0.00672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0038</td>\n", "      <td>0.3</td>\n", "      <td>-0.00114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_003</td>\n", "      <td>0.0074</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0056</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_003</td>\n", "      <td>0.0085</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0117</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_003</td>\n", "      <td>0.0090</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0347</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_003</td>\n", "      <td>0.0266</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_003</td>\n", "      <td>0.0110</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_003</td>\n", "      <td>0.0035</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0538</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_003</td>\n", "      <td>0.0356</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_003</td>\n", "      <td>0.0158</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_003</td>\n", "      <td>0.0045</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_003</td>\n", "      <td>0.0999</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_003</td>\n", "      <td>-0.0250</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_004</td>\n", "      <td>0.0830</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_004</td>\n", "      <td>0.0403</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0629</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0170</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0443</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0015</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_004</td>\n", "      <td>0.0017</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0119</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>132</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_004</td>\n", "      <td>0.0990</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_004</td>\n", "      <td>0.0564</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0194</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0015</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_004</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_004</td>\n", "      <td>0.0263</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_004</td>\n", "      <td>0.0265</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_004</td>\n", "      <td>0.0036</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_004</td>\n", "      <td>0.0223</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_004</td>\n", "      <td>0.0079</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_004</td>\n", "      <td>0.0172</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0075</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0051</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0254</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0101</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0250</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_004</td>\n", "      <td>0.0222</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_004</td>\n", "      <td>0.0189</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_004</td>\n", "      <td>0.0134</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0450</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_004</td>\n", "      <td>0.0289</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0032</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_004</td>\n", "      <td>0.0132</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_004</td>\n", "      <td>-0.0052</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_004</td>\n", "      <td>0.0117</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>2024-09-30</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0015</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>2024-10-08</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>2024-10-09</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>2024-10-10</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2024-10-11</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>2024-10-14</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0015</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>2024-10-15</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0114</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>2024-10-16</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0159</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>2024-10-17</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0206</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>2024-10-18</td>\n", "      <td>STK_005</td>\n", "      <td>0.0090</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>2024-10-21</td>\n", "      <td>STK_005</td>\n", "      <td>0.0967</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>2024-10-22</td>\n", "      <td>STK_005</td>\n", "      <td>0.0990</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>2024-10-23</td>\n", "      <td>STK_005</td>\n", "      <td>0.0038</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>2024-10-24</td>\n", "      <td>STK_005</td>\n", "      <td>0.0385</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>2024-10-25</td>\n", "      <td>STK_005</td>\n", "      <td>0.0992</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>175</th>\n", "      <td>2024-10-28</td>\n", "      <td>STK_005</td>\n", "      <td>0.0984</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>2024-10-29</td>\n", "      <td>STK_005</td>\n", "      <td>0.0735</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>2024-10-30</td>\n", "      <td>STK_005</td>\n", "      <td>0.1009</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>2024-10-31</td>\n", "      <td>STK_005</td>\n", "      <td>0.0992</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>2024-11-01</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0760</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>2024-11-04</td>\n", "      <td>STK_005</td>\n", "      <td>0.0200</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>2024-11-05</td>\n", "      <td>STK_005</td>\n", "      <td>0.0101</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>2024-11-06</td>\n", "      <td>STK_005</td>\n", "      <td>0.0122</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>2024-11-07</td>\n", "      <td>STK_005</td>\n", "      <td>0.0560</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>2024-11-08</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0187</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>2024-11-11</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0042</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>2024-11-12</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0075</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>2024-11-13</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2024-11-14</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0236</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2024-11-15</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0143</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>2024-11-18</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0123</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>2024-11-19</td>\n", "      <td>STK_005</td>\n", "      <td>0.0226</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>2024-11-20</td>\n", "      <td>STK_005</td>\n", "      <td>0.0287</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>2024-11-21</td>\n", "      <td>STK_005</td>\n", "      <td>0.0054</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>2024-11-22</td>\n", "      <td>STK_005</td>\n", "      <td>-0.0309</td>\n", "      <td>0.0</td>\n", "      <td>-0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>2024-11-25</td>\n", "      <td>STK_005</td>\n", "      <td>0.0264</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>2024-11-26</td>\n", "      <td>STK_005</td>\n", "      <td>0.0118</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>2024-11-27</td>\n", "      <td>STK_005</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>2024-11-28</td>\n", "      <td>STK_005</td>\n", "      <td>0.0212</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>2024-11-29</td>\n", "      <td>STK_005</td>\n", "      <td>0.0374</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             dt strategy  returns  config_weight  config_returns\n", "0    2024-09-30  STK_001   0.0667            0.0         0.00000\n", "1    2024-10-08  STK_001   0.0716            0.0         0.00000\n", "2    2024-10-09  STK_001  -0.1005            0.5        -0.05025\n", "3    2024-10-10  STK_001  -0.0234            0.5        -0.01170\n", "4    2024-10-11  STK_001  -0.0342            0.5        -0.01710\n", "5    2024-10-14  STK_001  -0.0015            0.5        -0.00075\n", "6    2024-10-15  STK_001   0.0000            0.5         0.00000\n", "7    2024-10-16  STK_001   0.0000            0.5         0.00000\n", "8    2024-10-17  STK_001   0.0000            0.5         0.00000\n", "9    2024-10-18  STK_001   0.0000            0.5         0.00000\n", "10   2024-10-21  STK_001   0.0125            0.5         0.00625\n", "11   2024-10-22  STK_001   0.0173            0.5         0.00865\n", "12   2024-10-23  STK_001  -0.0102            0.5        -0.00510\n", "13   2024-10-24  STK_001   0.0034            0.5         0.00170\n", "14   2024-10-25  STK_001   0.0103            0.5         0.00515\n", "15   2024-10-28  STK_001   0.0461            0.5         0.02305\n", "16   2024-10-29  STK_001  -0.0468            0.5        -0.02340\n", "17   2024-10-30  STK_001   0.0303            0.5         0.01515\n", "18   2024-10-31  STK_001   0.0294            0.5         0.01470\n", "19   2024-11-01  STK_001  -0.0252            0.5        -0.01260\n", "20   2024-11-04  STK_001  -0.0035            0.5        -0.00175\n", "21   2024-11-05  STK_001   0.0441            0.5         0.02205\n", "22   2024-11-06  STK_001   0.0241            0.5         0.01205\n", "23   2024-11-07  STK_001   0.0511            0.5         0.02555\n", "24   2024-11-08  STK_001  -0.0386            0.5        -0.01930\n", "25   2024-11-11  STK_001   0.0185            0.0         0.00000\n", "26   2024-11-12  STK_001  -0.0148            0.0        -0.00000\n", "27   2024-11-13  STK_001  -0.0102            0.0        -0.00000\n", "28   2024-11-14  STK_001  -0.0236            0.0        -0.00000\n", "29   2024-11-15  STK_001  -0.0015            0.0        -0.00000\n", "30   2024-11-18  STK_001   0.0000            0.0         0.00000\n", "31   2024-11-19  STK_001   0.0000            0.0         0.00000\n", "32   2024-11-20  STK_001   0.0000            0.0         0.00000\n", "33   2024-11-21  STK_001   0.0000            0.0         0.00000\n", "34   2024-11-22  STK_001   0.0000            0.0         0.00000\n", "35   2024-11-25  STK_001   0.0296            0.0         0.00000\n", "36   2024-11-26  STK_001  -0.0125            0.0        -0.00000\n", "37   2024-11-27  STK_001   0.0000            0.0         0.00000\n", "38   2024-11-28  STK_001   0.0103            0.0         0.00000\n", "39   2024-11-29  STK_001  -0.0051            0.0        -0.00000\n", "40   2024-09-30  STK_002   0.0692            0.0         0.00000\n", "41   2024-10-08  STK_002   0.0464            0.0         0.00000\n", "42   2024-10-09  STK_002  -0.0960            0.2        -0.01920\n", "43   2024-10-10  STK_002  -0.0035            0.2        -0.00070\n", "44   2024-10-11  STK_002  -0.0200            0.2        -0.00400\n", "45   2024-10-14  STK_002  -0.0015            0.2        -0.00030\n", "46   2024-10-15  STK_002   0.0000            0.2         0.00000\n", "47   2024-10-16  STK_002   0.0000            0.2         0.00000\n", "48   2024-10-17  STK_002   0.0000            0.2         0.00000\n", "49   2024-10-18  STK_002   0.0000            0.2         0.00000\n", "50   2024-10-21  STK_002   0.0013            0.2         0.00026\n", "51   2024-10-22  STK_002   0.0278            0.2         0.00556\n", "52   2024-10-23  STK_002   0.0011            0.2         0.00022\n", "53   2024-10-24  STK_002   0.0345            0.2         0.00690\n", "54   2024-10-25  STK_002   0.0208            0.2         0.00416\n", "55   2024-10-28  STK_002  -0.0015            0.2        -0.00030\n", "56   2024-10-29  STK_002   0.0000            0.2         0.00000\n", "57   2024-10-30  STK_002   0.0000            0.2         0.00000\n", "58   2024-10-31  STK_002   0.0000            0.2         0.00000\n", "59   2024-11-01  STK_002   0.0000            0.2         0.00000\n", "60   2024-11-04  STK_002   0.0240            0.2         0.00480\n", "61   2024-11-05  STK_002   0.0128            0.2         0.00256\n", "62   2024-11-06  STK_002   0.0029            0.2         0.00058\n", "63   2024-11-07  STK_002   0.0407            0.2         0.00814\n", "64   2024-11-08  STK_002  -0.0233            0.2        -0.00466\n", "65   2024-11-11  STK_002   0.0080            0.0         0.00000\n", "66   2024-11-12  STK_002  -0.0067            0.0        -0.00000\n", "67   2024-11-13  STK_002  -0.0130            0.0        -0.00000\n", "68   2024-11-14  STK_002  -0.0386            0.0        -0.00000\n", "69   2024-11-15  STK_002  -0.0196            0.0        -0.00000\n", "70   2024-11-18  STK_002  -0.0110            0.0        -0.00000\n", "71   2024-11-19  STK_002   0.0083            0.0         0.00000\n", "72   2024-11-20  STK_002   0.0228            0.0         0.00000\n", "73   2024-11-21  STK_002   0.0295            0.0         0.00000\n", "74   2024-11-22  STK_002  -0.0299            0.0        -0.00000\n", "75   2024-11-25  STK_002   0.0253            0.0         0.00000\n", "76   2024-11-26  STK_002  -0.0120            0.0        -0.00000\n", "77   2024-11-27  STK_002   0.0028            0.0         0.00000\n", "78   2024-11-28  STK_002   0.0152            0.0         0.00000\n", "79   2024-11-29  STK_002   0.0036            0.0         0.00000\n", "80   2024-09-30  STK_003   0.0791            0.0         0.00000\n", "81   2024-10-08  STK_003   0.0325            0.0         0.00000\n", "82   2024-10-09  STK_003  -0.0676            0.3        -0.02028\n", "83   2024-10-10  STK_003   0.0172            0.3         0.00516\n", "84   2024-10-11  STK_003  -0.0163            0.3        -0.00489\n", "85   2024-10-14  STK_003  -0.0015            0.3        -0.00045\n", "86   2024-10-15  STK_003   0.0000            0.3         0.00000\n", "87   2024-10-16  STK_003   0.0000            0.3         0.00000\n", "88   2024-10-17  STK_003   0.0000            0.3         0.00000\n", "89   2024-10-18  STK_003   0.0000            0.3         0.00000\n", "90   2024-10-21  STK_003   0.0045            0.3         0.00135\n", "91   2024-10-22  STK_003   0.0027            0.3         0.00081\n", "92   2024-10-23  STK_003  -0.0038            0.3        -0.00114\n", "93   2024-10-24  STK_003   0.0055            0.3         0.00165\n", "94   2024-10-25  STK_003   0.0309            0.3         0.00927\n", "95   2024-10-28  STK_003  -0.0015            0.3        -0.00045\n", "96   2024-10-29  STK_003   0.0000            0.3         0.00000\n", "97   2024-10-30  STK_003   0.0000            0.3         0.00000\n", "98   2024-10-31  STK_003   0.0000            0.3         0.00000\n", "99   2024-11-01  STK_003   0.0000            0.3         0.00000\n", "100  2024-11-04  STK_003   0.0278            0.3         0.00834\n", "101  2024-11-05  STK_003   0.0088            0.3         0.00264\n", "102  2024-11-06  STK_003   0.0000            0.3         0.00000\n", "103  2024-11-07  STK_003   0.0224            0.3         0.00672\n", "104  2024-11-08  STK_003  -0.0038            0.3        -0.00114\n", "105  2024-11-11  STK_003   0.0074            0.0         0.00000\n", "106  2024-11-12  STK_003  -0.0056            0.0        -0.00000\n", "107  2024-11-13  STK_003   0.0085            0.0         0.00000\n", "108  2024-11-14  STK_003  -0.0117            0.0        -0.00000\n", "109  2024-11-15  STK_003   0.0090            0.0         0.00000\n", "110  2024-11-18  STK_003  -0.0347            0.0        -0.00000\n", "111  2024-11-19  STK_003   0.0266            0.0         0.00000\n", "112  2024-11-20  STK_003   0.0110            0.0         0.00000\n", "113  2024-11-21  STK_003   0.0035            0.0         0.00000\n", "114  2024-11-22  STK_003  -0.0538            0.0        -0.00000\n", "115  2024-11-25  STK_003   0.0356            0.0         0.00000\n", "116  2024-11-26  STK_003   0.0158            0.0         0.00000\n", "117  2024-11-27  STK_003   0.0045            0.0         0.00000\n", "118  2024-11-28  STK_003   0.0999            0.0         0.00000\n", "119  2024-11-29  STK_003  -0.0250            0.0        -0.00000\n", "120  2024-09-30  STK_004   0.0830            0.0         0.00000\n", "121  2024-10-08  STK_004   0.0403            0.0         0.00000\n", "122  2024-10-09  STK_004  -0.0629            0.0        -0.00000\n", "123  2024-10-10  STK_004  -0.0170            0.0        -0.00000\n", "124  2024-10-11  STK_004  -0.0443            0.0        -0.00000\n", "125  2024-10-14  STK_004  -0.0015            0.0        -0.00000\n", "126  2024-10-15  STK_004   0.0000            0.0         0.00000\n", "127  2024-10-16  STK_004   0.0000            0.0         0.00000\n", "128  2024-10-17  STK_004   0.0000            0.0         0.00000\n", "129  2024-10-18  STK_004   0.0000            0.0         0.00000\n", "130  2024-10-21  STK_004   0.0017            0.0         0.00000\n", "131  2024-10-22  STK_004  -0.0119            0.0        -0.00000\n", "132  2024-10-23  STK_004   0.0990            0.0         0.00000\n", "133  2024-10-24  STK_004   0.0564            0.0         0.00000\n", "134  2024-10-25  STK_004  -0.0194            0.0        -0.00000\n", "135  2024-10-28  STK_004  -0.0015            0.0        -0.00000\n", "136  2024-10-29  STK_004   0.0000            0.0         0.00000\n", "137  2024-10-30  STK_004   0.0000            0.0         0.00000\n", "138  2024-10-31  STK_004   0.0000            0.0         0.00000\n", "139  2024-11-01  STK_004   0.0000            0.0         0.00000\n", "140  2024-11-04  STK_004   0.0263            0.0         0.00000\n", "141  2024-11-05  STK_004   0.0265            0.0         0.00000\n", "142  2024-11-06  STK_004   0.0036            0.0         0.00000\n", "143  2024-11-07  STK_004   0.0223            0.0         0.00000\n", "144  2024-11-08  STK_004   0.0079            0.0         0.00000\n", "145  2024-11-11  STK_004   0.0172            0.0         0.00000\n", "146  2024-11-12  STK_004  -0.0075            0.0        -0.00000\n", "147  2024-11-13  STK_004  -0.0051            0.0        -0.00000\n", "148  2024-11-14  STK_004  -0.0254            0.0        -0.00000\n", "149  2024-11-15  STK_004  -0.0101            0.0        -0.00000\n", "150  2024-11-18  STK_004  -0.0250            0.0        -0.00000\n", "151  2024-11-19  STK_004   0.0222            0.0         0.00000\n", "152  2024-11-20  STK_004   0.0189            0.0         0.00000\n", "153  2024-11-21  STK_004   0.0134            0.0         0.00000\n", "154  2024-11-22  STK_004  -0.0450            0.0        -0.00000\n", "155  2024-11-25  STK_004   0.0289            0.0         0.00000\n", "156  2024-11-26  STK_004  -0.0032            0.0        -0.00000\n", "157  2024-11-27  STK_004   0.0132            0.0         0.00000\n", "158  2024-11-28  STK_004  -0.0052            0.0        -0.00000\n", "159  2024-11-29  STK_004   0.0117            0.0         0.00000\n", "160  2024-09-30  STK_005  -0.0015            0.0        -0.00000\n", "161  2024-10-08  STK_005   0.0000            0.0         0.00000\n", "162  2024-10-09  STK_005   0.0000            0.0         0.00000\n", "163  2024-10-10  STK_005   0.0000            0.0         0.00000\n", "164  2024-10-11  STK_005   0.0000            0.0         0.00000\n", "165  2024-10-14  STK_005  -0.0015            0.0        -0.00000\n", "166  2024-10-15  STK_005  -0.0114            0.0        -0.00000\n", "167  2024-10-16  STK_005  -0.0159            0.0        -0.00000\n", "168  2024-10-17  STK_005  -0.0206            0.0        -0.00000\n", "169  2024-10-18  STK_005   0.0090            0.0         0.00000\n", "170  2024-10-21  STK_005   0.0967            0.0         0.00000\n", "171  2024-10-22  STK_005   0.0990            0.0         0.00000\n", "172  2024-10-23  STK_005   0.0038            0.0         0.00000\n", "173  2024-10-24  STK_005   0.0385            0.0         0.00000\n", "174  2024-10-25  STK_005   0.0992            0.0         0.00000\n", "175  2024-10-28  STK_005   0.0984            0.0         0.00000\n", "176  2024-10-29  STK_005   0.0735            0.0         0.00000\n", "177  2024-10-30  STK_005   0.1009            0.0         0.00000\n", "178  2024-10-31  STK_005   0.0992            0.0         0.00000\n", "179  2024-11-01  STK_005  -0.0760            0.0        -0.00000\n", "180  2024-11-04  STK_005   0.0200            0.0         0.00000\n", "181  2024-11-05  STK_005   0.0101            0.0         0.00000\n", "182  2024-11-06  STK_005   0.0122            0.0         0.00000\n", "183  2024-11-07  STK_005   0.0560            0.0         0.00000\n", "184  2024-11-08  STK_005  -0.0187            0.0        -0.00000\n", "185  2024-11-11  STK_005  -0.0042            0.0        -0.00000\n", "186  2024-11-12  STK_005  -0.0075            0.0        -0.00000\n", "187  2024-11-13  STK_005   0.0000            0.0         0.00000\n", "188  2024-11-14  STK_005  -0.0236            0.0        -0.00000\n", "189  2024-11-15  STK_005  -0.0143            0.0        -0.00000\n", "190  2024-11-18  STK_005  -0.0123            0.0        -0.00000\n", "191  2024-11-19  STK_005   0.0226            0.0         0.00000\n", "192  2024-11-20  STK_005   0.0287            0.0         0.00000\n", "193  2024-11-21  STK_005   0.0054            0.0         0.00000\n", "194  2024-11-22  STK_005  -0.0309            0.0        -0.00000\n", "195  2024-11-25  STK_005   0.0264            0.0         0.00000\n", "196  2024-11-26  STK_005   0.0118            0.0         0.00000\n", "197  2024-11-27  STK_005   0.0000            0.0         0.00000\n", "198  2024-11-28  STK_005   0.0212            0.0         0.00000\n", "199  2024-11-29  STK_005   0.0374            0.0         0.00000"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 合并日收益数据和组合配置数据\n", "# 日期以日收益数据为准\n", "df = pd.merge(df_daily, dfc, on=['dt', 'strategy'], how='left')\n", "df['config_weight'] = df['config_weight'].fillna(0)\n", "df['config_returns'] = df['returns'] * df['config_weight']\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>strategy</th>\n", "      <th>STK_001</th>\n", "      <th>STK_002</th>\n", "      <th>STK_003</th>\n", "      <th>STK_004</th>\n", "      <th>STK_005</th>\n", "      <th>portfolio_returns</th>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-08</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-09</th>\n", "      <td>-0.05025</td>\n", "      <td>-0.01920</td>\n", "      <td>-0.02028</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.08973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-10</th>\n", "      <td>-0.01170</td>\n", "      <td>-0.00070</td>\n", "      <td>0.00516</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.00724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11</th>\n", "      <td>-0.01710</td>\n", "      <td>-0.00400</td>\n", "      <td>-0.00489</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.02599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-14</th>\n", "      <td>-0.00075</td>\n", "      <td>-0.00030</td>\n", "      <td>-0.00045</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.00150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>0.00625</td>\n", "      <td>0.00026</td>\n", "      <td>0.00135</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-22</th>\n", "      <td>0.00865</td>\n", "      <td>0.00556</td>\n", "      <td>0.00081</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01502</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-23</th>\n", "      <td>-0.00510</td>\n", "      <td>0.00022</td>\n", "      <td>-0.00114</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.00602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-24</th>\n", "      <td>0.00170</td>\n", "      <td>0.00690</td>\n", "      <td>0.00165</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-25</th>\n", "      <td>0.00515</td>\n", "      <td>0.00416</td>\n", "      <td>0.00927</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01858</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-28</th>\n", "      <td>0.02305</td>\n", "      <td>-0.00030</td>\n", "      <td>-0.00045</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.02230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-29</th>\n", "      <td>-0.02340</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.02340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-30</th>\n", "      <td>0.01515</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-31</th>\n", "      <td>0.01470</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-01</th>\n", "      <td>-0.01260</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.01260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-04</th>\n", "      <td>-0.00175</td>\n", "      <td>0.00480</td>\n", "      <td>0.00834</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-05</th>\n", "      <td>0.02205</td>\n", "      <td>0.00256</td>\n", "      <td>0.00264</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.02725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-06</th>\n", "      <td>0.01205</td>\n", "      <td>0.00058</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.01263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-07</th>\n", "      <td>0.02555</td>\n", "      <td>0.00814</td>\n", "      <td>0.00672</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.04041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-08</th>\n", "      <td>-0.01930</td>\n", "      <td>-0.00466</td>\n", "      <td>-0.00114</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.02510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-11</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-12</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-13</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-14</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-15</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-18</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-19</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-20</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-21</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-22</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-25</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-26</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-27</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-28</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-29</th>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["strategy    STK_001  STK_002  STK_003  STK_004  STK_005  portfolio_returns\n", "dt                                                                        \n", "2024-09-30  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-10-08  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-10-09 -0.05025 -0.01920 -0.02028      0.0      0.0           -0.08973\n", "2024-10-10 -0.01170 -0.00070  0.00516      0.0      0.0           -0.00724\n", "2024-10-11 -0.01710 -0.00400 -0.00489      0.0      0.0           -0.02599\n", "2024-10-14 -0.00075 -0.00030 -0.00045      0.0      0.0           -0.00150\n", "2024-10-15  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-10-16  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-10-17  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-10-18  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-10-21  0.00625  0.00026  0.00135      0.0      0.0            0.00786\n", "2024-10-22  0.00865  0.00556  0.00081      0.0      0.0            0.01502\n", "2024-10-23 -0.00510  0.00022 -0.00114      0.0      0.0           -0.00602\n", "2024-10-24  0.00170  0.00690  0.00165      0.0      0.0            0.01025\n", "2024-10-25  0.00515  0.00416  0.00927      0.0      0.0            0.01858\n", "2024-10-28  0.02305 -0.00030 -0.00045      0.0      0.0            0.02230\n", "2024-10-29 -0.02340  0.00000  0.00000      0.0      0.0           -0.02340\n", "2024-10-30  0.01515  0.00000  0.00000      0.0      0.0            0.01515\n", "2024-10-31  0.01470  0.00000  0.00000      0.0      0.0            0.01470\n", "2024-11-01 -0.01260  0.00000  0.00000      0.0      0.0           -0.01260\n", "2024-11-04 -0.00175  0.00480  0.00834      0.0      0.0            0.01139\n", "2024-11-05  0.02205  0.00256  0.00264      0.0      0.0            0.02725\n", "2024-11-06  0.01205  0.00058  0.00000      0.0      0.0            0.01263\n", "2024-11-07  0.02555  0.00814  0.00672      0.0      0.0            0.04041\n", "2024-11-08 -0.01930 -0.00466 -0.00114      0.0      0.0           -0.02510\n", "2024-11-11  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-12  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-13  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-14  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-15  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-18  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-19  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-20  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-21  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-22  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-25  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-26  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-27  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-28  0.00000  0.00000  0.00000      0.0      0.0            0.00000\n", "2024-11-29  0.00000  0.00000  0.00000      0.0      0.0            0.00000"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算组合收益\n", "daily = pd.pivot_table(df, index='dt', columns='strategy', values='config_returns', aggfunc='sum').fillna(0)\n", "daily['portfolio_returns'] = daily.sum(axis=1)\n", "daily.index = pd.to_datetime(daily.index)\n", "\n", "# 描述 每日(交易日) *组合下*每个策略的收益，和组合后的收益\n", "daily"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}