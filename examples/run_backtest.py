import pandas as pd
import time

from rs_czsc import WeightBacktest


dfw = pd.read_feather("../examples/run_backtest/data/weight_example.feather")
dfw = dfw[dfw['symbol'].isin(['ZZSF9001', 'DLj9001', 'SQag9001'])].copy().reset_index(drop=True)

start_time = time.time()

wb = WeightBacktest(dfw)

end_time = time.time()
print(f"Execution time: {end_time - start_time} seconds")

print(wb.stats)
print("最赚钱的品种：", wb.get_top_symbols(n=1, kind="profit"))
print("最亏钱的品种：", wb.get_top_symbols(n=1, kind="loss"))

