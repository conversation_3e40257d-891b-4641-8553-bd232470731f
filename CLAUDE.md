# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是 `rs_czsc`，缠中说禅技术分析库的 Rust 实现，专为高性能量化交易优化。项目采用多 crate 工作空间架构，通过 PyO3/maturin 提供 Python 绑定。

## 开发命令

### Rust 开发
```bash
# 代码格式化
cargo fmt

# 代码质量检查
cargo clippy

# 运行所有 Rust 测试
cargo test

# 构建所有 crate
cargo build --release

# 运行特定 crate 测试
cargo test -p czsc-core
```

### Python 绑定开发
```bash
# 进入 Python 目录
cd python

# 使用 uv 同步开发依赖
uv sync --group dev

# 使用 uv 本地构建 Python wheel
uv run maturin build --release

# 开发模式（安装到当前环境）
uv run maturin develop

# 运行 Python 测试
uv run pytest tests/

# 运行测试并生成覆盖率报告
uv run pytest tests/ --cov=rs_czsc --cov-report=html --cov-report=term-missing

# 只运行快速测试（跳过慢速测试）
uv run pytest tests/ -m "not slow"

# 运行特定测试文件
uv run pytest tests/test_czsc.py -v

# 运行特定测试函数
uv un pytest tests/test_czsc.py::test_czsc_creation -v

# 并行运行测试
uv run pytest tests/ -n auto
```

## 代码规范

- **注释语言**: 代码注释使用中文
- **格式化**: 使用 `rustfmt` 通过 `cargo fmt` 格式化 Rust 代码
- **代码检查**: 使用 `clippy` 通过 `cargo clippy` 进行代码质量检查
- **文档注释**: 使用 `///` 进行 Rust 文档注释

## 架构概览

### 工作空间结构
项目使用 Cargo 工作空间，包含以下主要 crate：

- **`czsc-core`**: 核心 CZSC 算法和数据结构（bars, bi, fx, zs 等）
- **`czsc-trader`**: 交易策略和回测框架
- **`czsc-utils`**: 工具函数（日历、性能分析、数据处理）
- **`czsc-binance`**: Binance 交易所期货交易集成
- **`czsc-ta`**: 技术分析指标
- **`czsc`**: 主库，重新导出其他 crate
- **`python/`**: Python 集成的 PyO3 绑定

### 核心组件

**核心对象** (czsc-core/src/objects/)：
- `Bar`: K线/蜡烛图数据结构
- `BI`: 笔 - CZSC 分析的基本单位
- `FX`: 分型 - 转折点
- `ZS`: 中枢 - 整理区域
- `Direction`: 市场方向枚举
- `Freq`: 时间频率处理

**交易系统** (czsc-trader/)：
- `WeightBacktest`: 基于权重的投资组合策略回测框架

**工具函数** (czsc-utils/)：
- `calendar`: 中国交易日历支持
- `daily_performance`: 性能分析工具
- `bar_generator`: K线数据生成工具

### Python 集成

Python 模块通过 PyO3 绑定暴露 Rust 功能：
- 核心 CZSC 对象（CZSC, BI, FX 等）
- 技术分析函数（ultimate_smoother, rolling_rank）
- 回测能力（WeightBacktest）
- 性能分析工具

## 测试策略

项目使用多种测试方法：

**Rust 测试**：
- 每个 crate 中使用 `#[cfg(test)]` 的单元测试
- `tests/` 目录中的集成测试
- 使用 `cargo test` 运行

**Python 测试**：
- `python/tests/` 中基于 pytest 的测试
- 测试标记：`@pytest.mark.slow`, `@pytest.mark.unit`, `@pytest.mark.integration`, `@pytest.mark.benchmark`
- 用于示例数据和测试工具的 fixtures
- 使用 pytest-cov 的覆盖率报告

基准测试方法参考：@python\tests\benchmark\README.md

## 构建和发布

**本地开发**：
```bash
# 构建 Python wheel 用于本地测试
cd python && uv run maturin build --release
```

**CI/CD**：
- GitHub Actions 工作流在发布时触发
- 为多平台（Linux, macOS, Windows）构建 wheel
- 使用 maturin-action 发布到 PyPI

## 依赖项

**主要 Rust 依赖**：
- `polars`: 用于数据处理的高性能 DataFrame 库
- `chrono`: 日期/时间处理
- `rayon`: 数据并行处理
- `serde`: 序列化框架

**Python 依赖**：
- `pandas`, `pyarrow`: 数据操作（必需）
- `pytest`: 测试框架（开发依赖）
- `pytest-benchmark`: 性能测试（开发依赖）