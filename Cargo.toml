[workspace]
members = ["crates/*", "examples/*", "python"]
resolver = "2"

[workspace.package]
version = "0.1.12"
edition = "2024"
description = "缠中说禅技术分析库的 Rust 实现"

[workspace.dependencies]
polars = { version = "0.42.0", features = [
    "lazy",
    "serde",
    "strings",
    "concat_str",
    "pivot",
    "is_in",
    "cum_agg",
    "abs",
    "ipc",
    "partition_by",
    "round_series",
] }
polars-plan = { version = "0.42.0", features = ["cov"] }
chrono = "0.4.38"
anyhow = "1.0.93"
derive_builder = "0.20.2"
hex = "0.4.3"
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.132"
sha2 = "0.10.8"
strum = "0.26.3"
strum_macros = "0.26.4"
thiserror = "2.0.3"
rayon = "1.10.0"
hashbrown = { version = "0.15.1", features = ["rayon", "serde"] }
tracing = "0.1.41"
numpy = { version = "0.25.0"}
pyo3 = { version = "0.25.0"}
pyo3-stub-gen = { version = "0.12.1"}
