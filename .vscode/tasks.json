{"version": "2.0.0", "tasks": [{"label": "Cargo Build", "type": "shell", "command": "cargo build", "problemMatcher": ["$rustc"], "group": {"kind": "build", "isDefault": true}}, {"label": "Cargo Run", "type": "shell", "command": "cargo run", "problemMatcher": ["$rustc"], "group": {"kind": "test", "isDefault": true}}, {"label": "Cargo Test", "type": "shell", "command": "cargo test", "problemMatcher": ["$rustc"]}, {"label": "Cargo Check", "type": "shell", "command": "cargo check", "problemMatcher": ["$rustc"], "options": {"env": {"PYO3_PYTHON": "python.exe"}}}, {"label": "Cargo Check Python", "type": "shell", "command": "cargo check -p czsc-python", "problemMatcher": ["$rustc"], "options": {"env": {"PYO3_PYTHON": "python.exe"}}}, {"label": "<PERSON><PERSON>", "type": "shell", "command": "cargo clippy", "problemMatcher": ["$rustc"]}, {"label": "Cargo Format", "type": "shell", "command": "cargo fmt", "problemMatcher": ["$rustc"]}, {"label": "Maturin Build Release", "type": "shell", "command": "uv run maturin build --release", "options": {"cwd": "${workspaceFolder}/python"}, "problemMatcher": ["$rustc"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "detail": "构建 Python wheel 包 (输出到 ./target/wheels/)"}, {"label": "<PERSON><PERSON><PERSON>", "type": "shell", "command": "uv run maturin develop", "options": {"cwd": "${workspaceFolder}/python"}, "problemMatcher": ["$rustc"], "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "detail": "开发模式构建并安装到当前环境"}, {"label": "Setup Virtual Environment", "type": "shell", "command": "uv venv .venv", "options": {"cwd": "${workspaceFolder}/python"}, "presentation": {"reveal": "always", "panel": "new"}, "detail": "创建虚拟环境"}]}