{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'czsc'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=czsc"],
        "filter": {
          "name": "czsc",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'czsc_core'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=czsc-core"],
        "filter": {
          "name": "czsc_core",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'czsc_signals'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=czsc-signals"],
        "filter": {
          "name": "czsc_signals",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'czsc_trader'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=czsc-trader"],
        "filter": {
          "name": "czsc_trader",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'czsc_utils'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=czsc-utils"],
        "filter": {
          "name": "czsc_utils",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug executable 'run_backtest'",
      "cargo": {
        "args": ["build", "--bin=run_backtest", "--package=run_backtest"],
        "filter": {
          "name": "run_backtest",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in executable 'run_backtest'",
      "cargo": {
        "args": [
          "test",
          "--no-run",
          "--bin=run_backtest",
          "--package=run_backtest"
        ],
        "filter": {
          "name": "run_backtest",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "name": "Run stub_gen",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/python/target/debug/stub_gen",
      "args": [],
      "cwd": "${workspaceFolder}/python",
      "cargo": {
        "args": ["build", "--bin", "stub_gen"]
      },
      "preLaunchTask": "cargo build stub_gen",
      "stopOnEntry": false
    }
  ]
}
