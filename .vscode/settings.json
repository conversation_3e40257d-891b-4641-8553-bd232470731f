{"python.analysis.extraPaths": ["./python"], "rust-analyzer.linkedProjects": ["./Cargo.toml"], "rust-analyzer.cargo.buildScripts.enable": true, "rust-analyzer.cargo.loadOutDirsFromCheck": true, "rust-analyzer.procMacro.enable": true, "rust-analyzer.procMacro.attributes.enable": true, "rust-analyzer.files.watcher": "server", "rust-analyzer.check.command": "clippy", "rust-analyzer.check.allTargets": true, "rust-analyzer.diagnostics.enable": true, "rust-analyzer.diagnostics.experimental.enable": true, "rust-analyzer.completion.addCallArgumentSnippets": true, "rust-analyzer.completion.addCallParenthesis": true, "rust-analyzer.inlayHints.bindingModeHints.enable": true, "rust-analyzer.inlayHints.closingBraceHints.minLines": 10, "rust-analyzer.inlayHints.closureReturnTypeHints.enable": "with_block", "rust-analyzer.inlayHints.discriminantHints.enable": "fieldless", "rust-analyzer.inlayHints.lifetimeElisionHints.enable": "skip_trivial", "rust-analyzer.inlayHints.typeHints.enable": true, "rust-analyzer.inlayHints.parameterHints.enable": true, "rust-analyzer.workspace.symbol.search.scope": "workspace_and_dependencies", "rust-analyzer.workspace.symbol.search.kind": "all_symbols", "rust-analyzer.server.extraEnv": {"PYO3_PYTHON": "python.exe"}, "editor.formatOnSave": true, "editor.rulers": [100], "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/target": true}, "[rust]": {"editor.defaultFormatter": "rust-lang.rust-analyzer", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.rust-analyzer": "explicit"}}, "[toml]": {"editor.defaultFormatter": "tamasfe.even-better-toml"}}