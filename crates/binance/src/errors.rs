use tokio_tungstenite::tungstenite;
use serde::Deserialize;
use thiserror::Error;

#[derive(Debug, Deserialize)]
pub struct BinanceContentError {
    pub code: i16,
    pub msg: String,
}

#[derive(Debug, Error)]
pub enum BinanceError {
    #[error("Binance error (code: {}): {}", .response.code, .response.msg)]
    BinanceError { response: BinanceContentError },

    #[error("{1} at {0} is missing")]
    KlineValueMissingError(usize, &'static str),

    #[error(transparent)]
    Reqwest(#[from] reqwest::Error),

    #[error(transparent)]
    InvalidHeader(#[from] reqwest::header::InvalidHeaderValue),

    #[error(transparent)]
    Io(#[from] std::io::Error),

    #[error(transparent)]
    ParseFloat(#[from] std::num::ParseFloatError),

    #[error(transparent)]
    UrlParser(#[from] url::ParseError),

    #[error(transparent)]
    Json(#[from] serde_json::Error),

    #[error(transparent)]
    Tungstenite(#[from] tungstenite::Error),

    #[error(transparent)]
    Timestamp(#[from] std::time::SystemTimeError),

    #[error(transparent)]
    Unexpected(#[from] anyhow::Error),

}

pub type Result<T> = std::result::Result<T, BinanceError>;

/// Copy from anyhow::bail!
#[macro_export]
macro_rules! bail {
    ($msg:literal $(,)?) => {
        return Err(anyhow::anyhow!($msg).into())
    };
    ($err:expr $(,)?) => {
        return Err(anyhow::anyhow!($err).into())
    };
    ($fmt:expr, $($arg:tt)*) => {
        return Err(anyhow::anyhow!($fmt, $($arg)*).into())
    };
}
