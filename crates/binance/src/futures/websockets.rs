// #![allow(unused)]
use crate::bail;
use anyhow::Context;
use futures_util::{SinkExt, StreamExt};
use tokio::io::{AsyncRead, AsyncWrite};
use tokio::sync::{mpsc, oneshot};
use tokio_tungstenite::tungstenite::Message;
use crate::errors::Result;
use crate::model::{
    AccountUpdateEvent, AggrTradesEvent, BookTickerEvent, ContinuousKlineEvent, DayTickerEvent,
    DepthOrderBookEvent, IndexKlineEvent, IndexPriceEvent, KlineEvent, LiquidationEvent,
    MarkPriceEvent, MiniTickerEvent, OrderBook, TradeEvent, UserDataStreamExpiredEvent,
};
use crate::futures::model;
use tokio::net::TcpStream;
use tokio_tungstenite::{client_async_tls, connect_async, MaybeTlsStream, WebSocketStream};
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use super::proxy::{InnerProxy, ProxyStream};

#[allow(clippy::all)]
pub enum FuturesWebsocketAPI {
    Default,
    MultiStream,
    Custom(String),
}

pub enum FuturesMarket {
    USDM,
    COINM,
    Vanilla,
}

impl FuturesWebsocketAPI {
    pub fn params(self, market: &FuturesMarket, subscription: &str) -> String {
        let baseurl = match market {
            FuturesMarket::USDM => "wss://fstream.binance.com",
            FuturesMarket::COINM => "wss://dstream.binance.com",
            FuturesMarket::Vanilla => "wss://vstream.binance.com",
        };

        match self {
            FuturesWebsocketAPI::Default => {
                format!("{}/ws/{}", baseurl, subscription)
            }
            FuturesWebsocketAPI::MultiStream => {
                format!("{}/stream?streams={}", baseurl, subscription)
            }
            FuturesWebsocketAPI::Custom(url) => {
                format!("{}/{}", url, subscription)
            }
        }
    }
}

#[allow(clippy::large_enum_variant)]
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum FuturesWebsocketEvent {
    AccountUpdate(AccountUpdateEvent),
    OrderTrade(model::OrderTradeEvent),
    AggrTrades(AggrTradesEvent),
    Trade(TradeEvent),
    OrderBook(OrderBook),
    DayTicker(DayTickerEvent),
    MiniTicker(MiniTickerEvent),
    MiniTickerAll(Vec<MiniTickerEvent>),
    IndexPrice(IndexPriceEvent),
    MarkPrice(MarkPriceEvent),
    MarkPriceAll(Vec<MarkPriceEvent>),
    DayTickerAll(Vec<DayTickerEvent>),
    Kline(KlineEvent),
    ContinuousKline(ContinuousKlineEvent),
    IndexKline(IndexKlineEvent),
    Liquidation(LiquidationEvent),
    DepthOrderBook(DepthOrderBookEvent),
    BookTicker(BookTickerEvent),
    UserDataStreamExpiredEvent(UserDataStreamExpiredEvent),
}

/// Websocket 文本消息处理
// type WsMsgHandler<'a> =
//     Box<dyn FnMut(FuturesWebsocketEvent) -> BoxFuture<'a, Result<()>> + Send + 'a>;

pub struct FuturesWebSockets<S>
where
    S: 'static + AsyncRead + AsyncWrite + Send + Unpin,
{
    pub socket: Option<WebSocketStream<MaybeTlsStream<S>>>,
    // handler: WsMsgHandler<'a>,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(untagged)]
enum FuturesEvents {
    Vec(Vec<DayTickerEvent>),
    DayTickerEvent(DayTickerEvent),
    BookTickerEvent(BookTickerEvent),
    MiniTickerEvent(MiniTickerEvent),
    VecMiniTickerEvent(Vec<MiniTickerEvent>),
    AccountUpdateEvent(AccountUpdateEvent),
    OrderTradeEvent(model::OrderTradeEvent),
    AggrTradesEvent(AggrTradesEvent),
    IndexPriceEvent(IndexPriceEvent),
    MarkPriceEvent(MarkPriceEvent),
    VecMarkPriceEvent(Vec<MarkPriceEvent>),
    TradeEvent(TradeEvent),
    KlineEvent(KlineEvent),
    ContinuousKlineEvent(ContinuousKlineEvent),
    IndexKlineEvent(IndexKlineEvent),
    LiquidationEvent(LiquidationEvent),
    OrderBook(OrderBook),
    DepthOrderBookEvent(DepthOrderBookEvent),
    UserDataStreamExpiredEvent(UserDataStreamExpiredEvent),
}

impl<S> FuturesWebSockets<S>
where
    S: 'static + AsyncRead + AsyncWrite + Send + Unpin,
{
    pub fn new() -> FuturesWebSockets<S> {
        FuturesWebSockets { socket: None }
    }

    async fn disconnect(&mut self) -> Result<()> {
        if let Some(ref mut socket) = self.socket {
            socket.close(None).await?;
            return Ok(());
        }
        bail!("Not able to close the connection");
    }

    pub fn new_channel(
        &self,
    ) -> (
        Arc<mpsc::Sender<Result<FuturesWebsocketEvent>>>,
        mpsc::Receiver<Result<FuturesWebsocketEvent>>,
    ) {
        let (tx, rx): (
            mpsc::Sender<Result<FuturesWebsocketEvent>>,
            mpsc::Receiver<Result<FuturesWebsocketEvent>>,
        ) = mpsc::channel(32);

        let tx = Arc::new(tx);
        (tx, rx)
    }

    pub async fn event_loop(
        &mut self, tx: Arc<mpsc::Sender<Result<FuturesWebsocketEvent>>>,
        mut shutdown_rx: oneshot::Receiver<()>,
    ) {
        while let Some(ref mut ws_stream) = self.socket {
            tokio::select! {
                Some(message) = ws_stream.next() => {
                    match message {
                        Ok(Message::Text(text)) => {
                            // Asynchronously handle text messages
                            let tx = Arc::clone(&tx);
                            let _ = tokio::spawn(async move {
                                handle_msg(text.to_string(), tx).await;
                            })
                            .await;
                        }
                        Ok(Message::Ping(payload)) => {
                            tracing::debug!(
                                payload = ?payload,
                                "Received Ping message"
                            );
                            if let Err(e) = ws_stream.send(Message::Pong(payload)).await {
                                tracing::error!(
                                    error = ?e,
                                    "Error sending Pong response"
                                );
                                break;
                            }
                        }
                        Ok(Message::Close(close_frame)) => {
                            tracing::error!(
                                close_frame = ?close_frame,
                                "Disconnected"
                            );
                            return ;
                        }
                        Err(e) => {
                            tracing::error!(
                                error = ?e,
                                "Error while receiving message"
                            );
                            break;
                        }
                        _ => {} // Ignore other message types
                    }
                }
                _ = &mut shutdown_rx => {
                    tracing::info!("Received shutdown signal, websocket channel closing up..");
                    break;
                }
            }
        }

        if let Err(e) = self.disconnect().await {
            tracing::error!("{:?}", e);
        };
    }
}

impl FuturesWebSockets<TcpStream>
where
    TcpStream: 'static + AsyncRead + AsyncWrite + Send + Unpin,
{
    pub async fn connect_wss(&mut self, wss: &str) -> Result<()> {
        let (s, _) = connect_async(wss).await?;
        self.socket = Some(s);
        Ok(())
    }
}

impl FuturesWebSockets<ProxyStream>
where
    ProxyStream: 'static + AsyncRead + AsyncWrite + Send + Unpin,
{
    pub async fn connect_wss(&mut self, wss: &str, proxy_addr: &str) -> Result<()> {
        let proxy =
            InnerProxy::from_proxy_str(&proxy_addr).context("failed to parse inner proxy")?;

        let tcp_stream = proxy
            .connect_async(wss)
            .await
            .context("failed to connect proxy")?;

        let (s, _) = client_async_tls(wss, tcp_stream).await?;
        self.socket = Some(s);
        Ok(())
    }
}

async fn handle_msg(msg: String, tx: Arc<mpsc::Sender<Result<FuturesWebsocketEvent>>>) {
    let value: serde_json::Value = match serde_json::from_str(&msg) {
        Ok(v) => v,
        Err(e) => {
            let _ = tx.send(Err(e.into())).await;
            return;
        }
    };

    if let Some(data) = value.get("data") {
        let fut = Box::pin(handle_msg(data.to_string(), tx.clone()));
        fut.await;
        return;
    }

    if let Ok(events) = serde_json::from_value::<FuturesEvents>(value) {
        let action = match events {
            FuturesEvents::Vec(v) => FuturesWebsocketEvent::DayTickerAll(v),
            FuturesEvents::DayTickerEvent(v) => FuturesWebsocketEvent::DayTicker(v),
            FuturesEvents::BookTickerEvent(v) => FuturesWebsocketEvent::BookTicker(v),
            FuturesEvents::MiniTickerEvent(v) => FuturesWebsocketEvent::MiniTicker(v),
            FuturesEvents::VecMiniTickerEvent(v) => FuturesWebsocketEvent::MiniTickerAll(v),
            FuturesEvents::AccountUpdateEvent(v) => FuturesWebsocketEvent::AccountUpdate(v),
            FuturesEvents::OrderTradeEvent(v) => FuturesWebsocketEvent::OrderTrade(v),
            FuturesEvents::IndexPriceEvent(v) => FuturesWebsocketEvent::IndexPrice(v),
            FuturesEvents::MarkPriceEvent(v) => FuturesWebsocketEvent::MarkPrice(v),
            FuturesEvents::VecMarkPriceEvent(v) => FuturesWebsocketEvent::MarkPriceAll(v),
            FuturesEvents::TradeEvent(v) => FuturesWebsocketEvent::Trade(v),
            FuturesEvents::ContinuousKlineEvent(v) => FuturesWebsocketEvent::ContinuousKline(v),
            FuturesEvents::IndexKlineEvent(v) => FuturesWebsocketEvent::IndexKline(v),
            FuturesEvents::LiquidationEvent(v) => FuturesWebsocketEvent::Liquidation(v),
            FuturesEvents::KlineEvent(v) => FuturesWebsocketEvent::Kline(v),
            FuturesEvents::OrderBook(v) => FuturesWebsocketEvent::OrderBook(v),
            FuturesEvents::DepthOrderBookEvent(v) => FuturesWebsocketEvent::DepthOrderBook(v),
            FuturesEvents::AggrTradesEvent(v) => FuturesWebsocketEvent::AggrTrades(v),
            FuturesEvents::UserDataStreamExpiredEvent(v) => {
                FuturesWebsocketEvent::UserDataStreamExpiredEvent(v)
            }
        };
        let _ = tx.send(Ok(action)).await;
    }
}
