use std::time::SystemTime;

use anyhow::Context;

use crate::bail;
use crate::model::Empty;
use crate::futures::model::{ExchangeInformation, ServerTime, Symbol};
use crate::client::Client;
use crate::errors::Result;
use crate::api::API;
use crate::api::Futures;
use crate::util::get_timestamp;

#[derive(Clone)]
pub struct FuturesGeneral {
    pub client: Client,
}

impl FuturesGeneral {
    // Test connectivity
    pub async fn ping(&self) -> Result<String> {
        self.client
            .get::<Empty>(API::Futures(Futures::Ping), None)
            .await?;
        Ok("pong".into())
    }

    // Check server time
    pub async fn get_server_time(&self) -> Result<ServerTime> {
        self.client.get(API::Futures(Futures::Time), None).await
    }

    // Obtain exchange information
    // - Current exchange trading rules and symbol information
    pub async fn exchange_info(&self) -> Result<ExchangeInformation> {
        self.client
            .get(API::Futures(Futures::ExchangeInfo), None)
            .await
    }

    // Get Symbol information
    pub async fn get_symbol_info<S>(&self, symbol: S) -> Result<Symbol>
    where
        S: Into<String>,
    {
        let upper_symbol = symbol.into().to_uppercase();
        match self.exchange_info().await {
            Ok(info) => {
                for item in info.symbols {
                    if item.symbol == upper_symbol {
                        return Ok(item);
                    }
                }
                bail!("Symbol not found")
            }
            Err(e) => Err(e),
        }
    }
}
// 加载服务器时差, 参考: https://github.com/ccxt/ccxt/blob/c2791473d549f54b1f531eee85119885829b9bdc/python/ccxt/async_support/base/exchange.py#L1638
pub async fn load_time_difference(general: &FuturesGeneral) -> Result<u64> {
    let server_time = general
        .get_server_time()
        .await
        .context("Failed to get server time")?;

    // 当前毫秒数
    let after = get_timestamp(SystemTime::now()).context("Failed to get timestamp")?;

    return Ok(after - server_time.server_time);
}
