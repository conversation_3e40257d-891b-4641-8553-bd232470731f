[package]
name = "binance"
version = "0.21.0"
license = "MIT OR Apache-2.0"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2021"

description = "Rust Library for the Binance API"
keywords = ["cryptocurrency", "trading", "binance"]
categories = ["api-bindings", "cryptography::cryptocurrencies"]
documentation = "https://docs.rs/crate/binance/"
repository = "https://github.com/wisespace-io/binance-rs"
readme = "README.md"

# [badges]
# travis-ci = { repository = "wisespace-io/binance-rs" }

[lib]
name = "binance"
path = "src/lib.rs"

[dependencies]
hex = "0.4"
hmac = "0.12.1"
sha2 = "0.10.8"
serde = { version = "1.0.197", features = ["derive"] }
serde_json = "1.0"
thiserror = "2.0.3"
anyhow = "1.0.93"
reqwest = { version = "0.11.24", features = ["json"] }
url = "2.5.0"
clap = "4.5.2"
tokio = { version = "1.41.0", features = ["io-util", "net", "rt", "sync"] }
tokio-tungstenite = { version = "0.26.2", features = ["native-tls", "connect"] }
futures-util = "0.3.31"
tracing = "0.1.41"

[dev-dependencies]
csv = "1.3.0"
mockito = "1.4.0"
env_logger = "0.11.2"
criterion = "0.5"
float-cmp = "0.9.0"
tokio = { version = "1.41.0", features = ["macros", "test-util"] }
