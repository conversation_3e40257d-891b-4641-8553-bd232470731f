use std::{cmp::Reverse, collections::BTreeMap};

/// # 优先级队列
///
/// 按照 event time 记录前 N 个事件, 最新的事件在前面
pub struct TopNEevent<T> {
    capacity: Option<usize>,
    events: BTreeMap<Reverse<u64>, T>,
}

impl<T> TopNEevent<T> {
    pub fn new(capacity: Option<usize>) -> Self {
        Self {
            capacity,
            events: BTreeMap::new(),
        }
    }

    /// 插入元素，如果 BTreeMap 容量超过指定容量，将移除最小 event time
    pub fn insert(&mut self, event_time: u64, value: T) {
        let key = Reverse(event_time);
        self.events.insert(key, value);
        if let Some(cap) = self.capacity {
            if self.events.len() > cap {
                self.events.pop_last();
            }
        }
    }

    /// 返回前 N 个元素
    pub fn top(&self, n: usize) -> Vec<(u64, &T)> {
        self.events
            .iter()
            .take(n)
            .map(|(Reverse(event_time), value)| (*event_time, value))
            .collect()
    }

    /// 返回最大的元素（事件时间最晚的）
    pub fn first(&self) -> Option<(&u64, &T)> {
        self.events
            .iter()
            .next()
            .map(|(Reverse(event_time), value)| (event_time, value))
    }

    pub fn iter(&self) -> impl Iterator<Item = (u64, &T)> + '_ {
        self.events.iter().map(|(k, v)| (k.0, v))
    }

    pub fn iter_rev(&self) -> impl Iterator<Item = (u64, &T)> + '_ {
        self.events.iter().rev().map(|(k, v)| (k.0, v))
    }
}
