use binance::futures::model::Symbol;

/// 加密货币永续合约基本信息
#[derive(Debug)]
pub struct ContractInfo {
    /// 最小下单数量
    pub min_amount: f64,
    /// 最小名义价值
    pub min_notional: f64,
    /// 价格精度
    pub price_precision: f64,
    /// 数量精度
    pub amount_precision: f64,
    /// 市价的数量精度
    pub market_amount_precision: f64,
    /// 市价的最小下单数量
    pub market_min_amount: f64,
    // todo 还有市价的数量精度以及最小下单数量
}

impl ContractInfo {
    pub fn new_from_symbol_info(symbol_info: &Symbol) -> Option<Self> {
        let mut amount_precision: Option<f64> = None;
        // 价格精度
        let mut price_precision: Option<f64> = None;
        let mut min_amount: Option<f64> = None;
        let mut min_notional: Option<f64> = None;
        let mut market_amount_precision: Option<f64> = None;
        let mut market_min_amount: Option<f64> = None;

        for filters in symbol_info.filters.iter() {
            match filters {
                binance::model::Filters::LotSize {
                    min_qty,
                    max_qty: _,
                    step_size,
                } => {
                    // 市价的最小下单数量
                    min_amount = min_qty.parse().ok();
                    // 市价的数量精度
                    amount_precision = step_size.parse().ok();
                }
                binance::model::Filters::MarketLotSize {
                    min_qty,
                    max_qty: _,
                    step_size,
                } => {
                    // 最小下单数量
                    market_min_amount = min_qty.parse().ok();
                    // 数量精度
                    market_amount_precision = step_size.parse().ok();
                }
                binance::model::Filters::MinNotional {
                    notional,
                    min_notional: _,
                    apply_to_market: _,
                    avg_price_mins: _,
                } => {
                    // 最小名义价值
                    min_notional = notional.clone().and_then(|s| s.parse().ok());
                }
                binance::model::Filters::PriceFilter {
                    min_price: _,
                    max_price: _,
                    tick_size,
                } => {
                    price_precision = tick_size.parse().ok();
                }
                _ => {}
            }
        }

        let min_amount = min_amount?;
        let min_notional = min_notional?;
        let price_precision = price_precision?;
        let amount_precision = amount_precision?;
        let market_amount_precision = market_amount_precision?;
        let market_min_amount = market_min_amount?;
        Some(Self {
            min_amount,
            min_notional,
            price_precision,
            amount_precision,
            market_amount_precision,
            market_min_amount,
        })
    }
}

/// # 限制下单数量的精度
///
/// ## 参数
///
/// amount: 下单数量
pub fn limit_amount(amount: f64, min_amount: f64, amount_precision: f64) -> f64 {
    if amount < min_amount {
        return 0.0;
    }

    if amount_precision == 0.0 {
        amount.trunc()
    } else {
        let p = (-amount_precision.log10()).round() as i32;
        let factor = 10_f64.powi(p);
        (amount * factor).round() / factor
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_limit_amount() {
        let ci = ContractInfo {
            min_amount: 0.001,
            min_notional: 100.0,
            price_precision: 0.1,
            amount_precision: 0.001,
            market_amount_precision: 0.001,
            market_min_amount: 0.001,
        };

        let res = limit_amount(0.03361796378663941, ci.min_amount, ci.amount_precision);
        println!("{}", res);
    }
}
