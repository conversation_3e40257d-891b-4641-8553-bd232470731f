use crate::futures_trader::errors::BinanceTraderError;
use crate::futures_trader::{
    objects::{ContractInfo, limit_amount},
    queue::TopNEevent,
};
use anyhow::Context as anyhowContext;
use binance::{
    api::Binance,
    config::Config,
    errors::BinanceError,
    futures::{
        account::{
            CustomOrderRequest, FuturesAccount, OrderSide, OrderType, PositionSide, TimeInForce,
        },
        general::{FuturesGeneral, load_time_difference},
        model::{OrderUpdate, PositionRisk},
        proxy::ProxyStream,
        userstream::FuturesUserStream,
        websockets::{FuturesWebSockets, FuturesWebsocketEvent},
    },
};
use chrono::{DateTime, Local};
use std::{borrow::Borrow, collections::HashMap, fmt::Write, sync::Arc, time::Duration};
use tokio::{
    net::TcpStream,
    pin,
    sync::{Mutex, Notify, mpsc, oneshot},
    task::JoinSet,
    time,
};

#[derive(Debug)]
pub enum OrderResult {
    /// 小于最小下单金额
    BelowMin,
    /// 创建订单成功
    Created,
    /// 跳过做空
    Skip,
    /// 下单失败
    Error(BinanceError),
}

impl OrderResult {
    fn created(&self) -> bool {
        match self {
            OrderResult::Created => true,
            _ => false,
        }
    }

    fn handle_error(&self, side: &str, symbol: &str, error: &mut Option<anyhow::Error>) {
        if let OrderResult::Error(e) = self {
            tracing::error!(
                symbol = %symbol,
                "Failed to place {side} order: {e}",
            );

            *error = error
                .take()
                .map(|prev| {
                    // 在闭包内重新生成错误信息
                    anyhow::anyhow!("{prev}\nFailed to place {side} order: {e}")
                })
                .or_else(|| {
                    // 在备选分支生成相同错误信息
                    Some(anyhow::anyhow!("Failed to place {side} order: {e}"))
                });
        }
    }
}

/// # 根据权重配置完成调仓(USDT-M)
///
/// ## 权重配置

/// 关联合约和它的目标权重映射
///
/// 例如: {'BNBUSDT': 0.27, 'BTCUSDT': -0.234, 'ETHUSDT': 0.1, 'SOLUSDT': -0.3, 'XRPUSDT': -0.36}
///
pub struct BinanceFuturesTrader {
    // general: FuturesGeneral,
    // market: FuturesMarket,
    /// 币安 API 地址配置
    config: Config,
    secret_key: String,
    api_key: String,
    /// 下单用的账户实例
    account: FuturesAccount,
    /// http 代理地址, 例如："http://127.0.0.1:20171". 留空则不使用代理
    proxy_addr: Option<String>,
}

impl BinanceFuturesTrader {
    /// # 创建实例
    ///
    /// ## 代理
    ///
    /// 如果代理设置为None, 将不使用代理
    pub async fn new(
        api_key: String,
        secret_key: String,
        mut config: Config,
        proxy_addr: Option<String>,
    ) -> Result<Self, BinanceTraderError> {
        let general: FuturesGeneral = Binance::new_with_config(
            Some(api_key.clone()),
            Some(secret_key.clone()),
            &config,
            proxy_addr.clone(),
        );

        config.time_difference = Some(
            load_time_difference(&general)
                .await
                .context("Failed to load time difference")?,
        );

        let account: FuturesAccount = Binance::new_with_config(
            Some(api_key.clone()),
            Some(secret_key.clone()),
            &config,
            proxy_addr.clone(),
        );

        Ok(Self {
            config,
            account,
            api_key,
            secret_key,
            proxy_addr,
        })
    }

    /// # 获取交易规则和交易对
    ///
    /// ## 参数
    ///
    /// 合约列表
    ///
    /// ## 返回
    ///
    /// 合约和它的交易规则的映射
    pub async fn fetch_contract_info<I, S>(
        &self,
        symbols: I,
    ) -> Result<HashMap<String, Option<ContractInfo>>, BinanceTraderError>
    where
        I: IntoIterator<Item = S>,
        S: Borrow<str>,
    {
        // 创建实例
        let general: FuturesGeneral = Binance::new_with_config(
            Some(self.api_key.clone()),
            Some(self.secret_key.clone()),
            &self.config,
            self.proxy_addr.clone(),
        );
        // 从 REST API 获取
        let exchange_info = general
            .exchange_info()
            .await
            .context("Failed to fetch exchange info")?;

        let mut contract_infos: HashMap<String, Option<ContractInfo>> = HashMap::new();

        for s in symbols {
            let s: String = s.borrow().to_string();
            contract_infos.insert(s, None);
        }

        for symbol_info in exchange_info.symbols {
            match contract_infos.get_mut(&symbol_info.symbol) {
                Some(v) => {
                    *v = ContractInfo::new_from_symbol_info(&symbol_info);
                }
                None => {}
            }
        }
        return Ok(contract_infos);
    }

    /// # 调仓主函数
    ///
    /// ## 参数
    ///
    /// target_weights: {'BNBUSDT': 0.27, 'BTCUSDT': -0.234, 'ETHUSDT': 0.1, 'SOLUSDT': -0.3, 'XRPUSDT': -0.36}
    ///
    /// max_attempts: 最大尝试
    ///
    /// attempt_timeout: 完全交易等待秒数
    ///
    /// ticker_timeout: 如果超时无法获得 ticker 数据,将结束调仓
    pub async fn run(
        &self,
        target_weights: &HashMap<String, f64>,
        max_attempts: usize,
        attempt_timeout: Duration,
        ticker_timeout: Duration,
    ) -> Result<(), BinanceTraderError> {
        tracing::info!(
            target_weights = ?target_weights,
            "Started with target weights"
        );

        // 获取每个交易对的交易规则
        let contract_infos = self
            .fetch_contract_info(target_weights.keys().map(|k| k.borrow()))
            .await?;

        let mut symbols = Vec::new();
        for (symbol, _contract_info) in contract_infos.iter() {
            symbols.push(symbol.as_str());
        }

        // 创建跟踪器
        let order_tracer = OrderTracer::new(&symbols);
        let ticker_tracer = TickerTracer::new(&symbols);

        // 创建 listen key
        let user_stream: FuturesUserStream = Binance::new_with_config(
            Some(self.api_key.clone()),
            Some(self.secret_key.clone()),
            &self.config,
            self.proxy_addr.clone(),
        );
        // todo keep alive listen key, 有效时长是60min, https://developers.binance.com/docs/zh-CN/derivatives/usds-margined-futures/user-data-streams
        let listen_key = user_stream
            .start()
            .await
            .context("Failed to create user listen key")?;

        // 订阅 websocket 数据流
        let (shutdown_tx, shutdown_rx) = oneshot::channel();

        let subscriber = WsSubscriber::ready(
            &symbols,
            &listen_key.listen_key,
            &self.config.futures_ws_endpoint,
            self.proxy_addr.as_deref(),
            shutdown_rx,
        )
        .await?;

        let order_tracer_s = order_tracer.clone();
        let ticker_tracer_s = ticker_tracer.clone();
        tokio::spawn(async move {
            subscriber.subscribe(order_tracer_s, ticker_tracer_s).await;
        });
        tracing::info!("Subscribed websocket");

        let mut join_set: JoinSet<(String, Result<(), BinanceTraderError>)> = JoinSet::new();
        // 并发处理每个symbol
        for (symbol, contract_info) in contract_infos {
            let contract_info = contract_info
                .ok_or_else(|| anyhow::anyhow!("Failed to find contract info for {}", symbol))?;
            let target_weight = *target_weights.get(&symbol).unwrap();

            let tickers = ticker_tracer.symbols_tickers.get(&symbol).unwrap().clone();
            let orders = order_tracer.symbols_orders.get(&symbol).unwrap().clone();
            let symbol_trader = SymbolTrader {
                contract_info,
                target_weight,
                account: self.account.clone(),
                tickers,
                orders,
                max_attempts,
                attempt_timeout,
                ticker_timeout,
                symbol: symbol.clone(),
            };
            join_set.spawn(async move { (symbol, symbol_trader.trade().await) });
        }

        while let Some(res) = join_set.join_next().await {
            match res {
                Ok((symbol, Ok(()))) => {
                    Summarizer::summarize(symbol, order_tracer.clone(), None).await;
                }
                Ok((symbol, Err(e))) => {
                    tracing::error!("Error processing symbol: {}", e);
                    Summarizer::summarize(symbol, order_tracer.clone(), Some(e)).await;
                }
                Err(e) => eprintln!("Error processing symbol: {}", e),
            }
        }

        let _ = shutdown_tx.send(());

        Ok(())
    }
}

/// 相同交易对下的ticker记录
#[derive(Clone)]
struct SymbolsTickers {
    notify: Arc<tokio::sync::Notify>,
    top_n_ticker_price: Arc<Mutex<TopNEevent<f64>>>,
}

impl SymbolsTickers {
    fn new() -> Self {
        Self {
            notify: Arc::new(Notify::new()),
            top_n_ticker_price: Arc::new(Mutex::new(TopNEevent::new(None))),
        }
    }
}

/// 交易对(Symbol): Ticker记录
pub struct TickerTracer {
    symbols_tickers: Arc<HashMap<String, SymbolsTickers>>,
}

impl TickerTracer {
    pub fn new(symbols: &[&str]) -> Self {
        let mut symbols_tickers = HashMap::new();
        for s in symbols {
            symbols_tickers.insert(s.to_string(), SymbolsTickers::new());
        }
        // 使用 Arc 确保运行时不会被修改symbols
        Self {
            symbols_tickers: Arc::new(symbols_tickers),
        }
    }

    pub fn clone(&self) -> Self {
        let symbols_tickers = self.symbols_tickers.clone();
        Self { symbols_tickers }
    }
}

/// 相同订单ID下所有的订单状态事件, 按照事件时间排序
type IdOrderEvents = HashMap<u64, TopNEevent<OrderUpdate>>;

/// 相同交易对下所有订单
#[derive(Clone)]
struct SymbolOrders {
    notify: Arc<tokio::sync::Notify>,
    data: Arc<Mutex<InnerSymbolOrders>>,
}
struct InnerSymbolOrders {
    /// 订单ID的时间顺序, Order ID为存储元素, 第一个是最新的订单
    order_id_sequence: TopNEevent<u64>,
    events: IdOrderEvents,
}

impl InnerSymbolOrders {
    /// 基于订单ID添加订单更新事件
    async fn add_event_by_id(&mut self, order_id: u64, event_time: u64, event: OrderUpdate) {
        self.events
            .entry(order_id)
            .or_insert(TopNEevent::new(None))
            .insert(event_time, event);
    }
}

impl SymbolOrders {
    fn new() -> Self {
        Self {
            notify: Arc::new(Notify::new()),
            data: Arc::new(Mutex::new(InnerSymbolOrders {
                order_id_sequence: TopNEevent::new(None),
                events: HashMap::new(),
            })),
        }
    }
}

/// websocket 订阅订单跟踪
///
/// 交易对(Symbol): 订单流(Order ID, 相同ID的所有订单): 事件时间(Event Time): 订单状态(Order Status)
struct OrderTracer {
    symbols_orders: Arc<HashMap<String, SymbolOrders>>,
}

impl OrderTracer {
    fn new(symbols: &[&str]) -> Self {
        let mut symbols_orders = HashMap::new();
        for s in symbols {
            symbols_orders.insert(s.to_string(), SymbolOrders::new());
        }
        // 使用 Arc 确保运行时不会被修改symbols
        Self {
            symbols_orders: Arc::new(symbols_orders),
        }
    }

    fn clone(&self) -> Self {
        let symbols_orders = self.symbols_orders.clone();
        Self { symbols_orders }
    }
}

/// # websocket 数据订阅, (用户数据+市场数据)
struct WsSubscriber {
    receiver: mpsc::Receiver<Result<FuturesWebsocketEvent, BinanceError>>,
}

impl WsSubscriber {
    /// # 初始化实例， 为订阅预备操作
    ///
    /// ## 参数
    ///
    /// - symbols: 关联交易对名称, `["BTCUSDT", "ETHUSDT"]`, 不区别大小写
    ///
    /// - listen_key: 用户数据流的key, https://developers.binance.com/docs/zh-CN/derivatives/usds-margined-futures/user-data-streams/Start-User-Data-Stream
    ///
    /// - ws_endpoint: ws地址, https://developers.binance.com/docs/zh-CN/derivatives/usds-margined-futures/websocket-market-streams
    ///
    /// - proxy_addr: 代理地址
    ///
    /// - shutdown_rx: 接收关闭ws的信号
    async fn ready(
        symbols: &[&str],
        listen_key: &str,
        ws_endpoint: &str,
        proxy_addr: Option<&str>,
        shutdown_rx: oneshot::Receiver<()>,
    ) -> Result<Self, BinanceTraderError> {
        let streams = symbols
            .iter()
            .map(|s| s.to_lowercase().to_string() + "@miniTicker")
            .collect::<Vec<_>>()
            .join("/");

        // 订阅api url
        let websocket_url = format!("{}/stream?streams={}/{}", ws_endpoint, streams, listen_key);

        let receiver = match proxy_addr {
            Some(proxy_addr) => {
                tracing::debug!(
                    websocket_url = %websocket_url,
                    proxy_addr = %proxy_addr,
                    "connecting via proxy"
                );
                let mut ws: FuturesWebSockets<ProxyStream> = FuturesWebSockets::new();
                // 连接 webscoket (使用代理)
                ws.connect_wss(&websocket_url, proxy_addr)
                    .await
                    .context("Failed to connect websocket via proxy")?;

                // 事件输送通道
                let (tx, rx) = ws.new_channel();

                tokio::spawn(async move {
                    ws.event_loop(tx, shutdown_rx).await;
                });
                rx
            }
            None => {
                tracing::debug!(
                    websocket_url = %websocket_url,
                    "connecting without proxy"
                );
                let mut ws: FuturesWebSockets<TcpStream> = FuturesWebSockets::new();
                // 连接 webscoket (不使用代理)
                ws.connect_wss(&websocket_url)
                    .await
                    .context("Failed to connect websocket via proxy")?;

                // 事件输送通道
                let (tx, rx) = ws.new_channel();

                tokio::spawn(async move {
                    ws.event_loop(tx, shutdown_rx).await;
                });
                rx
            }
        };

        Ok(Self { receiver })
    }

    /// # 订阅
    /// 消耗自身实例
    async fn subscribe(self, order_tracer: OrderTracer, ticker_tracer: TickerTracer) {
        let mut receiver = self.receiver;
        while let Some(event_result) = receiver.recv().await {
            match event_result {
                Err(e) => {
                    tracing::error!(
                        binance_error = ?e,
                        "[BinanceFuturesTrader WS] Failed to receive event"
                    );
                }
                Ok(event) => {
                    tracing::debug!(
                        error = ?event,
                        "[BinanceFuturesTrader WS] Received event"
                    );
                    match event {
                        FuturesWebsocketEvent::MiniTicker(e) => {
                            if let Some(symbol_tickers) =
                                ticker_tracer.symbols_tickers.get(&e.symbol)
                            {
                                // 只处理相关交易对的ticker
                                // 收盘价即是最新成交价格
                                // https://developers.binance.com/docs/zh-CN/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Mini-Ticker-Stream
                                let close = match e.close.parse::<f64>() {
                                    Ok(c) => c,
                                    Err(e) => {
                                        tracing::error!(
                                            parse_error = ?e,
                                            "[BinanceFuturesTrader WS] Failed to parse close price for mini ticker"
                                        );
                                        continue;
                                    }
                                };
                                let mut g = symbol_tickers.top_n_ticker_price.lock().await;
                                // 记录价格
                                g.insert(e.event_time, close);
                                // 收到 ticker 价格， 发起通知
                                symbol_tickers.notify.notify_one();
                                // 释放锁
                            }
                        }
                        FuturesWebsocketEvent::OrderTrade(e) => {
                            if let Some(symbol_orders) =
                                order_tracer.symbols_orders.get(&e.order.symbol)
                            {
                                // 只处理相关交易对的订单
                                let mut g = symbol_orders.data.lock().await;
                                // 记录订单ID
                                g.order_id_sequence.insert(e.event_time, e.order.order_id);
                                // 记录订单更新
                                g.add_event_by_id(e.order.order_id, e.event_time, e.order)
                                    .await;
                                // 收到 订单更新， 发起通知
                                symbol_orders.notify.notify_one();
                                // 释放锁
                            }
                        }
                        _ => {}
                    }
                }
            }
        }
    }
}

/// 给定目标权重，交易合约
struct SymbolTrader {
    /// 交易规则
    contract_info: ContractInfo,
    /// 目标权重
    target_weight: f64,
    /// 账户实例(REST API)
    account: FuturesAccount,
    /// 市场数据跟踪器
    tickers: SymbolsTickers,
    /// 订单数据跟踪器
    orders: SymbolOrders,
    /// 最大尝试
    max_attempts: usize,
    /// 完全交易等待秒数
    attempt_timeout: Duration,
    /// 获取 Ticker 数据超时，如果超时无法获得 ticker 数据,将结束调仓
    ticker_timeout: Duration,
    /// 交易对
    symbol: String,
}

impl SymbolTrader {
    /// 等待收到 tickers 数据
    async fn wait_ticker(&self) -> Result<(), BinanceTraderError> {
        let timeout = time::sleep(self.ticker_timeout);
        pin!(timeout);
        loop {
            tokio::select! {
                _ = self.tickers.notify.notified() => {
                    return Ok(());
                }
                _ = &mut timeout => {
                    tracing::warn!(
                        symbol = %self.symbol,
                        "ticker api timeout, exit now"
                    );
                    return Err(BinanceTraderError::TickerTimeout(self.ticker_timeout));
                }
            }
        }
    }

    async fn trade(self) -> Result<(), BinanceTraderError> {
        let symbol = self.symbol.as_ref();
        let target_weight = self.target_weight;

        let mut encounter_err: Option<anyhow::Error> = None;
        for attempt in 1..(self.max_attempts + 1) {
            if encounter_err.is_some() {
                break;
            }

            tracing::info!(
                symbol = %symbol,
                attempt = %attempt,
                ticker_timeout = ?self.ticker_timeout,
                "Waiting for tickers.."
            );
            self.wait_ticker().await?;

            let total_balance = self
                .fetch_balance()
                .await?
                .ok_or_else(|| anyhow::anyhow!("Failed to get balance: None"))?;
            tracing::info!(
                balance = ?total_balance,
                "Current balance"
            );

            tracing::info!(
                symbol = %symbol,
                attempt = %attempt,
                balance = %total_balance,
                "Started"
            );

            // 撤销当前所有订单(使用了 REST API)
            self.cancel_all_open_orders(&symbol).await?;

            // 获取当前持仓数量(使用了 REST API 获取当前持仓)
            let all_positions = self.fetch_positions(symbol).await?;

            // 获得 最新成交价格
            let current_price = {
                let g = self.tickers.top_n_ticker_price.lock().await;
                g.first().and_then(|(_, price)| Some(*price))
            };
            let current_price = current_price.unwrap();
            tracing::info!(
                symbol = %symbol,
                current_price = %current_price,
                "Price update"
            );

            // 计算持仓变化
            let (long_difference, short_difference) = self
                .calculate_position_change(
                    &symbol,
                    target_weight,
                    current_price,
                    total_balance,
                    all_positions,
                )
                .await?;

            // 最后一次尝试使用市价单
            let limit_order = if attempt == self.max_attempts {
                tracing::warn!(
                    symbol = %symbol,
                    "Rebalancing plan Max attempts reached. Final attempt used market order."
                );
                false
            } else {
                true
            };

            // 调整仓位(下单，使用 REST API)
            let (long, short) = self
                .adjust_position(
                    symbol,
                    &self.contract_info,
                    long_difference,
                    short_difference,
                    current_price,
                    limit_order,
                )
                .await;

            // 如果有一边报错，跑完这次就退出流程
            long.handle_error("long", symbol, &mut encounter_err);
            short.handle_error("short", symbol, &mut encounter_err);

            if !long.created() && !short.created() {
                // 都没有订单创建，直接退出流程
                tracing::warn!(
                    symbol = %symbol,
                    "Rebalancing plan skipped, no order created"
                );
                break;
            }

            // 在超时时间内订阅订状态信息

            // 创建超时定时器
            let timeout = time::sleep(self.attempt_timeout);
            pin!(timeout);

            tracing::info!(
                symbol = %symbol,
                attempt = %attempt,
                wait = ?self.attempt_timeout,
                "Waiting for filled order received.."
            );

            loop {
                tokio::select! {
                    // 收到通知时的处理分支
                    _ = self.orders.notify.notified() => {
                        if self.handle_received_order_update().await == Some(true) {
                            tracing::warn!(
                                symbol = %symbol,
                                "Rebalancing plan completed"
                            );
                            break;
                        }
                    }
                    // 超时处理分支
                    _ = &mut timeout => {
                        tracing::warn!(
                            symbol = %symbol,
                            "Rebalancing plan timeout, try again"
                        );
                        break;
                    }
                }
            }
        }

        match encounter_err {
            Some(e) => Err(anyhow::anyhow!("Failed to place order: {}", e).into()),
            None => Ok(()),
        }
    }

    /// # 查询当前账户的可用的币
    ///
    /// ## 返回
    ///
    /// - Ok(<余额>)
    ///
    /// - None
    async fn fetch_balance(&self) -> Result<Option<f64>, BinanceTraderError> {
        let balance = self
            .account
            .account_balance()
            .await
            .context("Failed to fetch balance")?;

        let mut coin_balance = None;
        for b in balance {
            if b.asset == "USDT" {
                coin_balance = Some(b);
                break;
            }
        }
        Ok(coin_balance.and_then(|b| Some(b.available_balance)))
    }

    /// # 检查最新订单ID的最新订单状态, 完全成交后返回true
    async fn handle_received_order_update(&self) -> Option<bool> {
        let g = self.orders.data.lock().await;

        // 获取最新订单 ID
        let latest_order_id = g.order_id_sequence.first()?.1;

        // 获取该订单对应的事件
        let events = g.events.get(latest_order_id)?;

        // 获取第一个事件
        let order_update_status = events.first()?.1;

        // PARTIALLY_FILLED: 部分成交
        // NEW：新建订单
        // FILLED: 完全成交
        match order_update_status.order_status.as_str() {
            "FILLED" => {
                // 完全成交后可以退出
                return Some(true);
            }
            "PARTIALLY_FILLED" => {
                tracing::info!(
                    symbol = %order_update_status.symbol,
                    accumulated_qty_filled_trades = %order_update_status.accumulated_qty_filled_trades,
                    "order is partially filled"
                );
            }
            _ => {
                // 其他状态忽略
            }
        }

        Some(false)
    }

    /// # 调仓
    ///
    /// 返回
    ///
    /// adjust_ok: 说明调仓计划完成
    async fn adjust_position(
        &self,
        symbol: &str,
        contract_info: &ContractInfo,
        long_difference: f64,
        short_difference: f64,
        current_price: f64,
        limit_order: bool,
    ) -> (OrderResult, OrderResult) {
        let long_result = if (long_difference * current_price).abs() >= contract_info.min_notional {
            // 做多
            let side = if long_difference > 0.0 {
                OrderSide::Buy
            } else {
                OrderSide::Sell
            };
            let amount = long_difference.abs();
            self.place_order(
                symbol.to_string(),
                contract_info,
                amount,
                side,
                PositionSide::Long,
                current_price,
                limit_order,
            )
            .await
        } else {
            // 这说明没有下单
            OrderResult::Skip
        };

        let short_result = if (short_difference * current_price).abs() >= contract_info.min_notional
        {
            // 做空
            let side = if short_difference > 0.0 {
                OrderSide::Sell
            } else {
                OrderSide::Buy
            };
            let amount = short_difference.abs();
            self.place_order(
                symbol.to_string(),
                contract_info,
                amount,
                side,
                PositionSide::Short,
                current_price,
                limit_order,
            )
            .await
        } else {
            // 这说明没有下单
            OrderResult::Skip
        };

        (long_result, short_result)
    }

    /// # 下单
    ///
    /// ## 参数
    ///
    /// symbol: 交易对
    ///
    /// amount: 下单数量
    ///
    /// side: 买卖方向
    ///
    /// position_side: 仓位方向
    ///
    /// current_price: 当前价格
    ///
    /// limit_order: 是否使用限价订单, 否则使用市价
    ///
    /// 返回
    ///
    /// 是否成功下单
    async fn place_order(
        &self,
        symbol: String,
        contract_info: &ContractInfo,
        amount: f64,
        side: OrderSide,
        position_side: PositionSide,
        current_price: f64,
        limit_order: bool,
    ) -> OrderResult {
        tracing::info!(
            symbol = %symbol,
            amount = %amount,
            side = %side,
            position_side = %position_side,
            limit_order = %limit_order,
            current_price = %current_price,
            "Rebalance plan"
        );

        // 根据 tick size 的大小，确定挂单调整的比例
        let tick_size = (contract_info.price_precision / current_price) * 10000.0;
        // 价格偏移的步数
        let price_offset_steps = if tick_size >= 1.0 {
            1
        } else if tick_size > 0.1 && tick_size < 1.0 {
            5
        } else if tick_size > 0.01 && tick_size < 0.1 {
            10
        } else {
            20
        } as f64;

        let adjusted_price = match side {
            OrderSide::Buy => current_price - price_offset_steps * contract_info.price_precision,
            OrderSide::Sell => current_price + price_offset_steps * contract_info.price_precision,
        };

        // 精度控制
        let adjusted_price = adjust_price_precision(adjusted_price, contract_info.price_precision);

        tracing::info!(
            symbol = %symbol,
            current_price = %current_price,
            price_precision = %contract_info.price_precision,
            tick_size = %tick_size,
            price_offset_steps = %price_offset_steps,
            adjusted_price = %adjusted_price,
            "Tick analysis"
        );

        let (order_type, order_price, time_in_force, order_amount) = if limit_order {
            // order type为limit即是Post Only下单
            let la = limit_amount(
                amount,
                contract_info.min_amount,
                contract_info.amount_precision,
            );
            (
                OrderType::Limit,
                Some(adjusted_price),
                Some(TimeInForce::GTC),
                la,
            )
        } else {
            // 使用市价的价格精度和最小量
            let la = limit_amount(
                amount,
                contract_info.market_min_amount,
                contract_info.market_amount_precision,
            );
            (OrderType::Market, None, None, la)
        };

        if order_amount * current_price < contract_info.min_notional {
            // 小于最小下单金额
            tracing::warn!(
                symbol = %symbol,
                amount = %amount,
                limited_amount = %order_amount,
                order_price = %order_amount * current_price,
                contract_info = ?contract_info,
                order_type = %order_type,
                "Order amount is less than the minimum notional requirement",
            );
            return OrderResult::BelowMin;
        }

        tracing::info!(
            symbol = %symbol,
            amount = %order_amount,
            side = %side,
            position_side = %position_side,
            price = ?order_price,
            order_type = %order_type,
            "Order parameters"
        );

        let order = CustomOrderRequest {
            symbol,
            side,
            position_side: Some(position_side),
            order_type,
            time_in_force,
            qty: Some(order_amount),
            reduce_only: None,
            price: order_price,
            stop_price: None,
            close_position: None,
            activation_price: None,
            callback_rate: None,
            working_type: None,
            price_protect: None,
        };

        let transaction = self.account.custom_order(order).await;

        match transaction {
            Ok(_) => OrderResult::Created,
            Err(e) => OrderResult::Error(e),
        }
    }

    /// # 获取当前账户的交易对持仓
    pub async fn fetch_positions(
        &self,
        symbol: &str,
    ) -> Result<Vec<PositionRisk>, BinanceTraderError> {
        let info = self
            .account
            .position_information(symbol)
            .await
            .context("Failed to get positions")?;

        Ok(info)
    }

    // /// # 获取当前账户所有持仓
    // async fn fetch_all_positions(&self) -> Result<Vec<FuturesPosition>, BinanceTraderError> {
    //     let info = self
    //         .account
    //         .account_information()
    //         .await
    //         .context("Failed to get all positions")?;

    //     let mut positions = Vec::new();
    //     for pos in info.positions {
    //         // 过滤 初始保证金不为0的持仓
    //         if pos.initial_margin != 0.0 {
    //             positions.push(pos);
    //         }
    //     }

    //     Ok(positions)
    // }

    /// # 计算持仓变化
    ///
    /// ## 参数
    ///
    /// symbol: 交易对
    ///
    /// target_weigth: 目标权重
    ///
    /// current_price: 当前价格
    ///
    /// total_balance: 总余额
    ///
    /// ## 返回
    ///
    /// 持仓变化
    /// - 做多: f64
    /// - 做空: f64
    pub async fn calculate_position_change(
        &self,
        symbol: &str,
        target_weight: f64,
        current_price: f64,
        total_balance: f64,
        all_positions: Vec<PositionRisk>,
    ) -> Result<(f64, f64), BinanceTraderError> {
        // 计算目标持仓数量
        let position_quantity = (total_balance * target_weight) / current_price;
        let target_long_quantity = if target_weight > 0.0 {
            position_quantity
        } else {
            0.0
        };
        let target_short_quantity = if target_weight < 0.0 {
            position_quantity.abs()
        } else {
            0.0
        };

        let mut current_long_position = None;
        let mut current_short_position = None;
        let mut current_long_quantity = 0.0;
        let mut current_short_quantity = 0.0;

        for pos in all_positions {
            if pos.symbol != symbol {
                // 过滤无关的交易对
                continue;
            }

            match pos.position_side.as_str() {
                "LONG" => {
                    current_long_quantity = pos.position_amount;
                    current_long_position = Some(pos);
                }
                "SHORT" => {
                    current_short_quantity = pos.position_amount.abs();
                    current_short_position = Some(pos);
                }
                _ => {}
            }
        }

        // 没有持仓，提前结束
        if current_long_position.is_none() || current_short_position.is_none() {
            return Ok((0.0, 0.0));
        }

        // 计算持仓变化
        let long_difference = target_long_quantity - current_long_quantity;
        let short_difference = target_short_quantity - current_short_quantity;

        tracing::info!(
            symbol = %symbol,
            current_long = current_long_quantity,
            current_short = current_short_quantity,
            target_long = target_long_quantity,
            target_short = target_short_quantity,
            long_change = long_difference,
            short_change = short_difference,
            "Position update"
        );
        Ok((long_difference, short_difference))
    }

    /// # 取消 某个合约类型的所有订单
    pub async fn cancel_all_open_orders(&self, symbol: &str) -> Result<(), BinanceTraderError> {
        self.account
            .cancel_all_open_orders(symbol)
            .await
            .context("Failed to cancel all open orders")?;
        Ok(())
    }
}

struct Summarizer {}

impl Summarizer {
    async fn summarize(symbol: String, order_tracer: OrderTracer, err: Option<BinanceTraderError>) {
        let orders = order_tracer.symbols_orders.get(&symbol).unwrap();
        let g = orders.data.lock().await;

        let mut final_price = -1.0;
        let mut filled_amount = 0.0;
        let mut benchmark_price = -1.0;

        let mut count = 0;
        let mut msg = String::new();
        let mut slippage_msg = String::new();
        for (_, order_id) in g.order_id_sequence.iter_rev() {
            // 从最早的订单开始迭代
            let events = g.events.get(order_id).unwrap();

            // 此id最新状态的订单
            let (event_time, order) = events.first().unwrap();
            let accum_qty_filled: f64 = order.accumulated_qty_filled_trades.parse().unwrap();

            if accum_qty_filled <= 0.0 {
                // 无累计成交的订单忽略
                continue;
            }

            // 本次成交均价
            let avg_price: f64 = order.average_price.parse().unwrap();
            if final_price == -1.0 {
                // 第一次订单
                benchmark_price = order.price.parse().unwrap();
                final_price = avg_price;
            } else {
                final_price = (avg_price * accum_qty_filled + final_price * filled_amount)
                    / (accum_qty_filled + filled_amount);
            }

            filled_amount += accum_qty_filled;
            // 计算 slippage，正数表示滑点为正，负数表示滑点为负
            let p1 = benchmark_price;
            let p2 = final_price;
            let side = order.side.as_str();
            let pos_side = order.position_side.as_str();
            let slippage = match side {
                "BUY" => Some((p2 - p1) / p1),
                "SELL" => Some((p1 - p2) / p1),
                _ => None,
            };

            count += 1;
            let local_time_str = timestamp_to_local_time((*event_time).try_into().unwrap());
            slippage_msg = slippage.map_or("N/A".to_string(), |s| format!("{}", s));
            write!(
                &mut msg,
                "* 第{count}次有效订单,成交时间:{local_time_str},订单类型:{order_type},买卖方向:{side},持仓方向:{pos_side},累计成交量:{filled_amount}枚,\
                累计均价:{final_price}U,滑点:{slippage_msg}\n",
                count = count,
                side = side,
                filled_amount = filled_amount,
                final_price = final_price,
                slippage_msg = slippage_msg,
                local_time_str = local_time_str,
                order_type = order.order_type,
            )
            .unwrap();
        }
        if count == 0 {
            return;
        }

        let amount = final_price * filled_amount;

        let (err_hint, err_msg) = err.as_ref().map_or(("", String::new()), |e| {
            ("部分", format!(" ,错误信息:{}", e))
        });

        let header = format!(
            "{symbol} 交易{err_hint}完成,共分成{count}次有效订单, 最终滑点:{slippage_msg},成交量:{filled_amount}枚,\
                成交额:{amount}U,基准价格:{benchmark_price}U,成交价格:{final_price}U{err_msg}\n",
            symbol = symbol,
            filled_amount = filled_amount,
            final_price = final_price,
            slippage_msg = slippage_msg,
            benchmark_price = benchmark_price,
            count = count,
            err_hint = err_hint,
            err_msg = err_msg,
        );

        tracing::warn!("{}{}", header, msg);
    }
}

fn timestamp_to_local_time(timestamp_ms: i64) -> String {
    // 将毫秒时间戳转换为秒（因为 NaiveDateTime 需要秒为单位）
    let timestamp_sec = timestamp_ms / 1000;

    // 解析时间戳
    DateTime::from_timestamp(timestamp_sec, 0)
        .unwrap()
        .with_timezone(&Local)
        .format("%Y-%m-%d %H:%M:%S")
        .to_string()
}

/// 计算精度对应的小数位数（假设 price_precision 是 10 的负整数次幂）
fn adjust_price_precision(price: f64, price_precision: f64) -> f64 {
    let n = (-price_precision.log10()).round() as i32;
    let scale = 10_f64.powi(n); // 计算缩放因子
    (price * scale).round() / scale // 通过缩放避免除法误差
}
