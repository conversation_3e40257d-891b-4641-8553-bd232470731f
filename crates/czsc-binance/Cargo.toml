[package]
name = "czsc-binance"
version.workspace = true
edition.workspace = true
description.workspace = true

[dependencies]
chrono.workspace = true
anyhow.workspace = true
serde.workspace = true
tracing.workspace = true
error-macros = { path = "../error-macros" }
error-support = { path = "../error-support" }
binance = { path = "../binance" }
thiserror.workspace = true
tokio = { version = "1.41.0", features = ["io-util", "net", "rt", "sync"] }
