pub trait Quantile {
    fn quantile(&self, q: f64) -> Option<f64>;
}

impl<'a> Quantile for [f64] {
    fn quantile(&self, q: f64) -> Option<f64> {
        // 检查 q 是否在合法范围内
        if !(0.0..=1.0).contains(&q) {
            return None; // q 不在 [0, 1] 范围内
        }

        let n = self.len();
        if n == 0 {
            // 空数组没有分位数
            return None;
        }

        // 排序数组（不可变引用）
        let mut sorted = self.to_vec();
        sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());

        // 计算位置索引
        let pos = q * (n as f64 - 1.0);
        let lower = pos.floor() as usize;
        let upper = pos.ceil() as usize;
        let fraction = pos - lower as f64;

        // 插值计算
        if lower == upper {
            // 恰好是一个点
            Some(sorted[lower])
        } else {
            Some(sorted[lower] + fraction * (sorted[upper] - sorted[lower]))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_quantile() {
        // pd.Series([1, 2, 3, 4]).quantile(.5)
        let data = vec![1.0, 2.0, 3.0, 4.0];
        assert_eq!(data.quantile(0.5), Some(2.5));
    }
}
