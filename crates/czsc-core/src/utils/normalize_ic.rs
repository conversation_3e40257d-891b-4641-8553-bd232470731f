use super::errors::CoreUtilsErorr;
use anyhow::anyhow;
use polars::prelude::*;

/// ## 因子标准化：缩尾，然后标准化
///
/// 函数计算逻辑：
///
/// 1. 首先，检查因子列x_col是否存在缺失值，如果存在缺失值，则抛出异常，提示缺失值的数量。
///
/// 2. 从kwargs参数中获取缩尾比例q的值，默认为0.05。
///
/// 3. 对因子列进行缩尾操作，首先根据 dt 分组，然后使用lambda函数对每个组内的因子进行缩尾处理，
///    将超过缩尾比例的值截断，并使用scale函数进行标准化。
///
/// 4. 将处理后的因子列重新赋值给原始DataFrame对象的对应列。
pub fn normalize_feature(df: DataFrame, x_col: &str, q: f64) -> Result<DataFrame, CoreUtilsErorr> {
    let x = df.column(x_col)?;

    // 检查 q 是否在合法范围内
    if !(0.0..=1.0).contains(&q) {
        return Err(CoreUtilsErorr::Unexpected(anyhow!(format!(
            "q should be in [0,1], got {q}",
            q = q
        ))));
    }

    // 检查因子列是否有NaN
    let has_na = x.is_null().any();
    if has_na {
        return Err(CoreUtilsErorr::Unexpected(anyhow!(format!(
            "Column `{x_col}` has NaN",
            x_col = x_col
        ))));
    }

    let df = df
        .lazy()
        .with_columns(&[
            // 分位数裁剪
            col(x_col)
                // 裁剪 类似: https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.clip.html
                .clip(
                    // 下界分位数（按dt分组计算）
                    col(x_col)
                        .quantile(lit(q), QuantileInterpolOptions::Linear)
                        .over([col("dt")]),
                    // 上界分位数（按dt分组计算）
                    col(x_col)
                        .quantile(lit(1.0 - q), QuantileInterpolOptions::Linear)
                        .over([col("dt")]),
                )
                .alias("_clipped_temp"),
        ])
        .with_columns(&[
            // 标准化步骤（使用裁剪后的值）, 相当于实现: https://scikit-learn.org/stable/modules/generated/sklearn.preprocessing.scale.html
            //
            // 为什么不需要对原列加 `.over`？
            // 因为 `col("_clipped_temp")` 直接引用列中的原始值，而 `.mean().over([col("dt")])` 和 `.std().over([col("dt")])` 的作用是：按 `dt` 分组计算统计量后，将组内统计量广播（自动对齐）到每一行
            ((col("_clipped_temp") - col("_clipped_temp").mean().over([col("dt")]))
                / col("_clipped_temp").std(0).over([col("dt")]))
            .alias(x_col),
        ])
        .drop(["_clipped_temp"])
        .collect()?;

    Ok(df)
}

#[cfg(test)]
mod tests {
    use polars::df;

    use crate::utils::rounded::RoundToNthDigit;

    use super::*;

    /// 使用python获取验证数据
    ///
    /// ```
    /// df = pd.DataFrame({
    ///     "dt": [
    ///         "2024-10-08",
    ///         # 插入更多数据
    ///     ],
    ///     "F#EXAMPLE#DEFAULT": [
    ///         0.3502900347871812,
    ///         # 插入更多数据
    ///     ]
    /// })
    /// df3 = czsc.normalize_feature(df2, x_col)[["dt", x_col]]
    /// print([round(x, 4) for x in df3[x_col].tolist()])
    /// ```
    #[test]
    fn test_normalize_feature() {
        let df = raw_example_data();
        let x_col = "F#EXAMPLE#DEFAULT";
        let df = normalize_feature(df, x_col, 0.05).unwrap();
        let values: Vec<f64> = df
            .column(x_col)
            .unwrap()
            .f64()
            .unwrap()
            .iter()
            .map(|f| f.unwrap().round_to_4_digit())
            .collect();

        assert_eq!(
            values,
            vec![
                -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -0.9929,
                -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -0.9929, -1.4128, -1.4129,
                1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 1.3686,
                1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 1.3686, 0.7606, 0.7588, -0.3757, -0.3757,
                -0.3757, -0.3757, -0.3757, -0.3757, -0.3757, -0.3757, -0.3757, -0.3757, -0.3757,
                -0.3757, -0.3757, -0.3757, -0.3757, -0.3757, 0.6523, 0.6541
            ]
        );
    }

    fn raw_example_data() -> DataFrame {
        df! {
                "dt" => &[
                "2024-10-08",
                "2024-10-09",
                "2024-10-10",
                "2024-10-11",
                "2024-10-14",
                "2024-10-15",
                "2024-10-16",
                "2024-10-17",
                "2024-10-18",
                "2024-10-21",
                "2024-10-22",
                "2024-10-23",
                "2024-10-24",
                "2024-10-25",
                "2024-10-28",
                "2024-10-29",
                "2024-10-30",
                "2024-10-31",
                "2024-10-08",
                "2024-10-09",
                "2024-10-10",
                "2024-10-11",
                "2024-10-14",
                "2024-10-15",
                "2024-10-16",
                "2024-10-17",
                "2024-10-18",
                "2024-10-21",
                "2024-10-22",
                "2024-10-23",
                "2024-10-24",
                "2024-10-25",
                "2024-10-28",
                "2024-10-29",
                "2024-10-30",
                "2024-10-31",
                "2024-10-08",
                "2024-10-09",
                "2024-10-10",
                "2024-10-11",
                "2024-10-14",
                "2024-10-15",
                "2024-10-16",
                "2024-10-17",
                "2024-10-18",
                "2024-10-21",
                "2024-10-22",
                "2024-10-23",
                "2024-10-24",
                "2024-10-25",
                "2024-10-28",
                "2024-10-29",
                "2024-10-30",
                "2024-10-31",
                ],
                "F#EXAMPLE#DEFAULT" => &[
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        0.2901052792187935,
        -2.8929516717878574,
        -3.0094040987856756,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.5203675448649068,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
        0.3502900347871812,
                    ]
                }
        .unwrap()
    }
}
