use strum_macros::{AsRefStr, Display, EnumString};

#[cfg(feature = "python")]
use pyo3::{pyclass, pymethods};
#[cfg(feature = "python")]
use pyo3_stub_gen::derive::{gen_stub_pyclass_enum, gen_stub_pymethods};

/// 时间周期
#[cfg_attr(feature = "python", gen_stub_pyclass_enum)]
#[cfg_attr(feature = "python", pyclass(eq, eq_int))]
#[derive(
    Debug, PartialOrd, Ord, Clone, Copy, PartialEq, EnumString, AsRefStr, Display, Eq, Hash,
)]
pub enum Freq {
    /// 逐笔
    #[strum(serialize = "Tick")]
    Tick,
    /// 1分钟
    #[strum(serialize = "1分钟")]
    F1,
    /// 2分钟
    #[strum(serialize = "2分钟")]
    F2,
    /// 3分钟
    #[strum(serialize = "3分钟")]
    F3,
    /// 4分钟
    #[strum(serialize = "4分钟")]
    F4,
    /// 5分钟
    #[strum(serialize = "5分钟")]
    F5,
    /// 6分钟
    #[strum(serialize = "6分钟")]
    F6,
    /// 10分钟
    #[strum(serialize = "10分钟")]
    F10,
    /// 12分钟
    #[strum(serialize = "12分钟")]
    F12,
    /// 15分钟
    #[strum(serialize = "15分钟")]
    F15,
    /// 20分钟
    #[strum(serialize = "20分钟")]
    F20,
    /// 30分钟
    #[strum(serialize = "30分钟")]
    F30,
    /// 60分钟
    #[strum(serialize = "60分钟")]
    F60,
    /// 120分钟
    #[strum(serialize = "120分钟")]
    F120,
    /// 240分钟
    #[strum(serialize = "240分钟")]
    F240,
    /// 360分钟
    #[strum(serialize = "360分钟")]
    F360,
    /// 日线
    #[strum(serialize = "日线")]
    D,
    /// 周线
    #[strum(serialize = "周线")]
    W,
    /// 月线
    #[strum(serialize = "月线")]
    M,
    /// 季线
    #[strum(serialize = "季线")]
    S,
    /// 年线
    #[strum(serialize = "年线")]
    Y,
}

#[cfg_attr(feature = "python", gen_stub_pymethods)]
#[cfg_attr(feature = "python", pymethods)]
impl Freq {
    /// 判断是否为分钟级别的周期
    pub fn is_minute_freq(&self) -> bool {
        matches!(
            self,
            Freq::F1
                | Freq::F2
                | Freq::F3
                | Freq::F4
                | Freq::F5
                | Freq::F6
                | Freq::F10
                | Freq::F12
                | Freq::F15
                | Freq::F20
                | Freq::F30
                | Freq::F60
                | Freq::F120
                | Freq::F240
                | Freq::F360
        )
    }

    /// 获取对应的分钟数
    pub fn minutes(&self) -> Option<i64> {
        match self {
            Freq::F1 => Some(1),
            Freq::F2 => Some(2),
            Freq::F3 => Some(3),
            Freq::F4 => Some(4),
            Freq::F5 => Some(5),
            Freq::F6 => Some(6),
            Freq::F10 => Some(10),
            Freq::F12 => Some(12),
            Freq::F15 => Some(15),
            Freq::F20 => Some(20),
            Freq::F30 => Some(30),
            Freq::F60 => Some(60),
            Freq::F120 => Some(120),
            Freq::F240 => Some(240),
            Freq::F360 => Some(360),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_string_to_freq() {
        // 测试从字符串解析为 Freq (EnumString)
        assert_eq!(Freq::from_str("Tick").unwrap(), Freq::Tick);
        assert_eq!(Freq::from_str("1分钟").unwrap(), Freq::F1);
        assert_eq!(Freq::from_str("15分钟").unwrap(), Freq::F15);
        assert_eq!(Freq::from_str("日线").unwrap(), Freq::D);
        assert_eq!(Freq::from_str("周线").unwrap(), Freq::W);
        assert_eq!(Freq::from_str("月线").unwrap(), Freq::M);
        assert_eq!(Freq::from_str("季线").unwrap(), Freq::S);
        assert_eq!(Freq::from_str("年线").unwrap(), Freq::Y);

        // 测试无效输入
        assert!(Freq::from_str("7分钟").is_err());
    }

    #[test]
    fn test_freq_to_string() {
        // 测试 Display trait
        assert_eq!(Freq::Tick.to_string(), "Tick");
        assert_eq!(Freq::F1.to_string(), "1分钟");
        assert_eq!(Freq::F15.to_string(), "15分钟");
        assert_eq!(Freq::D.to_string(), "日线");
        assert_eq!(Freq::W.to_string(), "周线");

        // 测试 AsRefStr trait
        assert_eq!(Freq::Tick.as_ref(), "Tick");
        assert_eq!(Freq::F1.as_ref(), "1分钟");
        assert_eq!(Freq::F15.as_ref(), "15分钟");
        assert_eq!(Freq::D.as_ref(), "日线");
        assert_eq!(Freq::W.as_ref(), "周线");
    }

    #[test]
    fn test_debug_format() {
        // 测试 Debug trait
        assert_eq!(format!("{:?}", Freq::Tick), "Tick");
        assert_eq!(format!("{:?}", Freq::F1), "F1");
        assert_eq!(format!("{:?}", Freq::D), "D");
        assert_eq!(format!("{:?}", Freq::W), "W");
    }

    #[test]
    fn test_clone_and_eq() {
        // 测试 Clone 和 PartialEq
        let freq1 = Freq::F15;
        let freq2 = freq1.clone();
        assert_eq!(freq1, freq2);

        let freq3 = Freq::F30;
        assert_ne!(freq1, freq3);
    }
}
