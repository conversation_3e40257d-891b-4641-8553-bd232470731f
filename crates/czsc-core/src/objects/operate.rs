use strum_macros::{AsRefStr, Display, EnumString};

/// 持有状态
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, EnumString, AsRefStr, Display)]
pub enum Operate {
    /// Hold Long 持多
    #[strum(serialize = "持多")]
    HL,
    /// Hold Short 持空
    #[strum(serialize = "持空")]
    HS,
    /// Hold Other 持币
    #[strum(serialize = "持币")]
    HO,
    /// Long Open 开多
    #[strum(serialize = "开多")]
    LO,
    /// Long Exit 平多
    #[strum(serialize = "平多")]
    LE,
    /// Short Open 开空
    #[strum(serialize = "开空")]
    SO,
    /// Short Exit 平空
    #[strum(serialize = "平空")]
    SE,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_string_to_operate() {
        // 测试从字符串解析为 Operate (EnumString)
        assert_eq!(Operate::from_str("持多").unwrap(), Operate::HL);
        assert_eq!(Operate::from_str("持空").unwrap(), Operate::HS);
        assert_eq!(Operate::from_str("持币").unwrap(), Operate::HO);
        assert_eq!(Operate::from_str("开多").unwrap(), Operate::LO);
        assert_eq!(Operate::from_str("平多").unwrap(), Operate::LE);
        assert_eq!(Operate::from_str("开空").unwrap(), Operate::SO);
        assert_eq!(Operate::from_str("平空").unwrap(), Operate::SE);

        // 测试无效输入
        assert!(Operate::from_str("不存在").is_err());
    }

    #[test]
    fn test_operate_to_string() {
        // 测试 Display trait
        assert_eq!(Operate::HL.to_string(), "持多");
        assert_eq!(Operate::HS.to_string(), "持空");
        assert_eq!(Operate::HO.to_string(), "持币");
        assert_eq!(Operate::LO.to_string(), "开多");
        assert_eq!(Operate::LE.to_string(), "平多");
        assert_eq!(Operate::SO.to_string(), "开空");
        assert_eq!(Operate::SE.to_string(), "平空");

        // 测试 AsRefStr trait
        assert_eq!(Operate::HL.as_ref(), "持多");
        assert_eq!(Operate::HS.as_ref(), "持空");
        assert_eq!(Operate::HO.as_ref(), "持币");
        assert_eq!(Operate::LO.as_ref(), "开多");
        assert_eq!(Operate::LE.as_ref(), "平多");
        assert_eq!(Operate::SO.as_ref(), "开空");
        assert_eq!(Operate::SE.as_ref(), "平空");
    }

    #[test]
    fn test_debug_format() {
        // 测试 Debug trait
        assert_eq!(format!("{:?}", Operate::HL), "HL");
        assert_eq!(format!("{:?}", Operate::HS), "HS");
        assert_eq!(format!("{:?}", Operate::HO), "HO");
    }

    #[test]
    fn test_clone_and_eq() {
        // 测试 Clone 和 PartialEq
        let op1 = Operate::HL;
        let op2 = op1.clone();
        assert_eq!(op1, op2);

        let op3 = Operate::HS;
        assert_ne!(op1, op3);
    }
}
