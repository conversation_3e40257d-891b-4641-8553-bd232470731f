use super::{bar::Symbol, direction::Direction, fx::FX};
use crate::{objects::mark::Mark, utils::rounded::RoundToNthDigit};
use chrono::{DateTime, Utc};
use derive_builder::Builder;
#[cfg(feature = "python")]
#[cfg(feature = "python")]
use pyo3::{pyclass, pymethods};
#[cfg(feature = "python")]
use pyo3_stub_gen::derive::{gen_stub_pyclass, gen_stub_pymethods};

/// 虚拟笔
/// 主要为笔的内部分析提供便利
#[cfg_attr(feature = "python", gen_stub_pyclass)]
#[cfg_attr(feature = "python", pyclass)]
#[derive(Debug, Clone, Builder)]
#[builder(setter(into))]
pub struct FakeBI {
    pub symbol: Symbol,
    pub sdt: DateTime<Utc>,
    pub edt: DateTime<Utc>,
    pub direction: Direction,
    pub high: f64,
    pub low: f64,
    pub power: f64,
}

#[cfg(feature = "python")]
#[cfg_attr(feature = "python", gen_stub_pymethods)]
#[cfg_attr(feature = "python", pymethods)]
impl FakeBI {
    #[getter]
    pub fn symbol(&self) -> &str {
        self.symbol.as_ref()
    }

    #[getter]
    pub fn sdt(&self) -> DateTime<Utc> {
        self.sdt
    }

    #[getter]
    pub fn edt(&self) -> DateTime<Utc> {
        self.edt
    }

    #[getter]
    pub fn direction(&self) -> Direction {
        self.direction
    }

    #[getter]
    pub fn high(&self) -> f64 {
        self.high
    }

    #[getter]
    pub fn low(&self) -> f64 {
        self.low
    }

    #[getter]
    pub fn power(&self) -> f64 {
        self.power
    }

    fn __repr__(&self) -> String {
        format!(
            "FakeBI(symbol={}, sdt={}, edt={}, direction={:?}, high={}, low={}, power={})",
            self.symbol,
            self.sdt.format("%Y-%m-%d %H:%M:%S"),
            self.edt.format("%Y-%m-%d %H:%M:%S"),
            self.direction,
            self.high,
            self.low,
            self.power
        )
    }
}

/// 创建 fake_bis 列表
///
/// # Arguments
///
/// * `fxs` - 分型序列，必须顶底分型交替
///
/// # Returns
///
/// * 返回 FakeBI 的 Vec
pub fn create_fake_bis(fxs: &[FX]) -> Vec<FakeBI> {
    // 如果长度为奇数，移除最后一个元素
    let len = if fxs.len() % 2 != 0 {
        fxs.len() - 1
    } else {
        fxs.len()
    };

    let mut fake_bis = Vec::new();
    for window in fxs[..len].windows(2) {
        let fx1 = &window[0];
        let fx2 = &window[1];
        assert!(fx1.mark != fx2.mark, "相邻分型标记必须不同");

        let fake_bi = match fx1.mark {
            Mark::D => FakeBIBuilder::default()
                .symbol(fx1.symbol.clone())
                .sdt(fx1.dt.clone())
                .edt(fx2.dt.clone())
                .direction(Direction::Up)
                .high(fx2.high)
                .low(fx1.low)
                // 保留2位小数
                .power((fx2.high - fx1.low).round_to_2_digit())
                .build()
                .unwrap(),
            Mark::G => FakeBIBuilder::default()
                .symbol(fx1.symbol.clone())
                .sdt(fx1.dt.clone())
                .edt(fx2.dt.clone())
                .direction(Direction::Down)
                .high(fx1.high)
                .low(fx2.low)
                .power((fx1.high - fx2.low).round_to_2_digit())
                .build()
                .unwrap(),
        };
        fake_bis.push(fake_bi);
    }
    fake_bis
}
