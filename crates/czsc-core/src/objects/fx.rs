#[cfg(feature = "python")]
use crate::objects::bar::RawBar;

use super::{
    bar::{NewBar, Symbol},
    mark::Mark,
};
use chrono::{DateTime, Utc};
use derive_builder::Builder;

#[cfg(feature = "python")]
use pyo3::{Bound, Python, pyclass, pymethods};
#[cfg(feature = "python")]
use pyo3_stub_gen::derive::{gen_stub_pyclass, gen_stub_pymethods};

const POWER_STRONG: &str = "强";
const POWER_MEDIUM: &str = "中";
const POWER_WEAK: &str = "弱";

/// 分型
#[cfg_attr(feature = "python", gen_stub_pyclass)]
#[cfg_attr(feature = "python", pyclass)]
#[derive(Debug, <PERSON><PERSON>, Builder)]
#[builder(setter(into))]
pub struct FX {
    pub symbol: Symbol,
    pub dt: DateTime<Utc>,
    pub mark: Mark,
    pub high: f64,
    pub low: f64,
    pub fx: f64,
    #[builder(default = "Vec::new()")]
    pub elements: Vec<NewBar>,
}

impl FX {
    fn _power_str(&self) -> &str {
        assert_eq!(self.elements.len(), 3);

        let k1 = &self.elements[0];
        let k2 = &self.elements[1];
        let k3 = &self.elements[2];

        match self.mark {
            Mark::D => {
                if k3.close > k1.high {
                    POWER_STRONG
                } else if k3.close > k2.high {
                    POWER_MEDIUM
                } else {
                    POWER_WEAK
                }
            }
            Mark::G => {
                if k3.close < k1.low {
                    POWER_STRONG
                } else if k3.close < k2.low {
                    POWER_MEDIUM
                } else {
                    POWER_WEAK
                }
            }
        }
    }

    fn _power_volume(&self) -> f64 {
        assert_eq!(self.elements.len(), 3);
        self.elements.iter().map(|x| x.vol).sum()
    }

    fn _has_zs(&self) -> bool {
        assert_eq!(self.elements.len(), 3);

        let zd = self
            .elements
            .iter()
            .map(|x| x.low)
            .fold(f64::NEG_INFINITY, f64::max);
        let zg = self
            .elements
            .iter()
            .map(|x| x.high)
            .fold(f64::INFINITY, f64::min);

        zg >= zd
    }
}

#[cfg(not(feature = "python"))]
impl FX {
    /// 判断分型强度
    pub fn power_str(&self) -> &str {
        self._power_str()
    }

    /// 计算成交量力度
    pub fn power_volume(&self) -> f64 {
        self._power_volume()
    }

    /// 判断构成分型的三根无包含K线是否有重叠中枢
    pub fn has_zs(&self) -> bool {
        self._has_zs()
    }
}

#[cfg(feature = "python")]
#[cfg_attr(feature = "python", gen_stub_pymethods)]
#[cfg_attr(feature = "python", pymethods)]
impl FX {
    #[getter]
    pub fn symbol(&self) -> &str {
        self.symbol.as_ref()
    }

    #[getter]
    pub fn dt(&self) -> DateTime<Utc> {
        self.dt
    }

    #[getter]
    pub fn mark(&self) -> Mark {
        self.mark.clone()
    }

    #[getter]
    pub fn high(&self) -> f64 {
        self.high
    }

    #[getter]
    pub fn low(&self) -> f64 {
        self.low
    }

    #[getter]
    pub fn fx(&self) -> f64 {
        self.fx
    }

    /// 获取构成分型的NewBar列表
    #[getter]
    fn new_bars<'py>(&self, py: Python<'py>) -> Vec<Bound<'py, NewBar>> {
        self.elements
            .iter()
            .map(|element| Bound::new(py, element.clone()).unwrap())
            .collect()
    }

    /// 获取原始K线列表（从NewBar的elements中提取）
    #[getter]
    fn raw_bars<'py>(&self, py: Python<'py>) -> Vec<Bound<'py, RawBar>> {
        self.elements
            .iter()
            .flat_map(|new_bar| {
                new_bar
                    .elements
                    .iter()
                    .map(|raw_bar| Bound::new(py, raw_bar.clone()).unwrap())
            })
            .collect()
    }

    /// 判断分型强度
    #[getter]
    pub fn power_str(&self) -> &str {
        self._power_str()
    }

    /// 计算成交量力度
    #[getter]
    pub fn power_volume(&self) -> f64 {
        self._power_volume()
    }

    /// 判断构成分型的三根无包含K线是否有重叠中枢
    #[getter]
    pub fn has_zs(&self) -> bool {
        self._has_zs()
    }

    fn __repr__(&self) -> String {
        format!(
            "FX(symbol={}, dt={}, mark={:?}, fx={})",
            self.symbol,
            self.dt.format("%Y-%m-%d %H:%M:%S"),
            self.mark,
            self.fx
        )
    }
}

pub fn print_fx_list(fxs: &[FX]) {
    println!("{:<12} {:>12} {:>12} {:>12}", "Mark", "High", "Low", "FX");
    println!("{:-<12} {:-^12} {:-^12} {:-^12}", "", "", "", "");

    for fx in fxs {
        println!(
            "{:<12} {:>12.4} {:>12.4} {:>12.4}",
            fx.mark, fx.high, fx.low, fx.fx
        );
    }
}

#[cfg(test)]
pub mod tests {
    use std::sync::Arc;

    use chrono::Utc;

    use crate::objects::bar::NewBarBuilder;

    use super::*;

    #[test]
    fn test_fx_new() {
        let fx1 = FXBuilder::default()
            .symbol(Arc::from("TEST".to_string()))
            .dt(Utc::now())
            .mark(Mark::D)
            .high(0)
            .low(0)
            .fx(0)
            .build()
            .unwrap();
        assert_eq!(fx1.high, 0.0);
    }

    /// 创建一个测试用的底分型
    pub fn create_d_fx() -> FX {
        // 创建测试用的K线数据
        let k1 = NewBarBuilder::default()
            .symbol(Arc::from("TEST".to_string()))
            .dt(Utc::now())
            .id(1)
            .open(8.5)
            .high(9.0)
            .low(8.0)
            .close(8.2)
            .vol(90.0)
            .amount(900.0)
            .build()
            .unwrap();

        let k2 = NewBarBuilder::default()
            .symbol(Arc::from("TEST".to_string()))
            .dt(Utc::now())
            .id(2)
            .open(8)
            .high(8.5)
            .low(7.5)
            .close(8.0)
            .vol(100.0)
            .amount(1000.0)
            .build()
            .unwrap();

        let k3 = NewBarBuilder::default()
            .symbol(Arc::from("TEST".to_string()))
            .dt(Utc::now())
            .id(3)
            .open(8.5)
            .high(9.0)
            .low(8.0)
            .close(8.8)
            .vol(110.0)
            .amount(1100.0)
            .build()
            .unwrap();

        FXBuilder::default()
            .symbol(k1.symbol.clone())
            .dt(k2.dt.clone())
            .mark(Mark::D)
            .high(k2.high)
            .low(k2.low)
            .fx(k2.low)
            .elements(vec![k1.clone(), k2.clone(), k3.clone()])
            .build()
            .unwrap()
    }

    #[test]
    fn test_power_str() {
        let fx_d = create_d_fx();
        assert_eq!(fx_d.power_str(), POWER_MEDIUM);
    }

    #[test]
    fn test_power_volume() {
        let fx_d = create_d_fx();
        assert_eq!(fx_d.power_volume(), 300.0);
    }

    #[test]
    fn test_has_zs() {
        let fx_d = create_d_fx();
        assert_eq!(fx_d.has_zs(), true);
    }
}
