use hashbrown::HashMap;
use serde::{Deserialize, Serialize};

use super::errors::ObjectError;

/// 信号通用常量
pub const SIGNAL_ANY: &str = "任意";

/// 信号
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Signal {
    pub signal: String,
    score: i32,
    k1: String,
    k2: String,
    k3: String,
    v1: String,
    v2: String,
    v3: String,
}

impl Signal {
    pub fn new(
        signal: Option<String>,
        score: i32,
        k1: String,
        k2: String,
        k3: String,
        v1: String,
        v2: String,
        v3: String,
    ) -> Result<Self, ObjectError> {
        if score > 100 || score < 0 {
            return Err(ObjectError::ScoreOutOfRange(score));
        }

        let mut sig = Signal {
            signal: String::new(),
            score,
            k1,
            k2,
            k3,
            v1,
            v2,
            v3,
        };

        if let Some(signal_str) = signal {
            sig.signal = signal_str.clone();
            let parts: Vec<&str> = signal_str.split('_').collect();
            if parts.len() != 7 {
                return Err(ObjectError::InvalidSignalsFormat(Some(signal_str)));
            }
            sig.k1 = parts[0].to_string();
            sig.k2 = parts[1].to_string();
            sig.k3 = parts[2].to_string();
            sig.v1 = parts[3].to_string();
            sig.v2 = parts[4].to_string();
            sig.v3 = parts[5].to_string();
            sig.score = parts[6]
                .parse()
                .map_err(|_| ObjectError::InvalidScoreFormat(parts[6].to_string()))?;
        } else {
            sig.signal = format!(
                "{}_{}_{}_{}_{}_{}_{}",
                sig.k1, sig.k2, sig.k3, sig.v1, sig.v2, sig.v3, sig.score
            );
        }

        Ok(sig)
    }

    /// 获取信号名称
    pub fn key(&self) -> String {
        // 使用迭代器,避免创建中间 Vec
        [&self.k1, &self.k2, &self.k3]
            .iter()
            .filter(|k| k.as_str() != SIGNAL_ANY)
            .map(|s| s.as_str())
            .collect::<Vec<_>>()
            .join("_")
    }

    // 获取信号值
    pub fn value(&self) -> String {
        format!("{}_{}_{}_{}", self.v1, self.v2, self.v3, self.score)
    }

    /// 判断信号是否与信号列表中的值匹配
    /// 代码的执行逻辑如下:
    /// 接收一个字典 signals 作为参数，该字典包含了所有信号的信息。从字典 signals 中获取名称为 key 的信号的值 v。
    /// 如果 v 不存在，则抛出异常。从信号的值 v 中解析出 v1、v2、v3 和 score 四个变量。
    ///
    /// 如果当前信号的得分 score 大于等于目标信号的得分 self.score，则继续执行，否则返回 False。
    /// 如果当前信号的第一个值 v1 等于目标信号的第一个值 self.v1 或者目标信号的第一个值为 "任意"，则继续执行，否则返回 False。
    /// 如果当前信号的第二个值 v2 等于目标信号的第二个值 self.v2 或者目标信号的第二个值为 "任意"，则继续执行，否则返回 False。
    /// 如果当前信号的第三个值 v3 等于目标信号的第三个值 self.v3 或者目标信号的第三个值为 "任意"，则返回 True，否则返回 False。
    pub fn is_match(
        &self,
        signals: &HashMap<String, String>,
    ) -> Result<bool, ObjectError> {
        let key = self.key();
        let value = signals
            .get(&key)
            .ok_or_else(|| ObjectError::SignalKeyNotFound(key))?;

        // 使用 split_once 避免创建 Vec
        let (v1, rest) = value
            .split_once('_')
            .ok_or(ObjectError::MalformedSignalValue(value.to_string()))?;
        let (v2, rest) = rest
            .split_once('_')
            .ok_or(ObjectError::MalformedSignalValue(value.to_string()))?;
        let (v3, score_str) = rest
            .split_once('_')
            .ok_or(ObjectError::MalformedSignalValue(value.to_string()))?;

        let score = score_str
            .parse::<i32>()
            .map_err(|_| ObjectError::InvalidScoreFormat(score_str.to_string()))?;

        Ok(score >= self.score
            && (v1 == self.v1 || self.v1 == SIGNAL_ANY)
            && (v2 == self.v2 || self.v2 == SIGNAL_ANY)
            && (v3 == self.v3 || self.v3 == SIGNAL_ANY))
    }
}
