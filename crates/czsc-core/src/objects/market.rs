use strum_macros::{AsRefStr, Display, EnumString};

#[cfg(feature = "python")]
use pyo3::pyclass;
#[cfg(feature = "python")]
use pyo3_stub_gen::derive::gen_stub_pyclass_enum;

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, EnumString, AsRefStr, Display)]
#[cfg_attr(feature = "python", pyclass(eq, eq_int))]
#[cfg_attr(feature = "python", gen_stub_pyclass_enum)]
pub enum Market {
    /// A股
    #[strum(serialize = "A股")]
    AShare,
    /// 期货
    #[strum(serialize = "期货")]
    Futures,
    /// 默认
    #[strum(serialize = "默认")]
    Default,
}
