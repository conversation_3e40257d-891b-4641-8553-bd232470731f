use super::{bi::BI, direction::Direction};
use chrono::{DateTime, Utc};
use core::f64;

#[derive(Debug, Clone)]
pub struct ZS {
    pub bis: Vec<BI>,
    /// 中枢开始时间
    pub sdt: DateTime<Utc>,
    /// 中枢结束时间
    pub edt: DateTime<Utc>,
    /// 中枢第一笔方向，sdir 是 start direction 的缩写
    pub sdir: Direction,
    /// 中枢倒一笔方向，edir 是 end direction 的缩写
    pub edir: Direction,
    /// 中枢上沿
    pub zg: f64,
    /// 中枢下沿
    pub zd: f64,
    /// 中枢中轴
    pub zz: f64,
    /// 中枢最高点
    pub gg: f64,
    /// 中枢最低点
    pub dd: f64, // todo cache
}

impl ZS {
    pub fn new(bis: Vec<BI>) -> Self {
        let sdt = bis.first().unwrap().start_dt();
        let edt = bis.last().unwrap().end_dt();
        let sdir = bis.first().unwrap().direction;
        let edir = bis.last().unwrap().direction;
        let zg = bis
            .iter()
            .take(3)
            .map(|x| x.get_high())
            .fold(f64::INFINITY, f64::min);
        let zd = bis
            .iter()
            .take(3)
            .map(|x| x.get_low())
            .fold(f64::NEG_INFINITY, f64::max);
        let gg = bis
            .iter()
            .map(|x| x.get_high())
            .fold(f64::NEG_INFINITY, f64::max);
        let dd = bis.iter().map(|x| x.get_low()).fold(f64::INFINITY, f64::min);

        let zz = zd + (zg - zd) * 0.5;

        ZS {
            bis,
            sdt,
            edt,
            sdir,
            edir,
            zg,
            zd,
            zz,
            gg,
            dd,
        }
    }

    /// 中枢是否有效
    pub fn is_valid(&self) -> bool {
        let zg = self.zg;
        let zd = self.zd;
        if zg < zd {
            return false;
        }

        self.bis.iter().all(|bi| {
            // 情况1: 笔的高点在中枢区间内
            let high_in_range = (bi.get_high() <= zg) && (bi.get_high() >= zd);
            // 情况2: 笔的低点在中枢区间内
            let low_in_range = (bi.get_low() <= zg) && (bi.get_low() >= zd);
            // 情况3: 笔完全包含中枢区间
            let contains_range = (bi.get_high() >= zg) && (bi.get_low() <= zd);
            high_in_range || low_in_range || contains_range
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::objects::bi::tests::create_bi;

    #[test]
    fn test_new_zs() {
        let bi1 = create_bi();

        let zs1 = ZS::new(vec![bi1.clone(), bi1.clone(), bi1]);

        println!("{:?}", zs1.sdt);
    }
}
