#[cfg(feature = "python")]
use crate::objects::errors::ObjectError;

use super::freq::Freq;
use chrono::{DateTime, Utc};
use derive_builder::Builder;
use std::sync::Arc;

#[cfg(feature = "python")]
use anyhow::anyhow;
#[cfg(feature = "python")]
use pyo3::{Bound, PyAny, PyResult, Python, pyclass, pymethods, types::PyAnyMethods};
#[cfg(feature = "python")]
use pyo3_stub_gen::derive::{gen_stub_pyclass, gen_stub_pymethods};

// 数据不会被修改，只需要共享一个只读视图
pub type Symbol = Arc<str>;

/// 原始K线元素
#[cfg_attr(feature = "python", gen_stub_pyclass)]
#[cfg_attr(feature = "python", pyclass)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, Builder)]
#[builder(setter(into), pattern = "owned")]
pub struct RawBar {
    pub symbol: Symbol,
    pub dt: DateTime<Utc>,
    #[builder(default = "Freq::Tick")]
    pub freq: Freq,
    /// id 必须是升序
    pub id: i32,
    pub open: f64,
    pub close: f64,
    pub high: f64,
    pub low: f64,
    pub vol: f64,
    pub amount: f64,
}

#[cfg(not(feature = "python"))]
impl RawBar {
    /// 上影
    pub fn upper(&self) -> f64 {
        self.high - self.open.max(self.close)
    }

    /// 下影
    pub fn lower(&self) -> f64 {
        self.open.min(self.close) - self.low
    }

    /// 实体
    pub fn solid(&self) -> f64 {
        (self.open - self.close).abs()
    }
}

#[cfg(feature = "python")]
#[cfg_attr(feature = "python", gen_stub_pymethods)]
#[cfg_attr(feature = "python", pymethods)]
impl RawBar {
    #[new]
    #[pyo3(signature = (symbol, dt, freq, open, close, high, low, vol, amount, id=0))]
    fn new_py(
        symbol: &str,
        dt: &Bound<PyAny>,
        freq: Freq,
        open: f64,
        close: f64,
        high: f64,
        low: f64,
        vol: f64,
        amount: f64,
        id: i32,
    ) -> PyResult<Self> {
        let datetime_utc = parse_py_dt(dt)?;

        Ok(RawBarBuilder::default()
            .symbol(symbol)
            .dt(datetime_utc)
            .freq(Freq::from(freq))
            .open(open)
            .close(close)
            .high(high)
            .low(low)
            .vol(vol)
            .id(id)
            .amount(amount)
            .build()
            .map_err(|e| ObjectError::Unexpected(anyhow!("{:?}", e)))?)
    }

    #[getter]
    pub fn symbol(&self) -> &str {
        self.symbol.as_ref()
    }

    #[getter]
    fn dt(&self) -> i64 {
        self.dt.timestamp()
    }

    #[getter]
    fn freq(&self) -> Freq {
        self.freq
    }

    #[getter]
    fn id(&self) -> i32 {
        self.id
    }

    #[getter]
    fn open(&self) -> f64 {
        self.open
    }

    #[getter]
    fn close(&self) -> f64 {
        self.close
    }

    #[getter]
    fn high(&self) -> f64 {
        self.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.low
    }

    #[getter]
    fn vol(&self) -> f64 {
        self.vol
    }

    #[getter]
    fn amount(&self) -> f64 {
        self.amount
    }

    /// 上影
    #[getter]
    pub fn upper(&self) -> f64 {
        self.high - self.open.max(self.close)
    }

    /// 下影
    #[getter]
    pub fn lower(&self) -> f64 {
        self.open.min(self.close) - self.low
    }

    /// 实体
    #[getter]
    pub fn solid(&self) -> f64 {
        (self.open - self.close).abs()
    }

    fn __repr__(&self) -> String {
        format!(
            "RawBar(symbol={}, dt={}, freq={:?}, id={}, open={}, close={}, high={}, low={}, vol={}, amount={})",
            self.symbol,
            self.dt.format("%Y-%m-%d %H:%M:%S"),
            self.freq,
            self.id,
            self.open,
            self.close,
            self.high,
            self.low,
            self.vol,
            self.amount
        )
    }
}

/// 去除包含关系后的K线元素
#[cfg_attr(feature = "python", gen_stub_pyclass)]
#[cfg_attr(feature = "python", pyclass)]
#[derive(Debug, Clone, Builder)]
#[builder(setter(into))]
pub struct NewBar {
    pub symbol: Symbol,
    pub dt: DateTime<Utc>,
    #[builder(default = "Freq::Tick")]
    pub freq: Freq,
    /// id 必须是升序
    pub id: i32,
    pub open: f64,
    pub close: f64,
    pub high: f64,
    pub low: f64,
    pub vol: f64,
    pub amount: f64,
    /// 存入具有包含关系的原始K线
    #[builder(default = "Vec::new()")]
    pub elements: Vec<RawBar>,
    // todo cache
}

impl AsRef<NewBar> for NewBar {
    fn as_ref(&self) -> &NewBar {
        self
    }
}

#[cfg(feature = "python")]
#[cfg_attr(feature = "python", gen_stub_pymethods)]
#[cfg_attr(feature = "python", pymethods)]
impl NewBar {
    #[new]
    #[pyo3(signature = (symbol, dt, freq, id, open, close, high, low, vol, amount, elements=None))]
    fn new_py(
        symbol: &str,
        dt: &Bound<PyAny>,
        freq: Freq,
        id: i32,
        open: f64,
        close: f64,
        high: f64,
        low: f64,
        vol: f64,
        amount: f64,
        elements: Option<Vec<RawBar>>,
    ) -> PyResult<Self> {
        let datetime_utc = parse_py_dt(dt)?;
        let raw_elements: Vec<RawBar> = elements.unwrap_or_default().into_iter().collect();

        Ok(NewBarBuilder::default()
            .symbol(symbol)
            .dt(datetime_utc)
            .freq(Freq::from(freq))
            .id(id)
            .open(open)
            .close(close)
            .high(high)
            .low(low)
            .vol(vol)
            .amount(amount)
            .elements(raw_elements)
            .build()
            .map_err(|e| ObjectError::Unexpected(anyhow!("{:?}", e)))?)
    }

    #[getter]
    pub fn symbol(&self) -> &str {
        self.symbol.as_ref()
    }

    #[getter]
    fn dt(&self) -> i64 {
        self.dt.timestamp()
    }

    #[getter]
    fn freq(&self) -> Freq {
        self.freq
    }

    #[getter]
    fn id(&self) -> i32 {
        self.id
    }

    #[getter]
    fn open(&self) -> f64 {
        self.open
    }

    #[getter]
    fn close(&self) -> f64 {
        self.close
    }

    #[getter]
    fn high(&self) -> f64 {
        self.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.low
    }

    #[getter]
    fn vol(&self) -> f64 {
        self.vol
    }

    #[getter]
    fn amount(&self) -> f64 {
        self.amount
    }

    /// 获取原始K线列表
    #[getter]
    fn elements<'py>(&self, py: Python<'py>) -> Vec<Bound<'py, RawBar>> {
        self.elements
            .iter()
            .map(|element| Bound::new(py, element.clone()).unwrap())
            .collect()
    }

    /// 获取原始K线列表（别名方法）
    #[getter]
    fn raw_bars<'py>(&self, py: Python<'py>) -> Vec<Bound<'py, RawBar>> {
        self.elements(py)
    }
}

impl NewBar {
    /// 创建新K线的辅助函数
    ///
    /// 出现error的可能性比较小
    pub fn new_from_raw(bar: &RawBar) -> Self {
        NewBarBuilder::default()
            .symbol(bar.symbol.clone())
            .id(bar.id)
            .freq(bar.freq)
            .dt(bar.dt)
            .open(bar.open)
            .close(bar.close)
            .high(bar.high)
            .low(bar.low)
            .vol(bar.vol)
            .amount(bar.amount)
            .elements(vec![bar.clone()])
            .build()
            .unwrap()
    }
}

#[cfg(feature = "python")]
fn parse_py_dt(dt: &Bound<PyAny>) -> PyResult<DateTime<Utc>> {
    // 尝试解析dt参数，支持多种输入格式
    let datetime_utc = if dt.hasattr("timestamp")? {
        // 如果是Python datetime对象（有timestamp方法）
        let timestamp = dt.call_method0("timestamp")?;
        let timestamp_f64: f64 = timestamp.extract()?;
        DateTime::from_timestamp(
            timestamp_f64 as i64,
            (timestamp_f64.fract() * 1_000_000_000.0) as u32,
        )
        .ok_or(ObjectError::Unexpected(anyhow!(
            "Invalid datetime for building RawBar"
        )))?
    } else if let Ok(timestamp) = dt.extract::<i64>() {
        // 如果是时间戳（保持向后兼容）
        DateTime::from_timestamp(timestamp, 0).ok_or(ObjectError::Unexpected(anyhow!(
            "Invalid timestamp for building RawBar"
        )))?
    } else if let Ok(timestamp_f64) = dt.extract::<f64>() {
        // 如果是浮点数时间戳
        DateTime::from_timestamp(
            timestamp_f64 as i64,
            (timestamp_f64.fract() * 1_000_000_000.0) as u32,
        )
        .ok_or(ObjectError::Unexpected(anyhow!(
            "Invalid timestamp for building RawBar"
        )))?
    } else {
        return Err(ObjectError::Unexpected(anyhow!(
            "dt parameter must be a Python datetime object, integer timestamp, or float timestamp"
        ))
        .into());
    };
    Ok(datetime_utc)
}

#[cfg(test)]
mod tests {
    use super::*;

    const TEST: &str = "test";

    #[test]
    fn test_raw_bar_new() {
        let bar = RawBarBuilder::default()
            .symbol(Arc::from(TEST))
            .dt(Utc::now())
            .id(0)
            .open(0)
            .close(0)
            .high(0)
            .low(0)
            .vol(0)
            .amount(0)
            .build()
            .unwrap();
        assert_eq!(bar.freq, Freq::Tick);
    }

    #[test]
    fn test_raw_bar_calculations() {
        // 测试上涨的情况
        let bar = RawBarBuilder::default()
            .symbol(Arc::from(TEST))
            .dt(Utc::now())
            .id(0)
            .open(10)
            .close(12)
            .high(15)
            .low(8)
            .vol(0)
            .amount(0)
            .build()
            .unwrap();

        // 15 - 12 = 3
        assert_eq!(bar.upper(), 3.0);
        // 10 - 8 = 2
        assert_eq!(bar.lower(), 2.0);
        // 10 - 12 = 2
        assert_eq!(bar.solid(), 2.0);

        // 测试下跌的情况
        let bar = RawBarBuilder::default()
            .symbol(Arc::from(TEST))
            .dt(Utc::now())
            .id(0)
            .open(12)
            .close(10)
            .high(15)
            .low(8)
            .vol(0)
            .amount(0)
            .build()
            .unwrap();

        // 15 - 12 = 3
        assert_eq!(bar.upper(), 3.0);
        // 10 - 8 = 2
        assert_eq!(bar.lower(), 2.0);

        // |12 - 10| = 2
        assert_eq!(bar.solid(), 2.0);
    }

    #[test]
    fn test_new_bar() {
        let bar = NewBarBuilder::default()
            .symbol(Arc::from(TEST))
            .dt(Utc::now())
            .id(0)
            .open(0)
            .close(0)
            .high(0)
            .low(0)
            .vol(0)
            .amount(0)
            .build()
            .unwrap();
        assert_eq!(bar.freq, Freq::Tick);
    }

    #[test]
    fn test_new_bar_with_elements() {
        let mut new_bar = NewBarBuilder::default()
            .symbol(Arc::from(TEST))
            .dt(Utc::now())
            .id(0)
            .open(0)
            .close(0)
            .high(0)
            .low(0)
            .vol(0)
            .amount(0)
            .build()
            .unwrap();

        let raw_bar1 = RawBarBuilder::default()
            .symbol(Arc::from(TEST))
            .id(1)
            .dt(Utc::now())
            .freq(Freq::Tick)
            .open(10)
            .close(12)
            .high(15)
            .low(8)
            .vol(100)
            .amount(1000)
            .build()
            .unwrap();

        new_bar.elements.push(raw_bar1.clone());
        assert_eq!(new_bar.elements.len(), 1);
        assert_eq!(new_bar.elements[0].id, 1);
    }
}
