use super::{errors::ObjectError, signal::Signal};
use hashbrown::{HashMap, HashSet};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Factor {
    /// 必须全部满足的信号，至少需要设定一个信号
    signals_all: Vec<Signal>,

    /// 满足其中任一信号，允许为空
    signals_any: Vec<Signal>,

    /// 不能满足其中任一信号，允许为空
    signals_not: Vec<Signal>,

    /// Factor 的名称
    name: String,
}

impl Factor {
    pub fn new(
        signals_all: Vec<Signal>,
        signals_any: Vec<Signal>,
        signals_not: Vec<Signal>,
        name: String,
    ) -> Result<Self, ObjectError> {
        if signals_all.is_empty() {
            return Err(ObjectError::FactorSignalsAllEmpty);
        }

        let mut factor = Factor {
            signals_all,
            signals_any,
            signals_not,
            name,
        };

        // 计算并更新 name 的 hash
        factor.update_name_hash();

        Ok(factor)
    }

    fn update_name_hash(&mut self) {
        // 创建一个用于计算 hash 的临时数据结构
        let mut hash_data = self.dump();
        hash_data.remove("name");

        // 计算 SHA256
        let mut hasher = Sha256::new();
        hasher.update(format!("{:?}", hash_data).as_bytes());
        let hash = hasher.finalize();

        // 取前 4 个字节并转换为大写十六进制
        let hash_prefix = hex::encode(&hash[..2]).to_uppercase();

        // 更新 name
        if self.name.is_empty() {
            self.name = format!("#{}", hash_prefix);
        } else {
            let parts: Vec<&str> = self.name.split('#').collect();
            self.name = format!("{}#{}", parts[0], hash_prefix);
        }
    }

    /// 获取 Factor 的唯一信号列表
    pub fn unique_signals(&self) -> Vec<String> {
        let mut signals = HashSet::new();

        // 收集所有信号
        for signal in &self.signals_all {
            signals.insert(signal.signal.clone());
        }
        for signal in &self.signals_any {
            signals.insert(signal.signal.clone());
        }
        for signal in &self.signals_not {
            signals.insert(signal.signal.clone());
        }

        signals.into_iter().collect()
    }

    /// 判断 factor 是否满足
    pub fn is_match(&self, signals: &HashMap<String, String>) -> Result<bool, ObjectError> {
        // 检查 signals_not
        for signal in &self.signals_not {
            if signal.is_match(signals)? {
                return Ok(false);
            }
        }

        // 检查 signals_all
        for signal in &self.signals_all {
            if !signal.is_match(signals)? {
                return Ok(false);
            }
        }

        // 如果没有 signals_any，则已满足所有条件
        if self.signals_any.is_empty() {
            return Ok(true);
        }

        // 检查 signals_any
        for signal in &self.signals_any {
            if signal.is_match(signals)? {
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// 将 Factor 转存为 HashMap
    pub fn dump(&self) -> HashMap<String, serde_json::Value> {
        let mut map = HashMap::new();

        map.insert(
            "name".to_string(),
            serde_json::Value::String(self.name.clone()),
        );
        map.insert(
            "signals_all".to_string(),
            serde_json::Value::Array(
                self.signals_all
                    .iter()
                    .map(|s| serde_json::Value::String(s.signal.clone()))
                    .collect(),
            ),
        );
        map.insert(
            "signals_any".to_string(),
            serde_json::Value::Array(
                self.signals_any
                    .iter()
                    .map(|s| serde_json::Value::String(s.signal.clone()))
                    .collect(),
            ),
        );
        map.insert(
            "signals_not".to_string(),
            serde_json::Value::Array(
                self.signals_not
                    .iter()
                    .map(|s| serde_json::Value::String(s.signal.clone()))
                    .collect(),
            ),
        );

        map
    }

    /// 从 HashMap 加载 Factor
    pub fn load(raw: HashMap<String, serde_json::Value>) -> Result<Self, ObjectError> {
        let name = match raw.get("name") {
            Some(serde_json::Value::String(s)) => s.clone(),
            _ => String::new(),
        };

        let signals_all = parse_signals_array(raw.get("signals_all"))?;
        let signals_any = parse_signals_array(raw.get("signals_any")).unwrap_or_default();
        let signals_not = parse_signals_array(raw.get("signals_not")).unwrap_or_default();

        Factor::new(signals_all, signals_any, signals_not, name)
    }
}

/// 辅助函数：解析信号数组
fn parse_signals_array(value: Option<&serde_json::Value>) -> Result<Vec<Signal>, ObjectError> {
    match value {
        Some(serde_json::Value::Array(arr)) => {
            let mut signals = Vec::new();
            for v in arr {
                if let serde_json::Value::String(s) = v {
                    let s = Signal::new(
                        Some(s.clone()),
                        0,
                        String::new(),
                        String::new(),
                        String::new(),
                        String::new(),
                        String::new(),
                        String::new(),
                    )?;
                    signals.push(s);
                }
            }
            Ok(signals)
        }
        _ => Err(ObjectError::InvalidSignalsArrayFormat(None)),
    }
}
