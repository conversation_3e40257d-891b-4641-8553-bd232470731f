use strum_macros::{AsRefStr, Display, EnumString};
#[cfg(feature = "python")]
use pyo3::pyclass;
#[cfg(feature = "python")]
use pyo3_stub_gen::derive::gen_stub_pyclass_enum;


/// 方向
#[cfg_attr(feature = "python", gen_stub_pyclass_enum)]
#[cfg_attr(feature = "python", pyclass(eq, eq_int))]
#[derive(Debug, <PERSON>lone, Copy, PartialEq, EnumString, AsRefStr, Display)]
pub enum Direction {
    /// 向上
    #[strum(serialize = "向上")]
    Up,
    /// 向下
    #[strum(serialize = "向下")]
    Down,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_string_to_direction() {
        // 测试从字符串解析为 Direction (EnumString)
        assert_eq!(Direction::from_str("向上").unwrap(), Direction::Up);
        assert_eq!(Direction::from_str("向下").unwrap(), Direction::Down);

        // 测试无效输入
        assert!(Direction::from_str("向左").is_err());
    }

    #[test]
    fn test_direction_to_string() {
        // 测试 Display trait
        assert_eq!(Direction::Up.to_string(), "向上");
        assert_eq!(Direction::Down.to_string(), "向下");

        // 测试 AsRefStr trait
        assert_eq!(Direction::Up.as_ref(), "向上");
        assert_eq!(Direction::Down.as_ref(), "向下");
    }

    #[test]
    fn test_debug_format() {
        // 测试 Debug trait
        assert_eq!(format!("{:?}", Direction::Up), "Up");
        assert_eq!(format!("{:?}", Direction::Down), "Down");
    }

    #[test]
    fn test_clone_and_eq() {
        // 测试 Clone 和 PartialEq
        let dir1 = Direction::Up;
        let dir2 = dir1.clone();
        assert_eq!(dir1, dir2);

        let dir3 = Direction::Down;
        assert_ne!(dir1, dir3);
    }
}
