[package]
name = "czsc-core"
version.workspace = true
edition.workspace = true


[dependencies]
error-macros = { path = "../error-macros" }
error-support = { path = "../error-support" }

chrono.workspace = true
derive_builder.workspace = true
hex.workspace = true
serde.workspace = true
serde_json.workspace = true
sha2.workspace = true
strum.workspace = true
strum_macros.workspace = true
thiserror.workspace = true
anyhow.workspace = true
hashbrown.workspace = true
polars = { workspace = true, features = [] }
pyo3 = { workspace = true, optional = true }
pyo3-stub-gen = { workspace = true, optional = true }

[features]
python = ["pyo3", "pyo3-stub-gen"]
