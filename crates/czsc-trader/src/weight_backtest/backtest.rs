use super::{
    WeightBacktest,
    errors::WeightBackTestError,
    evaluate_pairs::evaluate_pairs,
    report::{Report, StatsReport, SymbolsReport},
    utils::WeightType,
};
use crate::weight_backtest::trade_dir::TradeDir;
use anyhow::Context;
use czsc_core::utils::rounded::RoundToNthDigit;
use czsc_utils::daily_performance::daily_performance;
use hashbrown::HashMap;
use polars::prelude::*;
use rayon::iter::{IntoParallelRefIterator, ParallelIterator};

impl WeightBacktest {
    /// 执行回测逻辑并计算性能指标
    ///
    /// # 参数
    /// * `n_jobs` - 并行处理的线程数，1表示单线程
    /// * `weight_type` - 权重类型，可以是时序策略(TS)或截面策略(CS)
    /// * `yearly_days` - 一年交易日数量，用于年化收益率计算
    ///
    /// # 返回值
    /// * `Result<(), WeightBackTestError>` - 成功返回 ()，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::{WeightBacktest, WeightType};
    ///
    /// // 创建回测实例
    /// let mut wb = // ...
    ///
    /// wb.do_backtest(WeightType::TS, 252).unwrap();
    ///
    /// // 结果存储在 wb.report 中
    /// ```
    pub fn do_backtest(
        &mut self,
        weight_type: WeightType,
        yearly_days: usize,
    ) -> Result<(), WeightBackTestError> {
        let (symbols_report, dailys, pairs) = self
            .process_symbols()
            .context("Failed to process symbols in parallel")?;

        self.dailys = dailys;

        // 计算品种等权日收益
        let daily_equal_weighted_returns = self.calc_daily_equal_weighted_returns(weight_type)?;

        let date_col = daily_equal_weighted_returns.column("date")?.date()?;
        let start_date = date_col.get(0).unwrap();
        let end_date = date_col.get(date_col.len() - 1).unwrap();

        // 开始日期
        let start_date = chrono::NaiveDate::from_ymd_opt(1970, 1, 1)
            .unwrap()
            .checked_add_signed(chrono::Duration::days(start_date as i64))
            .unwrap();

        // 结束日期
        let end_date = chrono::NaiveDate::from_ymd_opt(1970, 1, 1)
            .unwrap()
            .checked_add_signed(chrono::Duration::days(end_date as i64))
            .unwrap();

        // 单利计算日收益数据的各项指标
        let total_col = daily_equal_weighted_returns
            .column("total")?
            .f64()?
            .iter()
            .map(|x| x.unwrap_or(0.0).round_to_4_digit())
            .collect::<Vec<f64>>();
        let dp = daily_performance(&total_col, Some(yearly_days))?;

        let ep = evaluate_pairs(&pairs, TradeDir::LongShort)?;

        // 计算 long_rate 和 short_rate
        let weight_col = self.dfw.column("weight")?;
        let long_count = self
            .dfw
            // 筛选出 weight > 0 的行
            .filter(&weight_col.gt(0)?)?
            // 获取这些行的数量
            .height() as f64;
        let short_count = self
            .dfw
            // 筛选出 weight < 0 的行
            .filter(&weight_col.lt(0)?)?
            .height() as f64;
        let total_rows = self.dfw.height() as f64;
        let long_rate = (long_count / total_rows).round_to_4_digit();
        let short_rate = (short_count / total_rows).round_to_4_digit();

        let (volatility_ratio, relevance_volatility, relevance, relevance_short) =
            self.calc_stats_by_alpha()?;
        let volatility_ratio = volatility_ratio.round_to_4_digit();
        let relevance_volatility = relevance_volatility.round_to_4_digit();
        let relevance = relevance.round_to_4_digit();
        let relevance_short = relevance_short.round_to_4_digit();

        let stats = StatsReport {
            start_date,
            end_date,
            daily_performance: dp,
            evaluate_pairs: ep,
            long_rate,
            short_rate,
            volatility_ratio,
            relevance_volatility,
            relevance,
            relevance_short,
            symbols_count: self.symbols.len(),
        };

        self.report = Some(Report {
            symbols: symbols_report,
            daily_return: daily_equal_weighted_returns,
            stats,
        });

        Ok(())
    }

    /// 并行处理所有交易品种，计算每个品种的日收益率和交易对
    ///
    /// # 返回值
    /// * `Result<(Vec<SymbolsReport>, DataFrame, DataFrame), WeightBackTestError>` - 成功返回：
    ///   - 各品种的报告列表
    ///   - 合并后的日收益率DataFrame
    ///   - 合并后的交易对DataFrame
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// // 创建回测实例
    /// let wb = // ...
    ///
    /// // 并行处理所有品种
    /// let (symbols_report, dailys, pairs) = wb.process_symbols_parallel().unwrap();
    ///
    /// // 使用处理结果进行后续分析
    /// ```
    fn process_symbols(
        &self,
    ) -> Result<(Vec<SymbolsReport>, DataFrame, DataFrame), WeightBackTestError> {
        let digits = 10i64.pow(self.digits as u32);

        let partitions = self.dfw.partition_by(["symbol"], true)?;

        let (mut pair_results, mut daily_results) = rayon::join(
            || {
                partitions
                    .par_iter()
                    .map(|symbol_df| {
                        let symbol =
                            WeightBacktest::get_symbol_str_from_a_symbol_df(&symbol_df).unwrap();

                        let pair =
                            WeightBacktest::calc_symbol_pairs(symbol_df.clone(), symbol, digits)
                                .and_then(|pair| pair.collect().map_err(WeightBackTestError::from));

                        (symbol, pair)
                    })
                    .collect::<HashMap<_, _>>()
            },
            || {
                partitions
                    .par_iter()
                    .map(|symbol_df| {
                        let symbol =
                            WeightBacktest::get_symbol_str_from_a_symbol_df(&symbol_df).unwrap();

                        let daily = WeightBacktest::calc_symbol_daily(
                            symbol_df.clone(),
                            symbol,
                            self.fee_rate,
                        )
                        .collect()
                        .map_err(WeightBackTestError::from);
                        (symbol, daily)
                    })
                    .collect::<HashMap<_, _>>()
            },
        );

        let mut dailys = Vec::with_capacity(self.symbols.len());
        let mut pairs = Vec::with_capacity(self.symbols.len());
        let mut result: Vec<SymbolsReport> = Vec::with_capacity(self.symbols.len());

        for symbol in self.symbols.iter() {
            let daily = daily_results.remove(symbol.as_ref()).unwrap()?;
            let pair = pair_results.remove(symbol.as_ref()).unwrap()?;

            dailys.push(daily.clone().lazy());
            pairs.push(pair.clone().lazy());

            result.push(SymbolsReport {
                symbol: symbol.to_string(),
                daily,
                pair,
            });
        }

        let dailys = concat(
            dailys,
            UnionArgs {
                // rechunk: false,
                ..Default::default()
            },
        )?
        .collect()
        .context("Failed to concat dataframe for dailys")?;

        let pairs = concat(
            pairs,
            UnionArgs {
                ..Default::default()
            },
        )?
        .collect()
        .context("Failed to concat dataframe for pairs")?;

        Ok((result, dailys, pairs))
    }
}
