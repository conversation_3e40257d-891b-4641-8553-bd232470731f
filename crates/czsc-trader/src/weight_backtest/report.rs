use super::evaluate_pairs::EvaluatePairs;
use chrono::NaiveDate;
use czsc_utils::daily_performance::DailyPerformance;
use polars::frame::DataFrame;
use serde::Serialize;
// 从 serde_json 包中导入 Value 和 json 两个组件
// Value 是一个枚举类型,用于表示任意的 JSON 值
// json! 是一个宏,用于方便地构造 JSON 值
use serde_json::{Value, json};

/// 回测报告，包含品种报告、日收益率和统计指标
///
/// # 字段
/// * `symbols` - 各个交易品种的报告列表
/// * `daily_return` - 每日收益率数据
/// * `stats` - 统计指标报告
///
/// # 示例
/// ```
/// use czsc_trader::weight_backtest::{WeightBacktest, WeightType};
///
/// // 创建回测实例
/// let mut wb = // ...
///
/// // 执行回测
/// wb.backtest(Some(1), WeightType::TS, 252).unwrap();
///
/// // 获取报告
/// if let Some(report) = &wb.report {
///     println!("回测结果包含 {} 个品种", report.symbols.len());
///     
///     // 获取日收益率数据
///     let daily_returns = &report.daily_return;
///     
///     // 获取统计指标
///     let stats = &report.stats;
///     println!("策略收益率: {:.2}%", stats.daily_performance.cum_returns * 100.0);
/// }
/// ```
#[derive(Serialize)]
pub struct Report {
    pub symbols: Vec<SymbolsReport>,
    /// 品种等权日收益
    pub daily_return: DataFrame,
    pub stats: StatsReport,
}

/// 单个品种的报告，包含日收益率和交易对数据
///
/// # 字段
/// * `symbol` - 交易品种代码
/// * `daily` - 该品种的每日收益率DataFrame
/// * `pair` - 该品种的交易对DataFrame
///
/// # 示例
/// ```
/// // 获取某个品种的报告
/// let symbol_report = &report.symbols[0];
/// println!("品种: {}", symbol_report.symbol);
///
/// // 查看该品种的交易对数据
/// println!("交易对数据:\n{:?}", symbol_report.pair);
/// ```
#[derive(Serialize)]
pub struct SymbolsReport {
    pub symbol: String,
    pub daily: DataFrame,
    pub pair: DataFrame,
}

/// 统计指标报告，包含回测性能的各项指标
///
/// # 字段
/// * `start_date` - 回测开始日期
/// * `end_date` - 回测结束日期
/// * `daily_performance` - 日收益率绩效指标
/// * `evaluate_pairs` - 交易对评估指标
/// * `long_rate` - 多头仓位比例
/// * `short_rate` - 空头仓位比例
/// * `volatility_ratio` - 波动率比率
/// * `relevance_volatility` - 相关波动率
/// * `relevance` - 相关性
/// * `relevance_short` - 空头相关性
/// * `symbols_count` - 交易品种数量
///
/// # 示例
/// ```
/// // 获取统计指标
/// let stats = &report.stats;
///
/// // 打印基本信息
/// println!("回测期间: {} 至 {}", stats.start_date, stats.end_date);
/// println!("交易品种数: {}", stats.symbols_count);
///
/// // 打印收益指标
/// println!("累计收益率: {:.2}%", stats.daily_performance.cum_returns * 100.0);
/// println!("年化收益率: {:.2}%", stats.daily_performance.cagr * 100.0);
/// println!("夏普比率: {:.2}", stats.daily_performance.sharpe_ratio);
///
/// // 打印交易指标
/// println!("胜率: {:.2}%", stats.evaluate_pairs.win_rate * 100.0);
/// println!("盈亏比: {:.2}", stats.evaluate_pairs.total_profit_loss_ratio);
/// ```
#[derive(Serialize)]
pub struct StatsReport {
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    /// 单利计算日收益数据的各项指标
    pub daily_performance: DailyPerformance,
    pub evaluate_pairs: EvaluatePairs,
    /// 多头占比
    pub long_rate: f64,
    /// 空头占比
    pub short_rate: f64,
    /// 与基准相关性
    pub relevance: f64,
    /// 与基准空头相关性
    pub relevance_short: f64,
    /// 波动比
    pub volatility_ratio: f64,
    /// 与基准波动相关性
    pub relevance_volatility: f64,
    /// 品种数量
    pub symbols_count: usize,
}

impl Into<Value> for Report {
    fn into(self) -> Value {
        let mut result = serde_json::Map::new();

        for symbol in self.symbols {
            result.insert(
                symbol.symbol.into(),
                json!({
                    "daily": symbol.daily,
                    "pairs": symbol.pair,
                }),
            );
        }

        result.insert("品种等权日收益".into(), json!(self.daily_return));

        result.insert("绩效评价".into(), self.stats.into());

        Value::Object(result)
    }
}

impl Into<Value> for StatsReport {
    fn into(self) -> Value {
        let dp = self.daily_performance;
        let ep = self.evaluate_pairs;

        json!({
            "开始日期": self.start_date.to_string(),
            "结束日期": self.end_date.to_string(),
            "绝对收益": dp.absolute_return,
            "年化收益": dp.annual_returns,
            "夏普比率": dp.sharpe_ratio,
            "最大回撤": dp.max_drawdown,
            "卡玛比率": dp.calmar_ratio,
            "日胜率": dp.daily_win_rate,
            "日盈亏比": dp.daily_profit_loss_ratio,
            "日赢面": dp.daily_win_probability,
            "年化波动率": dp.annual_volatility,
            "下行波动率": dp.downside_volatility,
            "非零覆盖": dp.non_zero_coverage,
            "盈亏平衡点": dp.break_even_point,
            "新高间隔": dp.new_high_interval,
            "新高占比": dp.new_high_ratio,
            "回撤风险": dp.drawdown_risk,
            "单笔收益": ep.single_trade_profit,
            "持仓K线数": ep.position_k_days,
            "多头占比": self.long_rate,
            "空头占比": self.short_rate,
            "与基准相关性": self.relevance,
            "与基准空头相关性": self.relevance_short,
            "波动比": self.volatility_ratio,
            "与基准波动相关性": self.relevance_volatility,
            "品种数量": self.symbols_count,
        })
    }
}

// #[cfg(test)]
// mod tests {
//     use super::*;

//     #[test]
//     fn test_1() {

//     }
// }
