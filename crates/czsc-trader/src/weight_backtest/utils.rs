use super::{WeightBacktest, errors::WeightBackTestError};
use anyhow::Context;
use error_support::czsc_bail;
use hashbrown::HashSet;
use polars::lazy::dsl::{mean_horizontal, sum_horizontal};
use polars::prelude::pivot::pivot_stable;
use polars::prelude::*;
use strum_macros::{AsRefStr, Display, EnumString};

#[derive(Debug, <PERSON>lone, Copy, PartialEq, EnumString, AsRefStr, Display)]
pub enum WeightType {
    /// 时序策略
    #[strum(serialize = "ts")]
    TS,
    /// 截面策略
    #[strum(serialize = "cs")]
    CS,
}

impl WeightBacktest {
    /// 从 DataFrame 中的 `symbol` 列获取唯一品种集合
    ///
    /// # 参数
    /// * `df` - 包含 symbol 列的 DataFrame
    ///
    /// # 返回值
    /// * `Result<Vec<Arc<str>>, WeightBackTestError>` - 成功返回唯一品种列表，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use polars::prelude::*;
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// let df = df! {
    ///     "symbol" => &["DLi9001", "DLi9001", "RBi9001", "RBi9001"]
    /// }.unwrap();
    ///
    /// let symbols = WeightBacktest::unique_symbols(&df).unwrap();
    /// assert_eq!(symbols.len(), 2); // 只有两个唯一品种
    /// ```
    pub(crate) fn unique_symbols(df: &DataFrame) -> Result<Vec<Arc<str>>, WeightBackTestError> {
        // 获取符号列
        let symbols_series = df.column("symbol")?.str()?;

        // 创建一个 HashSet，用于收集唯一的符号
        let mut unique_symbols_set = HashSet::new();

        // 迭代符号列，收集非空的符号到 HashSet 中
        for symbol in symbols_series.into_iter().flatten() {
            unique_symbols_set.insert(symbol);
        }

        // 将 HashSet 中的符号收集到 Vec<Arc<str>> 中
        let unique_symbols: Vec<Arc<str>> = unique_symbols_set.into_iter().map(Arc::from).collect();
        Ok(unique_symbols)
    }

    /// 将 DataFrame 中的 `dt` 列转换为 datetime 格式
    ///
    /// # 参数
    /// * `df` - 包含 dt 列的 DataFrame，dt 列为字符串格式的日期时间，如 "2019-01-02 09:01:00"
    ///
    /// # 返回值
    /// * `Result<DataFrame, WeightBackTestError>` - 成功返回转换后的 DataFrame，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use polars::prelude::*;
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// let df = df! {
    ///     "dt" => &["2019-01-02 09:01:00", "2019-01-03 09:02:00"]
    /// }.unwrap();
    ///
    /// let df_converted = WeightBacktest::convert_datetime(df).unwrap();
    /// // dt 列已转换为 datetime 格式
    /// ```
    pub(crate) fn convert_datetime(mut df: DataFrame) -> Result<DataFrame, WeightBackTestError> {
        let dt_col = df.column("dt")?;
        let dt_type = dt_col.dtype();

        match dt_type {
            DataType::Datetime(TimeUnit::Nanoseconds, _) => Ok(df),
            DataType::Datetime(TimeUnit::Milliseconds, _) => {
                // 将纳秒转换为毫秒
                let dt_col = dt_col.cast(&DataType::Datetime(TimeUnit::Milliseconds, None))?;
                df.replace("dt", dt_col)?;
                Ok(df)
            }
            DataType::Int64 => {
                // 如果是整数类型，假设其为 Unix 时间戳，直接转换为毫秒
                let parsed_col = dt_col
                    .i64()?
                    .into_iter()
                    .map(|opt_ts| opt_ts.map(|ts| ts * 1000));
                // 强制转换为 Datetime[ms]
                let dt_s = Series::from_iter(parsed_col)
                    .cast(&DataType::Datetime(TimeUnit::Milliseconds, None))?;
                // 替换原始列
                df.replace("dt", dt_s)?;
                Ok(df)
            }
            DataType::String => {
                let df = df
                    .lazy()
                    .with_column(col("dt").str().to_datetime(
                        Some(TimeUnit::Milliseconds),
                        None,
                        StrptimeOptions {
                            format: Some("%Y-%m-%d %H:%M:%S".into()),
                            strict: true,
                            exact: false,
                            cache: true,
                        },
                        lit("raise"),
                    ))
                    .sort(
                        ["dt"],
                        SortMultipleOptions::default().with_order_descending(false),
                    )
                    .collect()
                    .context("Failed to convert datetime")?;

                Ok(df)
            }
            _ => {
                czsc_bail!("Unsupported datetime type: {:?}", dt_type);
            }
        }
    }

    /// 四舍五入 DataFrame 中的 `weight` 列，保留指定精度
    ///
    /// # 参数
    /// * `df` - 包含 weight 列的 DataFrame
    ///
    /// # 返回值
    /// * `Result<(), WeightBackTestError>` - 成功返回 ()，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use polars::prelude::*;
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// let mut df = df! {
    ///     "weight" => &[0.5116, -0.2523]
    /// }.unwrap();
    ///
    /// WeightBacktest::round_weight(&mut df).unwrap();
    /// // weight 列的值会被四舍五入
    /// ```
    pub(crate) fn round_weight(df: &mut DataFrame) -> Result<(), WeightBackTestError> {
        df.apply("weight", |s| {
            s.f64()
                .unwrap()
                .clone()
                .apply_in_place(|val| (val * 100.0).round() / 100.0)
        })?;
        Ok(())
    }

    /// 计算策略的统计指标，用于评估策略质量
    ///
    /// # 返回值
    /// * `Result<(f64, f64, f64, f64), WeightBackTestError>` - 成功返回一个包含以下指标的元组，失败返回错误：
    ///   - 波动率比率 (volatility_ratio)
    ///   - 相关波动率 (relevance_volatility)
    ///   - 相关性 (relevance)
    ///   - 空头相关性 (relevance_short)
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// // 创建并初始化回测实例
    /// let wb = // ...
    ///
    /// let (volatility_ratio, relevance_volatility, relevance, relevance_short) = wb.calc_stats_by_alpha().unwrap();
    /// // 使用这些指标进行策略质量评估
    /// ```
    pub fn calc_stats_by_alpha(&self) -> Result<(f64, f64, f64, f64), WeightBackTestError> {
        let alpha = self.alpha().collect()?;

        // 提前计算"策略"和"基准"的标准差
        let strategy_std = alpha.column("策略")?.f64()?.std(0).unwrap_or(0.0);
        let benchmark_std = alpha.column("基准")?.f64()?.std(0).unwrap_or(0.0);

        // 波动比
        let volatility_ratio = if benchmark_std > 0.0 {
            strategy_std / benchmark_std
        } else {
            0.0
        };

        // 计算波动相关性和普通相关性
        let result = alpha
            .clone()
            .lazy()
            .with_column(pearson_corr(col("策略"), col("基准").abs(), 1).alias("与基准波动相关性"))
            .with_column(pearson_corr(col("策略"), col("基准"), 1).alias("与基准相关性"))
            .collect()?;

        let relevance_volatility = result
            .column("与基准波动相关性")?
            .f64()?
            .get(0)
            .unwrap_or(0.0);
        let relevance = result.column("与基准相关性")?.f64()?.get(0).unwrap_or(0.0);

        // 计算空头相关性
        let relevance_short = alpha
            .lazy()
            .filter(col("基准").lt(lit(0)))
            .select([pearson_corr(col("策略"), col("基准"), 1).alias("空头相关性")])
            .collect()?
            .column("空头相关性")?
            .f64()?
            .get(0)
            .unwrap_or(0.0);

        Ok((
            volatility_ratio,
            relevance_volatility,
            relevance,
            relevance_short,
        ))
    }

    /// 计算Alpha收益率，用于评估策略的超额收益
    ///
    /// # 返回值
    /// * `LazyFrame` - 包含策略Alpha收益的LazyFrame
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// // 创建并初始化回测实例
    /// let wb = // ...
    ///
    /// let alpha_lf = wb.alpha();
    /// let alpha_df = alpha_lf.collect().unwrap();
    /// // 使用alpha_df进行后续分析
    /// ```
    pub fn alpha(&self) -> LazyFrame {
        self.dailys
            .clone()
            .lazy()
            .group_by([col("date")])
            .agg([
                (col("return").mean() - col("n1b").mean()).alias("超额"), // 计算 "策略" 和 "基准" 的差值作为 "超额"
                col("return").mean().alias("策略"),
                col("n1b").mean().alias("基准"),
            ])
            // 按 "date" 排序
            .sort(["date"], Default::default())
    }

    /// 计算品种等权日收益率
    ///
    /// # 参数
    /// * `weight_type` - 权重类型，可选值：
    ///   - `WeightType::TS`: 时序策略
    ///   - `WeightType::CS`: 截面策略
    ///
    /// # 返回值
    /// * `Result<DataFrame, WeightBackTestError>` - 成功返回包含等权日收益率的DataFrame，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::{WeightBacktest, WeightType};
    ///
    /// // 创建并初始化回测实例
    /// let wb = // ...
    ///
    /// // 计算时序策略的等权日收益率
    /// let daily_returns = wb.calc_daily_equal_weighted_returns(WeightType::TS).unwrap();
    /// // 使用daily_returns进行后续分析
    /// ```
    pub(crate) fn calc_daily_equal_weighted_returns(
        &self,
        weight_type: WeightType,
    ) -> Result<DataFrame, WeightBackTestError> {
        // 数据透视: 将数据从长格式转换为宽格式，使每个 品种 成为单独的列，列中的值为相应的收益率
        // ┌─────────────────────┬─────────┬─────────┬─────────┐
        // │ date                ┆ Symbol1 ┆ Symbol2 ┆ Symbol3 │
        // │ ---                 ┆ ---     ┆ ---     ┆ ---     │
        // │ str                 ┆ f64     ┆ f64     ┆ f64     │
        // ╞═════════════════════╪═════════╪═════════╪═════════╡
        // │ 2019-01-02 09:01:00 ┆ 0.0     ┆ ...     ┆ ...     │
        // │ 2019-01-03 09:02:00 ┆ -0.000102├ ...     ┆ ...     │
        // │ 2019-01-04 09:03:00 ┆ 0.000456├ ...     ┆ ...     │
        // │ 2019-01-05 09:04:00 ┆ -0.00005├ ...     ┆ ...     │
        // │ 2019-01-06 09:05:00 ┆ 0.0     ┆ ...     ┆ ...     │
        // └─────────────────────┴─────────┴─────────┴─────────┘

        let daily_equal_weighted_returns = pivot_stable(
            &self.dailys,
            ["symbol"],
            Some(["date"]),
            Some(["return"]),
            true,
            None,
            None,
        )?
        .fill_null(FillNullStrategy::Zero)?
        .sort(
            ["date"],
            SortMultipleOptions::default().with_order_descending(false),
        )?;

        let daily_equal_weighted_returns = if self.symbols.len() > 999 {
            self.calc_daily_equal_weighted_returns_long(weight_type, daily_equal_weighted_returns)?
        } else {
            self.calc_daily_equal_weighted_returns_short(weight_type, daily_equal_weighted_returns)?
        };

        // 品种等权日收益计算完成 (时序)
        // ┌─────────────────────┬───────────┬───────────┬───────────┬───────────┐
        // │ date                ┆ Symbol1   ┆ Symbol2   ┆ Symbol3   ┆ total     │
        // │ ---                 ┆ ---       ┆ ---       ┆ ---       ┆ ---       │
        // │ str                 ┆ f64       ┆ f64       ┆ f64       ┆ f64       │
        // ╞═════════════════════╪═══════════╪═══════════╪═══════════╪═══════════╡
        // │ 2019-01-02 09:01:00 ┆ 0.0       ┆ ...       ┆ ...       ┆ Average1  │
        // │ 2019-01-03 09:02:00 ┆ -0.000102 ┆ ...       ┆ ...       ┆ Average2  │
        // │ 2019-01-04 09:03:00 ┆ 0.000456  ┆ ...       ┆ ...       ┆ Average3  │
        // │ 2019-01-05 09:04:00 ┆ -0.00005  ┆ ...       ┆ ...       ┆ Average4  │
        // │ 2019-01-06 09:05:00 ┆ 0.0       ┆ ...       ┆ ...       ┆ Average5  │
        // └─────────────────────┴───────────┴───────────┴───────────┴───────────┘

        Ok(daily_equal_weighted_returns)
    }

    /// 使用 Polars `Expr` 加速计算
    pub(crate) fn calc_daily_equal_weighted_returns_short(
        &self,
        weight_type: WeightType,
        mut daily_equal_weighted_returns: DataFrame,
    ) -> Result<DataFrame, WeightBackTestError> {
        let dt_series = daily_equal_weighted_returns.drop_in_place("date")?;

        // 对剩余列求和/Mean
        let agg_expr = match weight_type {
            WeightType::CS => sum_horizontal([col("*")]),
            WeightType::TS => mean_horizontal([col("*")]),
        }?
        .alias("total");

        let mut daily_equal_weighted_returns = daily_equal_weighted_returns
            .lazy()
            .with_column(agg_expr)
            .collect()?;

        daily_equal_weighted_returns.insert_column(0, dt_series)?;

        Ok(daily_equal_weighted_returns)
    }

    pub(crate) fn calc_daily_equal_weighted_returns_long(
        &self,
        weight_type: WeightType,
        mut daily_equal_weighted_returns: DataFrame,
    ) -> Result<DataFrame, WeightBackTestError> {
        // 1. 先定义一个可变变量，存放累加结果，初始化为空 Option<Series>
        let mut sum_series: Option<Series> = None;

        // 2. 遍历 symbols 列，逐列累加
        for col_name in &self.symbols {
            // 取列（Series），unwrap 简化错误处理
            let series = daily_equal_weighted_returns.column(col_name)?;

            // 如果是第一次赋值，直接 clone
            if sum_series.is_none() {
                sum_series = Some(series.clone());
            } else {
                // 否则累加
                sum_series = Some((&sum_series.unwrap() + series)?);
            }
        }

        // 3. 根据权重类型计算 total_series
        let total_series = match weight_type {
            WeightType::TS => sum_series.unwrap() / (self.symbols.len() as f64),
            WeightType::CS => sum_series.unwrap(),
        }
        .rename("total")
        .clone();

        daily_equal_weighted_returns.with_column(total_series)?;

        Ok(daily_equal_weighted_returns)
    }
}
