use czsc_core::objects::errors::ObjectError;
use czsc_utils::errors::UtilsError;
use error_macros::CZSCErrorDerive;
use error_support::expand_error_chain;
use polars::error::PolarsError;
use thiserror::Error;

/// 持仓权重回测过程中可能出现的错误
///
/// 封装了回测过程中可能出现的各种错误类型，包括：
/// - Object: 对象错误
/// - Utils: 工具错误
/// - NoneValue: 空值错误
/// - Polars: 数据处理错误
/// - Unexpected: An unexpected error occurred
///
/// # 示例
/// ```
/// use czsc_trader::weight_backtest::errors::WeightBackTestError;
/// use thiserror::Error;
/// use std::fmt;
///
/// // 自定义错误处理
/// fn process_result<T>(result: Result<T, WeightBackTestError>) -> T {
///     match result {
///         Ok(value) => value,
///         Err(err) => {
///             eprintln!("回测出错: {}", err);
///             panic!("回测过程中出现错误");
///         }
///     }
/// }
/// ```
#[derive(Debug, Error, CZSCErrorDerive)]
pub enum WeightBackTestError {
    #[error("Object: {0}")]
    Object(#[from] ObjectError),

    #[error("Utils: {0}")]
    Utils(#[from] UtilsError),

    #[error("Expected a value for {0}, but got None")]
    NoneValue(String),

    #[error("Polars: {0}")]
    Polars(#[from] PolarsError),

    #[error("{}", expand_error_chain(.0))]
    Unexpected(anyhow::Error),
}
