use super::WeightBacktest;
use crate::weight_backtest::TradeDir;
use crate::weight_backtest::{WeightBackTestError, trade_dir::TradeAction};
use anyhow::Context;
use polars::prelude::*;

#[derive(Debug, Clone, Copy)]
struct Operate {
    volume: i64,
    datetime: i64,
    bar_id: i64,
    price: f64,
    action: TradeAction,
}

impl WeightBacktest {
    /// 计算某个合约的每日收益率
    ///
    /// # 参数
    /// * `symbol` - 合约代码
    ///
    /// # 返回值
    /// * `LazyFrame` - 包含该合约每日收益率的LazyFrame，包括以下列:
    ///   - `n1b`: 品种每日收益率
    ///   - `edge`: 策略每日收益率
    ///   - `turnover`: 当日单边换手率
    ///   - `cost`: 交易成本
    ///   - `return`: 策略实际收益率(edge - cost)
    ///   - `long_weight`: 多头权重
    ///   - `short_weight`: 空头权重
    ///   - `long_return`: 多头部分收益率
    ///   - `short_return`: 空头部分收益率
    ///   - `long_turnover`: 多头换手率
    ///   - `short_turnover`: 空头换手率
    ///   - `symbol`: 合约代码
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    pub(crate) fn calc_symbol_daily(
        symbol_df: DataFrame,
        symbol: &str,
        fee_rate: f64,
    ) -> LazyFrame {
        let dfs = symbol_df
            .lazy()
            // 新增 品种每日收益率(n1b) 列
            .with_column((col("price").shift(lit(-1)) / col("price") - lit(1)).alias("n1b"))
            // 新增 策略每日收益率(edge) 列
            .with_column((col("weight") * col("n1b")).alias("edge"))
            // 新增 当日的单边换手率(turnover) 列
            .with_column(
                (col("weight").shift(lit(1)) - col("weight"))
                    .abs()
                    .alias("turnover"),
            )
            // 新增 交易成本(cost) 列
            .with_column((col("turnover") * fee_rate.into()).alias("cost"))
            // 新增 策略每日收益率(return) 列
            .with_column((col("edge") - col("cost")).alias("return"))
            // 新增 long_weight 列，多头权重
            .with_column(
                when(col("weight").gt(lit(0)))
                    .then(col("weight"))
                    .otherwise(lit(0))
                    .alias("long_weight"),
            )
            // 新增 short_weight 列，空头权重
            .with_column(
                when(col("weight").lt(lit(0)))
                    .then(col("weight"))
                    .otherwise(lit(0))
                    .alias("short_weight"),
            )
            // 新增 long_edge 列，多头收益
            .with_column((col("long_weight") * col("n1b")).alias("long_edge"))
            // 新增 short_edge 列，空头收益
            .with_column((col("short_weight") * col("n1b")).alias("short_edge"))
            .with_column(
                (col("long_weight").shift(lit(1)) - col("long_weight"))
                    .abs()
                    .alias("long_turnover"),
            )
            .with_column(
                (col("short_weight").shift(lit(1)) - col("short_weight"))
                    .abs()
                    .alias("short_turnover"),
            )
            .with_column((col("long_turnover") * fee_rate.into()).alias("long_cost"))
            .with_column((col("short_turnover") * fee_rate.into()).alias("short_cost"))
            .with_column((col("long_edge") - col("long_cost")).alias("long_return"))
            .with_column((col("short_edge") - col("short_cost")).alias("short_return"))
            // 将 datetime 转换为 date 类型
            .with_column(col("dt").cast(DataType::Date).alias("date"))
            // 根据日期进行分组，并对每组进行求和操作，得到每日的总收益、总扣除手续费后的收益和总手续费
            .group_by([col("date")])
            .agg([
                col("n1b").sum(),
                col("edge").sum(),
                col("return").sum(),
                col("cost").sum(),
                col("turnover").sum(),
                col("long_edge").sum(),
                col("short_edge").sum(),
                col("long_cost").sum(),
                col("short_cost").sum(),
                col("long_turnover").sum(),
                col("short_turnover").sum(),
                col("long_return").sum(),
                col("short_return").sum(),
            ])
            // 分组后的 DataFrame 补充 symbol 列
            .with_column(lit(symbol).alias("symbol"));
        dfs
    }

    /// 计算某个合约的交易对
    ///
    /// # 参数
    /// * `symbol` - 合约代码
    ///
    /// # 返回值
    /// * `Result<LazyFrame, WeightBackTestError>` - 成功返回包含该合约交易对的LazyFrame，包括以下列:
    ///   - `交易方向`: 多头或空头
    ///   - `开仓时间`: 开仓时间
    ///   - `平仓时间`: 平仓时间
    ///   - `开仓价格`: 开仓价格
    ///   - `平仓价格`: 平仓价格
    ///   - `持仓K线数`: 持仓持续的K线数量
    ///   - `事件序列`: 如 "开多 -> 平多"
    ///   - `盈亏比例`: 收益百分比
    ///   - `symbol`: 合约代码
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    pub(crate) fn calc_symbol_pairs(
        symbol_df: DataFrame,
        symbol: &str,
        digits: i64,
    ) -> Result<LazyFrame, WeightBackTestError> {
        let dfs = symbol_df
            .lazy()
            .with_column(
                (col("weight") * lit(digits))
                    .cast(DataType::Int64)
                    .alias("volume"),
            )
            .with_column((col("volume").shift(lit(-1)) - col("volume")).alias("operation"))
            .collect()
            .context("Failed to filter symbol")?;

        fn add_operate(operates: &mut Vec<Operate>, op: Operate) {
            for _ in 0..op.volume.abs() {
                operates.push(op);
            }
        }

        let mut operates: Vec<Operate> = vec![];

        // first
        let initial_volume = dfs
            .column("volume")?
            .i64()?
            .get(0)
            .ok_or(WeightBackTestError::NoneValue("volume column".to_string()))?;

        let initial_datetime = dfs
            .column("dt")?
            .datetime()?
            .get(0)
            .ok_or(WeightBackTestError::NoneValue("dt column".to_string()))?;

        let initial_price = dfs
            .column("price")?
            .f64()?
            .get(0)
            .ok_or(WeightBackTestError::NoneValue("price column".to_string()))?;

        if let Some(action) = TradeAction::first_create(initial_volume) {
            add_operate(
                &mut operates,
                Operate {
                    volume: initial_volume,
                    datetime: initial_datetime,
                    bar_id: 1,
                    price: initial_price,
                    action,
                },
            );
        }

        // 3列同时按行遍历
        let volumes = dfs.column("volume")?.i64()?.into_iter();
        let datetimes = dfs.column("dt")?.datetime()?.into_iter();
        let prices = dfs.column("price")?.f64()?.into_iter();

        let combined_iter = volumes
            .zip(datetimes)
            .zip(prices)
            .map(|((volume, datetime), price)| (volume, datetime, price));

        let mut last_volume = initial_volume;

        let mut bar_id = 1;
        for (volume, datetime, price) in combined_iter.skip(1) {
            let volume =
                volume.ok_or(WeightBackTestError::NoneValue("volume column".to_string()))?;
            let datetime =
                datetime.ok_or(WeightBackTestError::NoneValue("dt column".to_string()))?;
            let price = price.ok_or(WeightBackTestError::NoneValue("price column".to_string()))?;

            bar_id += 1;

            match (last_volume, volume) {
                (v, v2) if v >= 0 && v2 >= 0 && v2 > v => {
                    // 开多
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v2 - v,
                            action: TradeAction::OpenLong,
                        },
                    );
                }
                (v, v2) if v >= 0 && v2 >= 0 && v2 < v => {
                    // 平多
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v - v2,
                            action: TradeAction::CloseLong,
                        },
                    );
                }
                (v, v2) if v <= 0 && v2 <= 0 && v2 > v => {
                    // 平空
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v - v2,
                            action: TradeAction::CloseShort,
                        },
                    );
                }
                (v, v2) if v <= 0 && v2 <= 0 && v2 < v => {
                    // 开空
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v - v2,
                            action: TradeAction::OpenShort,
                        },
                    );
                }
                (v, v2) if v >= 0 && v2 <= 0 => {
                    // 多头转换成空头
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v,
                            action: TradeAction::CloseLong,
                        },
                    );
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v2,
                            action: TradeAction::OpenShort,
                        },
                    );
                }
                (v, v2) if v <= 0 && v2 >= 0 => {
                    // 空头转换成多头
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v,
                            action: TradeAction::CloseShort,
                        },
                    );
                    add_operate(
                        &mut operates,
                        Operate {
                            datetime,
                            bar_id,
                            price,
                            volume: v2,
                            action: TradeAction::OpenLong,
                        },
                    );
                }
                _ => {}
            };
            last_volume = volume;
        }

        let mut opens: Vec<Operate> = vec![];
        let mut results: Vec<(TradeDir, i64, i64, f64, f64, i64, &str, f64)> = vec![];

        for op in operates {
            match op.action {
                TradeAction::OpenShort | TradeAction::OpenLong => {
                    opens.push(op);
                    continue;
                }
                _ => {}
            };

            // 如果 opens 为空，说明没有开仓操作，直接跳过
            if opens.is_empty() {
                continue;
            }

            let open_op = opens.pop().unwrap();
            let (p_dir, p_ret) = match open_op.action {
                TradeAction::OpenShort => (
                    TradeDir::Short,
                    ((open_op.price - op.price) / open_op.price * 10000_f64 * 100_f64).round()
                        / 100_f64,
                ),
                TradeAction::OpenLong => (
                    TradeDir::Long,
                    ((op.price - open_op.price) / open_op.price * 10000_f64 * 100_f64).round()
                        / 100_f64,
                ),
                _ => {
                    panic!("should never run here..")
                }
            };

            results.push((
                p_dir,
                open_op.datetime,
                op.datetime,
                open_op.price,
                op.price,
                op.bar_id - open_op.bar_id + 1,
                open_op.action.get_event_seq(op.action),
                p_ret,
            ));
        }

        let dfs = DataFrame::new(vec![
            Series::new(
                "交易方向",
                results.iter().map(|x| x.0.as_ref()).collect::<Vec<_>>(),
            ),
            Series::new("开仓时间", results.iter().map(|x| x.1).collect::<Vec<_>>()),
            Series::new("平仓时间", results.iter().map(|x| x.2).collect::<Vec<_>>()),
            Series::new("开仓价格", results.iter().map(|x| x.3).collect::<Vec<_>>()),
            Series::new("平仓价格", results.iter().map(|x| x.4).collect::<Vec<_>>()),
            Series::new("持仓K线数", results.iter().map(|x| x.5).collect::<Vec<_>>()),
            Series::new("事件序列", results.iter().map(|x| x.6).collect::<Vec<_>>()),
            Series::new("盈亏比例", results.iter().map(|x| x.7).collect::<Vec<_>>()),
        ])?
        .lazy()
        .with_columns([
            col("开仓时间").cast(DataType::Datetime(TimeUnit::Nanoseconds, None)),
            col("平仓时间").cast(DataType::Datetime(TimeUnit::Nanoseconds, None)),
        ])
        .with_column(lit(symbol).alias("symbol"));

        Ok(dfs)
    }

    pub(crate) fn get_symbol_str_from_a_symbol_df(
        symbol_df: &DataFrame,
    ) -> Result<&str, WeightBackTestError> {
        let symbol = symbol_df
            .column("symbol")
            .context("Column 'symbol' not found in DataFrame")?
            .str()
            .context("Column 'symbol' is not of string type")?
            .get(0)
            .context("Failed to get the first element from 'symbol' column")?;
        Ok(symbol)
    }
}
