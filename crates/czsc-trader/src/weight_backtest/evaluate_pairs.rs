use super::{errors::WeightBackTestError, trade_dir::TradeDir};
use czsc_core::utils::rounded::RoundToNthDigit;
use polars::prelude::*;
use serde::Serialize;

#[derive(Serialize)]
pub struct EvaluatePairs {
    /// 交易方向
    pub trade_direction: TradeDir,
    /// 交易次数
    pub trade_count: usize,
    /// 累计收益
    pub total_profit: f64,
    /// 单笔收益
    pub single_trade_profit: f64,
    /// 盈利次数
    pub win_trade_count: usize,
    /// 累计盈利
    pub sum_win: f64,
    /// 单笔盈利
    pub win_one: f64,
    /// 亏损次数
    pub loss_trade_count: usize,
    /// 累计亏损
    pub sum_loss: f64,
    /// 单笔亏损
    pub loss_one: f64,
    /// 交易胜率
    pub win_rate: f64,
    /// 累计盈亏比
    pub total_profit_loss_ratio: f64,
    /// 单笔盈亏比
    pub single_profit_loss_ratio: f64,
    /// 盈亏平衡点
    pub break_even_point: f64,
    /// 持仓K线数
    pub position_k_days: f64,
}

impl Default for EvaluatePairs {
    fn default() -> EvaluatePairs {
        EvaluatePairs {
            trade_direction: TradeDir::LongShort,
            trade_count: 0,
            total_profit: 0.0,
            single_trade_profit: 0.0,
            win_trade_count: 0,
            sum_win: 0.0,
            win_one: 0.0,
            loss_trade_count: 0,
            sum_loss: 0.0,
            loss_one: 0.0,
            win_rate: 0.0,
            total_profit_loss_ratio: 0.0,
            single_profit_loss_ratio: 0.0,
            break_even_point: 0.0,
            position_k_days: 0.0,
        }
    }
}

/// 评估交易对性能，计算各项性能指标
///
/// # 参数
/// * `pairs` - 包含交易对数据的DataFrame，需要包含以下列：
///   - `交易方向`: 多头或空头
///   - `盈亏比例`: 交易收益百分比
///   - `持仓K线数`: 持仓持续的K线数量
/// * `trade_dir` - 交易方向，可以是：
///   - `TradeDir::Long`: 只评估多头交易
///   - `TradeDir::Short`: 只评估空头交易
///   - `TradeDir::LongShort`: 评估多头和空头交易
///
/// # 返回值
/// * `Result<EvaluatePairs, WeightBackTestError>` - 成功返回包含各种性能指标的EvaluatePairs结构体，失败返回错误
///
/// # 示例
/// ```
/// use polars::prelude::*;
/// use czsc_trader::weight_backtest::{evaluate_pairs::evaluate_pairs, trade_dir::TradeDir};
///
/// // 创建交易对DataFrame
/// let pairs_df = df! {
///     "交易方向" => &["多头", "空头", "多头"],
///     "盈亏比例" => &[10.5, -5.2, -3.1],
///     "持仓K线数" => &[15, 10, 5]
/// }.unwrap();
///
/// // 评估所有交易的性能
/// let performance = evaluate_pairs(&pairs_df, TradeDir::LongShort).unwrap();
///
/// // 只评估多头交易的性能
/// let long_performance = evaluate_pairs(&pairs_df, TradeDir::Long).unwrap();
///
/// // 打印结果
/// println!("整体交易胜率: {:.2}%", performance.win_rate * 100.0);
/// println!("多头交易胜率: {:.2}%", long_performance.win_rate * 100.0);
/// ```
pub fn evaluate_pairs(
    pairs: &DataFrame,
    trade_dir: TradeDir,
) -> Result<EvaluatePairs, WeightBackTestError> {
    if pairs.is_empty() {
        return Ok(EvaluatePairs::default());
    }

    let mut pairs = pairs.clone();
    if let TradeDir::Long | TradeDir::Short = trade_dir {
        pairs = pairs
            .lazy()
            .filter(col("交易方向").eq(trade_dir.as_ref()))
            .collect()?;
        if pairs.is_empty() {
            return Ok(EvaluatePairs::default());
        }
    }

    let trade_count = pairs.height() as f64;

    // 盈亏平衡点
    let profit_loss_ra = pairs.column("盈亏比例")?.f64()?;
    let profit_loss_ra = profit_loss_ra.sort(false);
    let mut sum = 0.0;
    let mut break_even_point = 1.0;
    for (i, value) in profit_loss_ra.iter().enumerate() {
        let value = value.unwrap_or(0.0);
        sum += value;

        // 如果首次达到累积收益为正，计算盈亏平衡点
        if sum >= 0.0 && break_even_point == 1.0 {
            break_even_point = (i + 1) as f64 / trade_count;
        }
    }
    if sum <= 0.0 {
        break_even_point = 1.0;
    }

    let position_k_days: f64 = pairs.column("持仓K线数")?.mean().unwrap_or(0.0);

    let win_c = pairs
        .clone()
        .lazy()
        .filter(col("盈亏比例").gt_eq(0))
        .collect()?;
    // 盈利次数
    let win_trade_count = win_c.height();
    // 累计盈利
    let sum_win: f64 = win_c.column("盈亏比例")?.sum()?;
    // 单笔盈利
    let win_one = sum_win / win_trade_count as f64;
    // 交易胜率
    let win_rate = win_trade_count as f64 / trade_count;

    let loss_c = pairs
        .clone()
        .lazy()
        .filter(col("盈亏比例").lt(0))
        .collect()?;
    // 亏损次数
    let loss_trade_count = loss_c.height();
    // 累计亏损
    let sum_loss: f64 = loss_c.column("盈亏比例")?.sum()?;
    // 单笔亏损
    let loss_one = sum_loss / loss_trade_count as f64;

    // 累计盈亏比
    let total_profit_loss_ratio: f64 = sum_win / sum_loss.abs();
    // 单笔盈亏比
    let single_profit_loss_ratio: f64 = win_one / loss_one.abs();

    Ok(EvaluatePairs {
        trade_direction: trade_dir,
        trade_count: trade_count as usize,
        total_profit: sum.round_to_2_digit(),
        single_trade_profit: (sum / trade_count).round_to_2_digit(),
        win_trade_count,
        sum_win: sum_win.round_to_2_digit(),
        win_one: win_one.round_to_4_digit(),
        loss_trade_count,
        sum_loss: sum_loss.round_to_2_digit(),
        loss_one: loss_one.round_to_4_digit(),
        win_rate: win_rate.round_to_4_digit(),
        total_profit_loss_ratio: total_profit_loss_ratio.round_to_4_digit(),
        single_profit_loss_ratio: single_profit_loss_ratio.round_to_4_digit(),
        break_even_point,
        position_k_days: position_k_days.round_to_2_digit(),
    })
}
