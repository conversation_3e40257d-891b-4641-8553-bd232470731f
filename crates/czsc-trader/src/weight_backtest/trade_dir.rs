use serde::Serialize;
use strum_macros::{AsRefStr, Display, EnumString};

pub static OP_OPEN_LONG_CLOSE_SHORT: &str = "开多 -> 平空";
pub static OP_OPEN_LONG_CLOSE_LONG: &str = "开多 -> 平多";
pub static OP_OPEN_SHORT_CLOSE_SHORT: &str = "开空 -> 平空";
pub static OP_OPEN_SHORT_CLOSE_LONG: &str = "开空 -> 平多";
pub static OP_UNKNOWN: &str = "未知操作";

/// 交易方向枚举，用于指定交易策略的方向
///
/// # 成员
/// * `Long` - 只做多头交易
/// * `Short` - 只做空头交易
/// * `LongShort` - 同时做多头和空头交易
///
/// # 示例
/// ```
/// use czsc_trader::weight_backtest::trade_dir::TradeDir;
///
/// // 只评估多头交易
/// let dir = TradeDir::Long;
///
/// // 根据方向进行不同的处理
/// match dir {
///     TradeDir::Long => println!("只处理多头交易"),
///     TradeDir::Short => println!("只处理空头交易"),
///     TradeDir::LongShort => println!("处理多头和空头交易"),
/// }
/// ```
#[derive(Debug, Clone, Copy, PartialEq, EnumString, AsRefStr, Display, Serialize)]
pub enum TradeDir {
    /// 多头
    #[strum(serialize = "多头")]
    Long,
    /// 空头
    #[strum(serialize = "空头")]
    Short,
    /// 多空
    #[strum(serialize = "多空")]
    LongShort,
}

/// 交易动作枚举，用于描述交易行为
///
/// # 成员
/// * `OpenLong` - 开多仓
/// * `CloseLong` - 平多仓
/// * `OpenShort` - 开空仓
/// * `CloseShort` - 平空仓
/// * `None` - 无交易动作
///
/// # 示例
/// ```
/// use czsc_trader::weight_backtest::trade_dir::TradeAction;
///
/// // 创建开多仓交易动作
/// let action = TradeAction::OpenLong;
///
/// // 根据交易动作执行不同操作
/// match action {
///     TradeAction::OpenLong => println!("执行开多仓操作"),
///     TradeAction::CloseLong => println!("执行平多仓操作"),
///     TradeAction::OpenShort => println!("执行开空仓操作"),
///     TradeAction::CloseShort => println!("执行平空仓操作"),
///     TradeAction::None => println!("无交易操作"),
/// }
/// ```
#[derive(Debug, Clone, Copy, PartialEq, EnumString, AsRefStr, Display)]
pub enum TradeAction {
    /// 开空
    #[strum(serialize = "开空")]
    OpenShort,
    /// 开多
    #[strum(serialize = "开多")]
    OpenLong,
    /// 平空
    #[strum(serialize = "平空")]
    CloseShort,
    /// 平多
    #[strum(serialize = "平多")]
    CloseLong,
}

impl TradeAction {
    pub fn first_create(vol: i64) -> Option<Self> {
        match vol {
            vol if vol > 0 => Some(Self::OpenLong),
            vol if vol < 0 => Some(Self::CloseShort),
            _ => None,
        }
    }

    pub fn get_event_seq(&self, op: Self) -> &'static str {
        match (*self, op) {
            (TradeAction::OpenLong, TradeAction::CloseShort) => OP_OPEN_LONG_CLOSE_SHORT,
            (TradeAction::OpenLong, TradeAction::CloseLong) => OP_OPEN_LONG_CLOSE_LONG,
            (TradeAction::OpenShort, TradeAction::CloseShort) => OP_OPEN_SHORT_CLOSE_SHORT,
            (TradeAction::OpenShort, TradeAction::CloseLong) => OP_OPEN_SHORT_CLOSE_LONG,
            _ => OP_UNKNOWN, // 非法组合时返回未知操作
        }
    }
}
