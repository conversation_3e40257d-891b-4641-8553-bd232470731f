use crate::weight_backtest::trade_dir::TradeDir;
use anyhow::Context;
use errors::WeightBackTestError;
use polars::prelude::*;
use report::Report;

mod backtest;
mod calc_symbol;
pub mod errors;
mod evaluate_pairs;
mod report;
mod trade_dir;
mod utils;

pub use utils::WeightType;

/// 持仓权重回测
pub struct WeightBacktest {
    pub dfw: DataFrame,
    pub digits: i64,
    pub fee_rate: f64,
    pub symbols: Vec<Arc<str>>,
    /// 包含品种每日交易信息的 DataFrame
    pub dailys: DataFrame,
    pub report: Option<Report>,
}

impl WeightBacktest {
    /// 创建持仓权重回测对象
    ///
    /// # 参数
    /// * `dfw` - 包含交易数据的DataFrame，需要包含以下列：
    ///   - `dt`: 日期时间列，格式为字符串，如 "2019-01-02 09:01:00"
    ///   - `symbol`: 交易品种代码，如 "DLi9001"
    ///   - `weight`: 持仓权重，取值范围 [-1, 1]，正值表示多头，负值表示空头，0表示无持仓
    ///   - `price`: 交易价格
    /// * `digits` - 价格精度，用于四舍五入
    /// * `fee_rate` - 交易手续费率，如果为 None，则默认为 0.0002 (0.02%)
    ///
    /// # 返回值
    /// * `Result<Self, WeightBackTestError>` - 成功返回 WeightBacktest 实例，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use polars::prelude::*;
    /// use czsc_trader::weight_backtest::WeightBacktest;
    ///
    /// // 创建样例数据
    /// let df = df! {
    ///     "dt" => &[
    ///         "2019-01-02 09:01:00",  // 开多
    ///         "2019-01-03 09:02:00",  // 平多
    ///     ],
    ///     "symbol" => &["DLi9001", "DLi9001"],
    ///     "weight" => &[0.5, 0.0],    // 先开多仓(+0.5)，后平仓(0)
    ///     "price" => &[961.695, 960.720]
    /// }.unwrap();
    ///
    /// // 创建回测实例，价格精度为1，手续费率使用默认值
    /// let wb = WeightBacktest::new(df, 1, None).unwrap();
    /// ```
    pub fn new(
        dfw: DataFrame,
        digits: i64,
        fee_rate: Option<f64>,
    ) -> Result<Self, WeightBackTestError> {
        // dt列格式转换
        let mut dfw = Self::convert_datetime(dfw).context("Failed to convert datetime")?;
        // weight列格式处理
        Self::round_weight(&mut dfw).context("Failed to round weight")?;

        let symbols = Self::unique_symbols(&dfw).context("Failed to unique_symbols")?;

        let wb = Self {
            dfw,
            digits,
            symbols,
            fee_rate: fee_rate.unwrap_or(0.0002),
            dailys: DataFrame::default(),
            report: None,
        };
        Ok(wb)
    }

    /// 执行回测并计算性能指标
    ///
    /// # 参数
    /// * `n_jobs` - 并行处理的线程数，如果为 None，则默认为 4。当设置为 1 时使用单线程
    /// * `weight_type` - 权重类型，可选值:
    ///   - `WeightType::TS`: 时序策略
    ///   - `WeightType::CS`: 截面策略
    /// * `yearly_days` - 一年交易日数量，用于年化收益率计算
    ///
    /// # 返回值
    /// * `Result<(), WeightBackTestError>` - 成功返回 ()，失败返回错误
    ///
    /// # 示例
    /// ```
    /// use czsc_trader::weight_backtest::{WeightBacktest, WeightType};
    ///
    /// // 接上一个例子，创建好 wb 实例后
    /// let mut wb = // ...
    ///
    /// // 执行回测，使用2个线程，时序策略，一年按252个交易日计算
    /// wb.backtest(Some(2), WeightType::TS, 252).unwrap();
    ///
    /// // 获取回测报告
    /// if let Some(report) = &wb.report {
    ///     // 使用报告中的数据...
    /// }
    /// ```
    pub fn backtest(
        &mut self,
        n_jobs: Option<usize>,
        weight_type: WeightType,
        yearly_days: usize,
    ) -> Result<(), WeightBackTestError> {
        let n_jobs = n_jobs.unwrap_or(4);

        let pool = rayon::ThreadPoolBuilder::new()
            .stack_size(64 * 1024 * 1024)
            .num_threads(n_jobs)
            .build()
            .context("Failed to create thread pool")?;

        pool.install(|| self.do_backtest(weight_type, yearly_days))
    }
}

#[cfg(test)]
mod tests {
    use chrono::NaiveDateTime;

    use super::*;

    fn raw_example_data() -> DataFrame {
        df! {
            "dt" => &[
                "2019-01-02 09:01:00",  // 开多
                "2019-01-03 09:02:00",  // 平多
                "2019-01-04 09:03:00",  // 开空
                "2019-01-05 09:04:00",  // 平空
                "2019-01-06 09:05:00"
            ],
            "symbol" => &["DLi9001"; 5],
            "weight" => &[
                0.511,  // 开多（+0.511，增加持仓）
                0.000,  // 平多（减持到 0）
                -0.250, // 开空（-0.250，建立空头）
                0.000,  // 平空（回到 0）
                0.000   // 保持（没有仓位变化）
            ],
            "price" => &[
                961.695, // 开仓价格
                960.720, // 平仓价格
                962.669, // 开仓价格
                960.720, // 平仓价格
                961.695  // 持仓保持价格（无实际意义）
            ]
        }
        .unwrap()
    }

    #[test]
    fn test_calc_symbol_daily() {
        let wb = WeightBacktest::new(raw_example_data(), 1, None).unwrap();
        let dfs = WeightBacktest::calc_symbol_daily(wb.dfw, "DLi9001", wb.fee_rate)
            .collect()
            .unwrap();

        println!("{:?}", dfs);
        // ┌──────┬──────────┬───────────┬──────────┬───┬────────────────┬─────────────┬──────────────┬─────────┐
        // │ date ┆ n1b      ┆ edge      ┆ return   ┆ … ┆ short_turnover ┆ long_return ┆ short_return ┆ symbol  │
        // │ ---  ┆ ---      ┆ ---       ┆ ---      ┆   ┆ ---            ┆ ---         ┆ ---          ┆ ---     │
        // │ date ┆ f64      ┆ f64       ┆ f64      ┆   ┆ f64            ┆ f64         ┆ f64          ┆ str     │
        // ╞══════╪══════════╪═══════════╪══════════╪═══╪════════════════╪═════════════╪══════════════╪═════════╡
        // │ null ┆ 0.000005 ┆ -0.000012 ┆ 0.000304 ┆ … ┆ 0.5            ┆ -0.000102   ┆ 0.000406     ┆ DLi9001 │
        // └──────┴──────────┴───────────┴──────────┴───┴────────────────┴─────────────┴──────────────┴─────────┘
    }

    #[test]
    fn test_round_weight() {
        let mut df = raw_example_data();
        WeightBacktest::round_weight(&mut df).unwrap();
        println!("{:?}", df);
    }

    #[test]
    fn test_convert_datetime() {
        let df = raw_example_data();
        let df = WeightBacktest::convert_datetime(df).unwrap();
        println!("{:?}", df);
    }

    #[test]
    fn test_unique_symbols() {
        let df = raw_example_data();
        let symbols = WeightBacktest::unique_symbols(&df).unwrap();
        assert_eq!(symbols, vec![Arc::from("DLi9001")]);
    }

    #[test]
    fn test_calc_symbol_pairs() {
        let wb = WeightBacktest::new(raw_example_data(), 2, None).unwrap();
        let dfs = WeightBacktest::calc_symbol_pairs(wb.dfw, "DLi9001", 10i64.pow(wb.digits as u32))
            .unwrap()
            .collect()
            .unwrap();

        // std::env::set_var("POLARS_FMT_MAX_ROWS", "100");
        // std::env::set_var("POLARS_FMT_MAX_COLS", "100");
        // println!("{:?}", dfs);

        let open_dt =
            NaiveDateTime::parse_from_str("2019-01-02 09:01:00", "%Y-%m-%d %H:%M:%S").unwrap();

        let mask = dfs
            .column("开仓时间")
            .unwrap()
            .datetime()
            .unwrap()
            .equal(open_dt.and_utc().timestamp_millis());
        let filtered_df = dfs.filter(&mask).unwrap().head(Some(1));

        let example_df = df!(
            "交易方向" => &["多头"],
            "开仓时间" => &[
                open_dt,
            ],
            "平仓时间" => &[
                NaiveDateTime::parse_from_str("2019-01-03 09:02:00", "%Y-%m-%d %H:%M:%S").unwrap(),
            ],
            "开仓价格" => &[961.695f64],
            "平仓价格" => &[960.72f64],
            "持仓K线数" => &[2i64],
            "事件序列" => &["开多 -> 平多"],
            "盈亏比例" => &[-10.14f64],
            "symbol" => &["DLi9001"]
        )
        .unwrap();
        assert!(filtered_df.equals(&example_df));
    }
}
