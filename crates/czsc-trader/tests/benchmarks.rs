use criterion::{Criterion, criterion_group, criterion_main};
use czsc_trader::weight_backtest::{WeightBacktest, WeightType};
use polars::prelude::*;
use std::hint::black_box;

fn read_feather_sync(path: &str) -> DataFrame {
    // 打开文件
    IpcReader::new(std::fs::File::open(path).expect("文件不存在"))
        .finish()
        .expect("Feather 格式错误")
}

fn backtest() -> Result<(), Box<dyn std::error::Error>> {
    let df = read_feather_sync(r"A:\桌面临时数据\全A日线测试_20170101_20250429.feather");
    let mut wbt = WeightBacktest::new(df, 2, None)?;
    let _ = wbt.backtest(Some(50), WeightType::TS, 252);
    Ok(())
}

fn bench_backtest(c: &mut Criterion) {
    c.bench_function("czsc_trader backtest", |b| {
        b.iter(|| {
            // let _guard = flame::start_guard("iteration");
            black_box(backtest())
        }) // black_box 阻止编译器优化
    });
}

criterion_group!(
    name = benches;
    config = Criterion::default().sample_size(10); // 样本数量（默认100）
    targets = bench_backtest // 定义测试组
);
criterion_main!(benches); // 生成 main 函数
// cargo bench --package czsc-trader --release
