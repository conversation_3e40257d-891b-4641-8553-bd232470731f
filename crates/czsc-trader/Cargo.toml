[package]
name = "czsc-trader"
version.workspace = true
edition.workspace = true

[dependencies]
czsc-core = { path = "../czsc-core" }
czsc-utils = { path = "../czsc-utils", features = ["backtest"] }
error-macros = { path = "../error-macros" }
error-support = { path = "../error-support" }

polars = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
polars-plan = { workspace = true }
thiserror.workspace = true
anyhow.workspace = true
serde.workspace = true
serde_json.workspace = true
strum.workspace = true
strum_macros.workspace = true
rayon.workspace = true
hashbrown.workspace = true
criterion = "0.7.0"

[[bench]]
name = "benchmarks"
path = "tests/benchmarks.rs"
harness = false

