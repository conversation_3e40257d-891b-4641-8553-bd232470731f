use crate::data_client::{DataClient, result_to_dataframe};
use crate::errors::UtilsError;
use anyhow::Context;
use polars::{
    frame::DataFrame,
    prelude::{DataType, IntoLazy, col},
};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::collections::HashMap;

/// 策略配置
#[derive(Serialize, Deserialize, PartialEq, Debug, Clone)]
pub struct StrategyConfig {
    pub base_freq: Option<String>,
    pub description: Option<String>,
    pub name: String,
    pub outsample_sdt: Option<String>,
    /// 权重
    #[serde(rename = "权重")]
    pub weight: f64,
}

/// 合约权重
pub struct Weight<'a> {
    /// 策略名称
    pub strategy: &'a str,
    /// 策略指导权重
    pub weight: f64,
}

/// # 单策略获取并计算合约权重
///
/// ## 参数:
///
/// - df: 该策略最新持仓权重, 仅包含相同datetime的权重数据，必须有`symbol`,`weight`列
///
/// - symbols_weights: 保存: [权重名称]->list[权重]的哈希表
///
/// strategy: 该策略配置
async fn calc_weights_by_strategy<'a>(
    df: DataFrame,
    symbols_weights: &mut HashMap<String, Vec<Weight<'a>>>,
    // config_weights: &mut HashMap<&'a str, f64>,
    strategy: &'a StrategyConfig,
) -> Result<(), UtilsError> {
    if df.height() == 0 {
        // 没有数据
        return Ok(());
    }

    // 清洗、格式化数据
    let df = df
        .lazy()
        .with_column(col("weight").cast(DataType::Float64))
        .collect()
        .context("Failed to clean latest weight df")?;

    // 分别获取 symbol, weight 列
    let mut symbol_col = df.column("symbol")?.str()?.into_iter().flatten();
    let mut weight_col = df.column("weight")?.f64()?.into_iter().flatten();

    let _symbol_weight_ratio = strategy.weight / (df.height() as f64);

    // 此loop既是按行遍历
    while let (Some(sym_symbol), Some(sym_weight)) = (symbol_col.next(), weight_col.next()) {
        symbols_weights
            .entry(sym_symbol.into())
            .or_insert(Vec::new())
            .push(Weight {
                strategy: &strategy.name,
                weight: sym_weight,
            });
    }

    Ok(())
}

/// # 计算策略组合的权重
/// 从数据中心获取最新权重, 计算这个策略的所有合约权重, 每个合约的目标权重
///
/// ## 参数:
///
/// - dc: 数据中心实例
///
/// - strategies: 策略组合
///
/// - long_leverage: 做多杠杆
///
/// - short_leverage: 做空杠杆
///
/// ## 返回: 关联合约和它的目标权重映射
///
/// 例如: {'BTC_2H_P03': 0.27, 'BTC_2H_P04': 0.73}
pub async fn calculate_portfolio<'a>(
    dc: &DataClient<'a>,
    strategies: &'a Vec<StrategyConfig>,
    long_leverage: f64,
    short_leverage: f64,
) -> Result<HashMap<String, f64>, UtilsError> {
    let mut symbols_weights: HashMap<String, Vec<Weight>> = HashMap::new();

    for strategy in strategies.iter() {
        // 从数据中心获取最新权重
        let df = get_latest_weights_by_strategy(dc, &strategy.name).await?;
        // 计算这个策略的所有合约权重
        calc_weights_by_strategy(df, &mut symbols_weights, strategy).await?;
    }

    // 每个合约的目标权重
    let mut symbols_target_weights: HashMap<String, f64> = HashMap::new();
    for (symbol, weights) in symbols_weights {
        // 汇总每个品种的目标权重
        let mut target_weight: f64 = weights.iter().map(|w| w.weight).sum();

        // 根据多空杠杆调整权重
        if target_weight > 0.0 {
            target_weight = long_leverage * target_weight
        } else {
            target_weight = short_leverage * target_weight
        }

        symbols_target_weights.insert(symbol, target_weight);
    }
    Ok(symbols_target_weights)
}

/// ## 获取某个策略的最新持仓权重
/// 
/// ```rust
/// let dc = new_czsc_dc_from_env()?;
/// ```
pub async fn get_latest_weights_by_strategy<'a>(
    dc: &DataClient<'a>,
    strategy_name: &str,
) -> Result<DataFrame, UtilsError> {
    let params = json!({
        "v": 2,
        "ttl": 0,
        "hist": false
    });
    let resp = dc
        .post_request(None, strategy_name, Some(params), None, None)
        .await
        .context("Failed to get latest weights")?;

    let df = result_to_dataframe(resp)?;

    Ok(df)
}
