use crate::errors::UtilsError;
use anyhow::anyhow;
use chrono::NaiveDate;
use core::f64;
use polars::{df, frame::DataFrame};
use czsc_core::utils::quantile::Quantile;

/// 回撤值: 当前净值与历史最高净值的差值，负值表示回撤
pub(crate) fn calc_underwater(returns: &[f64]) -> Vec<f64> {
    let mut sum = 0.0;
    let mut sum_max_so_far = f64::NEG_INFINITY;

    returns
        .iter()
        .map(|&r| {
            sum += r;
            sum_max_so_far = sum_max_so_far.max(sum);
            sum - sum_max_so_far
        })
        .collect()
}

pub(crate) fn calc_underwater_valley(underwater: &[f64]) -> Option<usize> {
    underwater
        .iter()
        .enumerate()
        .filter(|&(_, &val)| !val.is_nan() && val != 0.0)
        .min_by(|&(_, val1), &(_, val2)| val1.partial_cmp(val2).unwrap())
        .map(|(i, _)| i)
}

pub(crate) fn calc_underwater_peak(underwater: &[f64], valley: usize) -> usize {
    underwater
        .iter()
        .enumerate()
        .rev()
        .skip(underwater.len() - 1 - valley)
        .find(|&(_, &x)| x == 0.0)
        .map(|(i, _)| i)
        .unwrap_or(0)
}

pub(crate) fn calc_underwater_recovery(underwater: &[f64], valley: usize) -> Option<usize> {
    underwater
        .iter()
        .enumerate()
        .skip(valley)
        .find(|&(_, &x)| x == 0.0)
        .map(|(i, _)| i)
}

/// 分析最大回撤，返回最大回撤的波峰、波谷、恢复日期、回撤天数、恢复天数
pub fn top_drawdowns(
    returns: &[f64],
    dates: &[NaiveDate],
    top: Option<usize>,
) -> Result<DataFrame, UtilsError> {
    if returns.len() != dates.len() {
        return Err(UtilsError::Unexpected(anyhow!(
            "returns.len() should be equal to dates.len()"
        )));
    }
    if returns.is_empty() {
        return Err(UtilsError::ReturnsEmpty);
    }

    let top = top.unwrap_or(10);
    let mut underwater = calc_underwater(returns);

    let mut drawdown_start_dates = Vec::with_capacity(top);
    let mut drawdown_end_dates = Vec::with_capacity(top);
    let mut drawdowns = Vec::with_capacity(top);
    let mut drawdown_days = Vec::with_capacity(top);
    let mut recovery_dates = Vec::with_capacity(top);
    let mut recovery_days = Vec::with_capacity(top);
    let mut new_high_inteval = Vec::with_capacity(top);

    for _ in 0..top {
        // 寻找 最大回撤点
        // 2023-01-01    0
        // 2023-01-02    0
        // 2023-01-03   -3
        // 2023-01-04   -4
        // 2023-01-05    0
        // valley将是2023-01-04
        let valley = calc_underwater_valley(&underwater);

        if valley.is_none() {
            break;
        }
        let valley = valley.unwrap();

        // 寻找 回撤开始, 从最大回撤点向前找第一个值为0.0的索引
        // 2023-01-01    0
        // 2023-01-02    0
        // 2023-01-03   -3
        // 2023-01-04   -4
        // 2023-01-05    0
        // peak将是2023-01-02
        let peak = calc_underwater_peak(&underwater, valley);

        // 寻找 回撤修复, 从最大回撤点向后找第一个值为0.0的索引,可能没有
        let recovery = calc_underwater_recovery(&underwater, valley);

        // 回撤开始
        drawdown_start_dates.push(dates[peak]);
        // 回撤结束
        drawdown_end_dates.push(dates[valley]);
        // 净值回撤
        drawdowns.push(underwater[valley]);
        // 回撤天数
        let drawdown_day = (dates[valley] - dates[peak]).num_days();
        drawdown_days.push(drawdown_day);
        // 回撤修复
        // 恢复天数
        if recovery.is_none() {
            recovery_dates.push(None);
            recovery_days.push(None);
            new_high_inteval.push(None);

            underwater[peak..returns.len()].fill(0.0);
        } else {
            let recovery = recovery.unwrap();
            recovery_dates.push(Some(dates[recovery]));
            let recovery_day = (dates[recovery] - dates[valley]).num_days();
            recovery_days.push(Some(recovery_day));
            new_high_inteval.push(Some(drawdown_day + recovery_day));

            underwater[peak..recovery].fill(0.0);
        }
    }

    let df = df!(
        "回撤开始" => drawdown_start_dates,
        "回撤结束" => drawdown_end_dates,
        "回撤修复" => recovery_dates,
        "净值回撤" => drawdowns,
        "回撤天数" => drawdown_days,
        "恢复天数" => recovery_days,
        "新高间隔" => new_high_inteval,
    )?;
    Ok(df)
}
pub fn calc_drawdowns(returns: &[f64]) -> (Vec<f64>, Vec<Option<f64>>){
  // 初始化累计收益率，从0开始累加
  let mut cum_ret = 0.0;
  // 初始化历史最大累计收益率，使用负无穷保证第一个值会被更新
  let mut cum_max = f64::NEG_INFINITY;
  // 预分配结果向量内存，容量与输入长度相同以提高性能
  // with_capacity 避免动态扩容时的内存重新分配
  let mut drawdown = Vec::with_capacity(returns.len());

  for &r in returns {
      // 更新累计收益率：加上当前周期的收益率
      cum_ret += r;
      // 更新历史最大累计收益率：比较当前累计值和历史最大值
      cum_max = cum_max.max(cum_ret);
      // 当 cum_ret < cum_max 时，回撤为负值，表示从峰值回落
      // 当 cum_ret == cum_max 时，回撤为0，表示持平
      // 当 cum_ret > cum_max 时，cum_max会立即更新，此时回撤为0
      drawdown.push(cum_ret - cum_max);
  }
  let quantiles = [0.1, 0.3, 0.5];
  let quantile_vals = quantiles
        .iter()
        .map(|&q| drawdown.quantile(q))
        .collect::<Vec<_>>();
  (drawdown, quantile_vals)
}
// fn calc_top_drawdowns(
//   daily_df: &DataFrame,
// ) -> Result<(Vec<f64>, Vec<Option<f64>>, DataFrame), StrategyError> {
//   let dates = daily_df
//       .column("dt")?
//       .date()?
//       .as_date_iter()
//       .flatten()
//       .collect::<Vec<_>>();

//   let returns: Vec<_> = daily_df
//       .column(&EQUAL_WEIGHTED_COL_NAME)?
//       .f64()?
//       .into_no_null_iter()
//       .collect();
//   let res = top_drawdowns(&returns, &dates, Some(10))?;
//   let net_drawdowns = calc_net_drawdowns(&returns);

//   let quantiles = [0.1, 0.3, 0.5]
//       .iter()
//       .map(|&q| net_drawdowns.quantile(q))
//       .collect::<Vec<_>>();

//   Ok((net_drawdowns, quantiles, res))
// }

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn tets_calc_underwater() {
        let returns = [1.0, 2.0, -3.0, -1.0, 5.0, 1.0, -7.0, 6.0, 16.0];
        let underwater = calc_underwater(&returns);
        assert_eq!(
            underwater,
            [0.0, 0.0, -3.0, -4.0, 0.0, 0.0, -7.0, -1.0, 0.0]
        );
        // println!("{:?}", underwater);
    }

    #[test]
    fn test_top_drawdowns() {
        let dates = vec![
            NaiveDate::from_ymd_opt(2024, 12, 1).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 2).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 3).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 4).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 5).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 6).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 7).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 8).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 9).unwrap(),
        ];
        let returns = [1.0, 2.0, -3.0, -1.0, 5.0, 1.0, -7.0, 6.0, 16.0];
        let res = top_drawdowns(&returns, &dates, Some(2));

        let drawdown_start_dates = vec![
            NaiveDate::from_ymd_opt(2024, 12, 6).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 2).unwrap(),
        ];
        let drawdown_end_dates = vec![
            NaiveDate::from_ymd_opt(2024, 12, 7).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 4).unwrap(),
        ];
        let recovery_dates = vec![
            NaiveDate::from_ymd_opt(2024, 12, 9).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 5).unwrap(),
        ];
        let drawdowns = [-7.0, -4.0];
        let drawdown_days = [1 as i64, 2 as i64];
        let recovery_days = [2 as i64, 1 as i64];
        let new_high_inteval = [3 as i64, 3 as i64];

        let expected_res = df!(
            "回撤开始" => drawdown_start_dates,
            "回撤结束" => drawdown_end_dates,
            "回撤修复" => recovery_dates,
            "净值回撤" => drawdowns,
            "回撤天数" => drawdown_days,
            "恢复天数" => recovery_days,
            "新高间隔" => new_high_inteval,
        )
        .unwrap();

        assert_eq!(res.unwrap(), expected_res);
        // println!("{:?}", res);
    }

    #[test]
    fn test_top_drawdowns2() {
        let dates = vec![
            NaiveDate::from_ymd_opt(2024, 12, 1).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 2).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 3).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 4).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 5).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 6).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 7).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 8).unwrap(),
            NaiveDate::from_ymd_opt(2024, 12, 9).unwrap(),
        ];
        let returns = [1.0, 2.0, -3.0, -1.0, 5.0, 1.0, -7.0, -6.0, -16.0];
        let res = top_drawdowns(&returns, &dates, Some(10));

        println!("{:?}", res);
    }
}
