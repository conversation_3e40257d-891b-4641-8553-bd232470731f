use crate::errors::UtilsError;
use anyhow::Context;
use error_support::czsc_bail;
use polars::{frame::DataFrame, prelude::NamedFrom, series::Series};
use reqwest::Url;
use serde_json::json;
use std::{borrow::Cow, collections::HashSet, env, fs, time::Duration};

#[cfg(debug_assertions)]
use tracing::info;

pub const DEFAULT_URL: &str = "http://api.tushare.pro";
pub const DEFAULT_CZSC_URL: &str = "http://zbczsc.com:9106";

pub struct DataClient<'a> {
    pub base_url: Url,
    pub token: Cow<'a, str>,
}

impl<'a> DataClient<'a> {
    pub fn new<A, T>(base_url: Option<A>, token: T) -> Self
    where
        A: Into<Cow<'a, str>>,
        T: Into<Cow<'a, str>>,
    {
        // 尝试解析传入的 URL，失败时使用默认 URL
        let base_url = base_url
            .map(|a| a.into())
            .and_then(|s| Url::parse(&s).ok())
            .unwrap_or_else(|| {
                Url::parse(DEFAULT_CZSC_URL).expect("DEFAULT_CZSC_URL must be valid")
            });

        Self {
            base_url,
            token: token.into(),
        }
    }

    pub fn set_base_url<A>(&mut self, base_url: Option<A>)
    where
        A: Into<Cow<'a, str>>,
    {
        if let Some(url) = base_url.map(|a| a.into()).and_then(|s| Url::parse(&s).ok()) {
            self.base_url = url;
        }
    }

    /// 执行API数据查询
    /// 参数:
    /// api_name: 查询接口名称
    /// fields: 查询字段
    /// timeout: int, 查询超时，单位秒
    pub async fn post_request(
        &self,
        path: Option<&str>,
        api_name: &str,
        params: Option<serde_json::Value>,
        fields: Option<&str>,
        timeout: Option<u64>,
    ) -> Result<serde_json::Value, UtilsError> {
        // 默认 fields 为 ""
        let fields = fields.unwrap_or("");
        let params = params.unwrap_or(json!({}));

        let req_params = json!({
            "api_name": api_name,
            "token": self.token,
            "params": params,
            "fields": fields,
        });

        // 发起 http post request
        let client = reqwest::Client::new();

        let mut url = self.base_url.clone();
        if let Some(path) = path {
            url = url.join(path).context("Failed to join path")?;
        }

        #[cfg(debug_assertions)]
        let start = std::time::Instant::now();
        #[cfg(debug_assertions)]
        let since = chrono::Utc::now().timestamp_millis();
        #[cfg(debug_assertions)]
        info!(
            since = since,
            url = %url.as_str(),
            body = %serde_json::to_string(&req_params).unwrap(),
            "posting request"
        );

        let mut req = client.post(url).json(&req_params);
        if let Some(ttl) = timeout {
            req = req.timeout(Duration::from_secs(ttl));
        }

        let resp = req.send().await.context("failed to send HTTP request")?;

        // 处理非成功状态码
        if !resp.status().is_success() {
            let status = resp.status();
            let error_body = resp
                .text()
                .await
                .unwrap_or_else(|_| String::from("unable to read error response"));

            return Err(UtilsError::Unexpected(anyhow::anyhow!(
                "HTTP request failed with status {}: {}",
                status,
                error_body
            )));
        }

        // let resp = resp.text().await.context("Failed to read response body")?;

        // #[cfg(debug_assertions)]
        // let resp: serde_json::Value = serde_json::from_str(&resp)
        //     .with_context(|| format!("Failed to parse JSON. Raw response: {}", resp))?;

        // 非调试模式，简化报错
        // #[cfg(not(debug_assertions))]
        let resp: serde_json::Value = resp.json().await.context("Failed to unmarshel json")?;


        #[cfg(debug_assertions)]
        info!(
            since = since,
            duration = start.elapsed().as_millis(),
            "post request finished"
        );

        Ok(resp)
    }
}

/// 检查给定的JSON值中是否包含"data"字段。
///
/// - 如果"data"字段不存在，则从"msg"字段提取错误消息（如果存在），
///   并返回`AppError::APIData`错误。
/// - 如果"data"字段存在，则表示检查通过。
fn check_data_field(val: &serde_json::Value) -> Result<(), UtilsError> {
    if val.get("data").is_none() {
        let msg: String = val
            .get("msg")
            .and_then(|v| v.as_str())
            .unwrap_or("<EMPTY MSG>")
            .to_string();
        return Err(UtilsError::Unexpected(anyhow::anyhow!(msg)));
    };
    Ok(())
}

/// 将JSON数据转为Polars DataFrame。
///
/// JSON格式：`{"data": {"fields": ["col1", "col2"], "items": [["val1", "val2"], ...]}}`
///
/// # 返回
/// - `Result<DataFrame, UtilsError>`
///
/// # 示例
/// ```rust
/// let json = json!({"data": {"fields": ["name"], "items": [["Alice"]]}});
/// let df = result_to_dataframe(json).unwrap();
/// ```
pub fn result_to_dataframe(data: serde_json::Value) -> Result<DataFrame, UtilsError> {
    check_data_field(&data)?;

    // 获取数据大小以预分配内存
    let fields_count = data
        .get("data")
        .and_then(|data| data.get("fields"))
        .and_then(|fields| fields.as_array())
        .map(|array| array.len())
        .unwrap_or(0);

    let items_count = data
        .get("data")
        .and_then(|data| data.get("items"))
        .and_then(|items| items.as_array())
        .map(|array| array.len())
        .unwrap_or(0);

    // 预分配 fields vector
    let mut fields = Vec::with_capacity(fields_count);
    if let Some(fields_array) = data
        .get("data")
        .and_then(|data| data.get("fields"))
        .and_then(|fields| fields.as_array())
    {
        fields.extend(
            fields_array
                .iter()
                .filter_map(|v| v.as_str().map(String::from)),
        );
    }

    // 预分配二维数组空间
    let mut items = Vec::with_capacity(items_count);
    if let Some(items_array) = data
        .get("data")
        .and_then(|data| data.get("items"))
        .and_then(|items| items.as_array())
    {
        items.extend(items_array.iter().filter_map(|v| {
            v.as_array().map(|inner_array| {
                let mut row = Vec::with_capacity(fields_count);
                row.extend(inner_array.iter().filter_map(|item| {
                    // 类型转换逻辑
                    match item {
                        serde_json::Value::String(s) => Some(s.clone()),
                        serde_json::Value::Number(n) => Some(n.to_string()),
                        serde_json::Value::Bool(b) => Some(b.to_string()), // 添加布尔值处理
                        serde_json::Value::Null => Some(String::new()),    // 添加空值处理
                        _ => None,
                    }
                }));
                row
            })
        }));
    }

    // Series 创建过程
    let mut series_vec = Vec::with_capacity(fields_count);
    for (i, field) in fields.iter().enumerate() {
        let column_values: Vec<String> =
            items.iter().filter_map(|row| row.get(i).cloned()).collect();

        series_vec.push(Series::new(field, column_values));
    }

    // 创建 DataFrame
    let df = DataFrame::new(series_vec).unwrap_or_else(|_| DataFrame::default());
    Ok(df)
}

/// 将JSON Array数据转为Polars DataFrame。
///
/// JSON格式：`{"data": [{}, {}]}`
///
/// # 返回
/// - `Result<DataFrame, UtilsError>`
///
pub fn json_array_to_dataframe(json_val: serde_json::Value) -> Result<DataFrame, UtilsError> {
    let json_array: Vec<serde_json::Value> = json_val
        .get("data")
        .unwrap()
        .clone()
        .as_array()
        .unwrap()
        .clone();
    // 提取所有唯一的键
    let mut keys_set = HashSet::new();
    let keys: Vec<String> = json_array
        .iter()
        .filter_map(|item| item.as_object().map(|obj| obj.keys().cloned()))
        .flat_map(|k| k)
        .filter(|key| keys_set.insert(key.clone())) // 只插入未重复的键
        .collect();
    // 初始化列数据
    let mut data: Vec<Vec<Option<String>>> = vec![vec![]; keys.len()];
    // 填充数据
    for item in json_array {
        if let serde_json::Value::Object(obj) = item {
            for (i, key) in keys.iter().enumerate() {
                let value = match obj.get(key) {
                    Some(v) => {
                        if v.is_string() {
                            v.as_str().map(String::from).map(Some).unwrap_or(None)
                        } else if v.is_number() {
                            v.as_f64().map(|n| n.to_string()).map(Some).unwrap_or(None)
                        } else {
                            None // 处理其他类型，例如布尔值、数组等
                        }
                    }
                    None => None,
                };
                data[i].push(value);
            }
        }
    }
    let series: Vec<Series> = keys
        .into_iter()
        .map(|key| {
            let values: Vec<Option<String>> = data.remove(0);
            Series::new(&key, values)
        })
        .collect();
    Ok(DataFrame::new(series)?)
}

/// 设置默认 CZSC 数据服务 TOKEN
pub fn set_url_token(api_url: &str, token: &str) -> Result<(), UtilsError> {
    let home_dir =
        home::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
    let url_hash = format!("{:x}.txt", md5::compute(api_url));
    let file_path = home_dir.join(url_hash);

    fs::write(file_path, token).context("Failed to write token into file")?;
    Ok(())
}

/// 获取默认 CZSC 数据服务 TOKEN
pub fn get_url_token(api_url: &str) -> Result<String, UtilsError> {
    let home_dir =
        home::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
    let url_hash = format!("{:x}.txt", md5::compute(api_url));
    let file_path = home_dir.join(url_hash);

    if file_path.exists() {
        let token = fs::read_to_string(file_path)
            .map(|content| content.trim().to_string())
            .context("Failed to read file")?;
        return Ok(token);
    } else {
        czsc_bail!("api url token not exist")
    }
}

/// 从 CZSC 数据 API
pub fn new_czsc_dc_from_env() -> Result<DataClient<'static>, UtilsError> {
    let api_url = env::var("CZSC_DATA_API")
        .ok()
        .unwrap_or(DEFAULT_CZSC_URL.to_string());

    let token = match env::var("CZSC_TOKEN").ok() {
        Some(tk) => tk,
        None => get_url_token(&api_url)?,
    };
    Ok(DataClient::new(Some(api_url), token))
}

#[cfg(test)]
mod tests {
    use crate::data_client::result_to_dataframe;
    use polars::{prelude::NamedFrom, series::Series};
    use serde_json::json;

    use super::{DEFAULT_CZSC_URL, get_url_token, new_czsc_dc_from_env};

    #[test]
    fn test_get_token() {
        let token = get_url_token(DEFAULT_CZSC_URL).unwrap();
        println!("{}", token)
    }

    /// 测试数据 API 获取目标权重
    #[tokio::test]
    async fn test_get_target_weight() {
        let dc = new_czsc_dc_from_env().unwrap();
        let params = json!({
            "v": 2
        });
        let df = dc
            .post_request(None, "BTC_1H_P04", Some(params), None, None)
            .await
            .unwrap();
        println!("{:?}", df);
    }

    #[test]
    fn test_result_to_dataframe() {
        // 准备测试数据
        let json_str = r#"{
            "data": {
                "fields": ["name", "age", "score"],
                "items": [
                    ["Alice", "20", "95.5"],
                    ["Bob", "22", "88.0"],
                    ["Charlie", "21", "92.5"]
                ]
            }
        }"#;
        let json_val = serde_json::from_str(&json_str).unwrap();

        // 调用被测试的函数
        let df = result_to_dataframe(json_val).unwrap();

        // 验证 DataFrame 的基本结构
        assert_eq!(df.shape(), (3, 3)); // 3行3列
        assert_eq!(df.get_column_names(), vec!["name", "age", "score"]);

        // 验证具体数据值
        // 验证 name 列
        let name_series = df.column("name").unwrap();
        assert_eq!(
            name_series,
            &Series::new("name", ["Alice", "Bob", "Charlie"])
        );

        // 验证 age 列
        let age_series = df.column("age").unwrap();
        assert_eq!(age_series, &Series::new("name", ["20", "22", "21"]));

        // 验证 score 列
        let score_series = df.column("score").unwrap();
        assert_eq!(score_series, &Series::new("name", ["95.5", "88.0", "92.5"]));

        // 测试空数据情况
        let empty_json = r#"{
            "data": {
                "fields": [],
                "items": []
            }
        }"#;

        let empty_json = serde_json::from_str(&empty_json).unwrap();
        let empty_df = result_to_dataframe(empty_json).unwrap();
        assert_eq!(empty_df.shape(), (0, 0));
    }
}
