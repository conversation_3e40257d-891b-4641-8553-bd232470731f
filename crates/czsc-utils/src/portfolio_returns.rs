//! 计算组合收益
use chrono::{Duration, NaiveDate, NaiveDateTime};
use serde::{Deserialize, Serialize};
use std::collections::{BTreeMap, HashMap};

/// 策略组合记录
///
/// 顶层结构：日期字符串 -> 策略配置
///
/// 例如:
/// ```
/// {"2024-10-08T12:00:00:": {"STK_001": 0.5, "STK_002": 0.2, "STK_003": 0.3}, "2024-11-08T12:00:00": {"STK_003": 0.3, "STK_002": 0.2, "STK_005": 0.5}}
/// ```
///
/// * 要求时间格式为 ​ISO 8601​, 秒级, 无UTC偏移
#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioRecords {
    #[serde(flatten)] // 将外层JSON对象平铺到当前结构
    pub portfolio: HashMap<NaiveDateTime, StrategyWeight>,
}

/// 策略日收益记录
#[derive(Debug, Serialize, Deserialize)]
pub struct DailyReturns {
    /// 日期，例如: "2024-07-22"
    dt: NaiveDate,
    /// 日收益
    returns: f64,
    /// 策略名称，例如: "STK_001"
    #[serde(skip_serializing_if = "String::is_empty")]
    strategy: String,
}

/// 日期点->各策略权重变化记录
struct PortfolioRecordsVec(Vec<(NaiveDate, StrategyWeight)>);

/// 策略配置类型：策略名称 -> 权重
pub type StrategyWeight = HashMap<String, f64>;

impl PortfolioRecords {
    /// 将时间点权重数据转换为按日期聚合的最终权重记录
    ///
    /// 处理策略调仓记录，为每个日期保留最后一次出现的权重配置，并按日期升序排列结果。
    ///
    /// # 输入
    /// - 消费`PortfolioRecords`实例，包含时间点→权重的映射关系
    ///
    /// # 输出
    /// - 返回`Vec<(NaiveDate, StrategyWeight)>`:
    ///   - 按日期升序排列
    ///   - 每个日期仅保留当天最后一个时间点的权重配置
    ///
    fn collect_daily_weights(self) -> PortfolioRecordsVec {
        // 处理空数据的情况
        if self.portfolio.is_empty() {
            return PortfolioRecordsVec(Vec::new());
        }

        // 将键值对转换为向量并排序
        let mut entries: Vec<_> = self.portfolio.into_iter().collect();
        entries.sort_unstable_by(|(a, _), (b, _)| a.cmp(b));

        let mut daily_weights = Vec::with_capacity(entries.len());
        for (datetime, weight) in entries {
            let current_date = datetime.date();

            // 检查并替换同一天的旧记录
            if let Some((last_date, _)) = daily_weights.last() {
                if *last_date == current_date {
                    daily_weights.pop();
                }
            }

            daily_weights.push((current_date, weight));
        }

        PortfolioRecordsVec(daily_weights)
    }
}

impl PortfolioRecordsVec {
    /// 将原始权重记录转换为以次生效的权重记录
    /// 示例：输入原始记录 2024-10-08 → 输出生效日期 2024-10-09
    fn adjust_to_next_day_effective(mut self) -> Self {
        let adjusted_weights = self
            .0
            .drain(..)
            .map(|(dt, weight)| {
                let effective_dt = dt
                    .checked_add_signed(Duration::days(1))
                    .unwrap_or_else(|| panic!("权重日期 {} 加1天无效", dt));
                (effective_dt, weight)
            })
            .collect();
        Self(adjusted_weights)
    }

    /// 计算投资组合的每日收益
    ///
    /// 参数：
    ///
    /// - daily_returns: 升序排列的每日各策略收益数据（日期，策略名，收益）
    ///
    /// 返回：
    ///
    /// - PortfolioReturns: 升序排列的每日组合收益（日期，总收益）
    fn calculate_daily_portfolio_returns(
        self,
        daily_returns: Vec<DailyReturns>,
    ) -> Vec<DailyReturns> {
        // 按日期分组策略收益：BTreeMap自动按日期升序排列
        let mut returns_map = BTreeMap::new();
        for DailyReturns {
            dt,
            returns,
            strategy,
        } in daily_returns
        {
            returns_map
                .entry(dt)
                .or_insert(HashMap::new())
                .insert(strategy, returns);
        }

        let weights = self.0;
        let mut returns = Vec::with_capacity(returns_map.len());

        // 遍历每个有收益记录的日期
        for (dt, strat_returns) in returns_map {
            let weight_idx = weights.binary_search_by(|(weight_dt, _)| weight_dt.cmp(&dt));

            let total_ret = match weight_idx {
                Ok(i) => compute_weighted_return(dt, &weights[i].1, &strat_returns),
                Err(i) if i > 0 => compute_weighted_return(dt, &weights[i - 1].1, &strat_returns),
                _ => 0.0,
            };

            returns.push(DailyReturns {
                dt,
                returns: total_ret,
                strategy: String::new(),
            });
        }

        returns
    }
}

#[cfg(debug_assertions)]
fn compute_weighted_return(
    dt: NaiveDate,
    weights: &HashMap<String, f64>,
    returns: &HashMap<String, f64>,
) -> f64 {
    use tracing::debug;

    let mut total = 0.0;
    let mut parts = Vec::new();
    for (strategy, ret) in returns {
        let weight = weights.get(strategy).unwrap_or(&0.0);
        let product = weight * ret;
        total += product;
        parts.push(format!("{}({:.4})", strategy, product));
    }
    let equation = parts.join(" + ");
    debug!("{} {} = {:.4}", dt, equation, total);
    total
}

#[cfg(not(debug_assertions))]
fn compute_weighted_return(
    _dt: NaiveDate,
    weights: &HashMap<String, f64>,
    returns: &HashMap<String, f64>,
) -> f64 {
    returns
        .iter()
        .map(|(strategy, ret)| weights.get(strategy).unwrap_or(&0.0) * ret)
        .sum()
}

/// 计算投资组合的每日收益
///
/// 参数：
///
/// - recors: 策略组合记录, 顶层结构：日期字符串 -> 策略配置, 例如:
///
/// 例如:
/// ```
/// {"2024-10-08T12:00:00:": {"STK_001": 0.5, "STK_002": 0.2, "STK_003": 0.3}, "2024-11-08T12:00:00": {"STK_003": 0.3, "STK_002": 0.2, "STK_005": 0.5}}
/// ```
///
/// * 要求时间格式为 ​ISO 8601​, 秒级, 无UTC偏移
///
/// - daily_returns: 升序排列的每日各策略收益数据（日期，策略名，收益）
///
/// 返回：
///
/// - PortfolioReturns: 升序排列的每日组合收益（日期，总收益）
pub fn calculate_portfolio_returns(
    records: PortfolioRecords,
    daily_returns: Vec<DailyReturns>,
) -> Vec<DailyReturns> {
    records
        .collect_daily_weights()
        .adjust_to_next_day_effective()
        .calculate_daily_portfolio_returns(daily_returns)
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json;

    fn example_portofolio_json_str() -> &'static str {
        r#"
{
    "2024-10-08T12:00:00": {
        "STK_001": 0.5,
        "STK_003": 0.3,
        "STK_002": 0.2
    },
    "2024-11-08T12:00:00": {
        "STK_005": 0.5,
        "STK_003": 0.3,
        "STK_002": 0.2
    }
}
"#
    }

    #[test]
    fn test_parse_records() {
        let p: PortfolioRecords = serde_json::from_str(example_portofolio_json_str()).unwrap();
        println!("{:?}", p.portfolio)
    }

    #[test]
    fn test_collect_daily_weights() {
        let records: PortfolioRecords =
            serde_json::from_str(example_portofolio_json_str()).unwrap();

        let v = records.collect_daily_weights();
        println!("{:?}", v.0);
    }

    #[test]
    fn test_adjust_to_next_day_effective() {
        let v: PortfolioRecords = serde_json::from_str(example_portofolio_json_str()).unwrap();

        let v = v.collect_daily_weights();
        let v = v.adjust_to_next_day_effective();
        println!("{:?}", v.0);
    }

    fn example_daily_returns() -> &'static str {
        r#"
[
  {
    "dt": "2024-09-30",
    "returns": 0.0667,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-08",
    "returns": 0.0716,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-09",
    "returns": -0.1005,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-10",
    "returns": -0.0234,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-11",
    "returns": -0.0342,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-14",
    "returns": -0.0015,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-15",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-16",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-17",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-18",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-21",
    "returns": 0.0125,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-22",
    "returns": 0.0173,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-23",
    "returns": -0.0102,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-24",
    "returns": 0.0034,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-25",
    "returns": 0.0103,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-28",
    "returns": 0.0461,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-29",
    "returns": -0.0468,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-30",
    "returns": 0.0303,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-10-31",
    "returns": 0.0294,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-01",
    "returns": -0.0252,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-04",
    "returns": -0.0035,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-05",
    "returns": 0.0441,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-06",
    "returns": 0.0241,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-07",
    "returns": 0.0511,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-08",
    "returns": -0.0386,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-11",
    "returns": 0.0185,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-12",
    "returns": -0.0148,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-13",
    "returns": -0.0102,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-14",
    "returns": -0.0236,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-15",
    "returns": -0.0015,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-18",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-19",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-20",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-21",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-22",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-25",
    "returns": 0.0296,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-26",
    "returns": -0.0125,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-27",
    "returns": 0.0,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-28",
    "returns": 0.0103,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-11-29",
    "returns": -0.0051,
    "strategy": "STK_001"
  },
  {
    "dt": "2024-09-30",
    "returns": 0.0692,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-08",
    "returns": 0.0464,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-09",
    "returns": -0.096,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-10",
    "returns": -0.0035,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-11",
    "returns": -0.02,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-14",
    "returns": -0.0015,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-15",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-16",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-17",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-18",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-21",
    "returns": 0.0013,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-22",
    "returns": 0.0278,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-23",
    "returns": 0.0011,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-24",
    "returns": 0.0345,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-25",
    "returns": 0.0208,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-28",
    "returns": -0.0015,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-29",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-30",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-10-31",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-01",
    "returns": 0.0,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-04",
    "returns": 0.024,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-05",
    "returns": 0.0128,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-06",
    "returns": 0.0029,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-07",
    "returns": 0.0407,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-08",
    "returns": -0.0233,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-11",
    "returns": 0.008,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-12",
    "returns": -0.0067,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-13",
    "returns": -0.013,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-14",
    "returns": -0.0386,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-15",
    "returns": -0.0196,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-18",
    "returns": -0.011,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-19",
    "returns": 0.0083,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-20",
    "returns": 0.0228,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-21",
    "returns": 0.0295,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-22",
    "returns": -0.0299,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-25",
    "returns": 0.0253,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-26",
    "returns": -0.012,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-27",
    "returns": 0.0028,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-28",
    "returns": 0.0152,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-11-29",
    "returns": 0.0036,
    "strategy": "STK_002"
  },
  {
    "dt": "2024-09-30",
    "returns": 0.0791,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-08",
    "returns": 0.0325,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-09",
    "returns": -0.0676,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-10",
    "returns": 0.0172,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-11",
    "returns": -0.0163,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-14",
    "returns": -0.0015,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-15",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-16",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-17",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-18",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-21",
    "returns": 0.0045,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-22",
    "returns": 0.0027,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-23",
    "returns": -0.0038,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-24",
    "returns": 0.0055,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-25",
    "returns": 0.0309,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-28",
    "returns": -0.0015,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-29",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-30",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-10-31",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-01",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-04",
    "returns": 0.0278,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-05",
    "returns": 0.0088,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-06",
    "returns": 0.0,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-07",
    "returns": 0.0224,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-08",
    "returns": -0.0038,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-11",
    "returns": 0.0074,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-12",
    "returns": -0.0056,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-13",
    "returns": 0.0085,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-14",
    "returns": -0.0117,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-15",
    "returns": 0.009,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-18",
    "returns": -0.0347,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-19",
    "returns": 0.0266,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-20",
    "returns": 0.011,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-21",
    "returns": 0.0035,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-22",
    "returns": -0.0538,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-25",
    "returns": 0.0356,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-26",
    "returns": 0.0158,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-27",
    "returns": 0.0045,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-28",
    "returns": 0.0999,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-11-29",
    "returns": -0.025,
    "strategy": "STK_003"
  },
  {
    "dt": "2024-09-30",
    "returns": 0.083,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-08",
    "returns": 0.0403,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-09",
    "returns": -0.0629,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-10",
    "returns": -0.017,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-11",
    "returns": -0.0443,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-14",
    "returns": -0.0015,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-15",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-16",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-17",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-18",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-21",
    "returns": 0.0017,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-22",
    "returns": -0.0119,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-23",
    "returns": 0.099,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-24",
    "returns": 0.0564,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-25",
    "returns": -0.0194,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-28",
    "returns": -0.0015,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-29",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-30",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-10-31",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-01",
    "returns": 0.0,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-04",
    "returns": 0.0263,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-05",
    "returns": 0.0265,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-06",
    "returns": 0.0036,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-07",
    "returns": 0.0223,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-08",
    "returns": 0.0079,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-11",
    "returns": 0.0172,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-12",
    "returns": -0.0075,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-13",
    "returns": -0.0051,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-14",
    "returns": -0.0254,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-15",
    "returns": -0.0101,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-18",
    "returns": -0.025,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-19",
    "returns": 0.0222,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-20",
    "returns": 0.0189,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-21",
    "returns": 0.0134,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-22",
    "returns": -0.045,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-25",
    "returns": 0.0289,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-26",
    "returns": -0.0032,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-27",
    "returns": 0.0132,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-28",
    "returns": -0.0052,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-11-29",
    "returns": 0.0117,
    "strategy": "STK_004"
  },
  {
    "dt": "2024-09-30",
    "returns": -0.0015,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-08",
    "returns": 0.0,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-09",
    "returns": 0.0,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-10",
    "returns": 0.0,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-11",
    "returns": 0.0,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-14",
    "returns": -0.0015,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-15",
    "returns": -0.0114,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-16",
    "returns": -0.0159,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-17",
    "returns": -0.0206,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-18",
    "returns": 0.009,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-21",
    "returns": 0.0967,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-22",
    "returns": 0.099,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-23",
    "returns": 0.0038,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-24",
    "returns": 0.0385,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-25",
    "returns": 0.0992,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-28",
    "returns": 0.0984,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-29",
    "returns": 0.0735,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-30",
    "returns": 0.1009,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-10-31",
    "returns": 0.0992,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-01",
    "returns": -0.076,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-04",
    "returns": 0.02,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-05",
    "returns": 0.0101,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-06",
    "returns": 0.0122,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-07",
    "returns": 0.056,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-08",
    "returns": -0.0187,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-11",
    "returns": -0.0042,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-12",
    "returns": -0.0075,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-13",
    "returns": 0.0,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-14",
    "returns": -0.0236,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-15",
    "returns": -0.0143,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-18",
    "returns": -0.0123,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-19",
    "returns": 0.0226,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-20",
    "returns": 0.0287,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-21",
    "returns": 0.0054,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-22",
    "returns": -0.0309,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-25",
    "returns": 0.0264,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-26",
    "returns": 0.0118,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-27",
    "returns": 0.0,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-28",
    "returns": 0.0212,
    "strategy": "STK_005"
  },
  {
    "dt": "2024-11-29",
    "returns": 0.0374,
    "strategy": "STK_005"
  }
]
"#
    }

    #[test]
    fn test_calculate_portfolio_returns() {
        let v: PortfolioRecords = serde_json::from_str(example_portofolio_json_str()).unwrap();

        let daily_returns: Vec<DailyReturns> =
            serde_json::from_str(example_daily_returns()).unwrap();
        let res = calculate_portfolio_returns(v, daily_returns);
        println!("{}", serde_json::to_string_pretty(&res).unwrap());
        // for (date, value) in res.returns {
        //     // 将日期格式化为YYYY-MM-DD，数值保留五位小数
        //     println!("{}    {:>10.5}", date, value);
        // }
    }
}
