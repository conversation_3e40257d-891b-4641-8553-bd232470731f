use chrono::{
    DateTime, Datelike, Duration, Local, NaiveDate, NaiveDateTime, NaiveTime, TimeZone, Timelike,
    Utc,
};
use czsc_core::objects::{freq::Freq, market::Market};
use error_support::czsc_bail;
use hashbrown::HashMap;
use once_cell::sync::Lazy;
use polars::{frame::DataFrame, io::SerReader, prelude::IpcReader};
use std::{io::Cursor, str::FromStr};

use crate::errors::UtilsError;

static MINUTES_SPLIT_DF: Lazy<DataFrame> = Lazy::new(|| {
    // 将文件包含在二进制文件中
    const MINUTES_SPLIT_BYTES: &[u8] = include_bytes!("../data/minutes_split.feather");
    let cursor = Cursor::new(MINUTES_SPLIT_BYTES);
    IpcReader::new(cursor)
        .finish()
        .expect("failed to read minutes_split.feather")
});

static FREQ_EDT_MAP: Lazy<HashMap<(Market, Freq), HashMap<NaiveTime, NaiveTime>>> =
    Lazy::new(|| {
        let mut result: HashMap<(Market, Freq), HashMap<NaiveTime, NaiveTime>> = HashMap::new();

        let format = "%H:%M";

        // 按照market分组
        let groups = MINUTES_SPLIT_DF
            .partition_by(["market"], true)
            .expect("failed tp groupby markets");

        for g in groups {
            let market_type = g
                .column("market")
                .expect("failed to get market col")
                .str()
                .expect("failed to convert market col into str")
                .get(0)
                .expect("failed to get the first row for market col");
            let market_type = Market::from_str(market_type).expect("unregistered market type");

            // 遍历所有包含 "分钟" 的列名
            for minute in MINUTES_SPLIT_DF
                .get_column_names()
                .iter()
                .filter(|&col| col.contains("分钟"))
            {
                let time_col = g
                    .column("time")
                    .expect("failed to get time col")
                    .str()
                    .expect("failed to convert time col into str");
                let freq_of_time_col = g
                    .column(minute)
                    .expect("failed to get minute col")
                    .str()
                    .expect("failed to convert minute col into str");

                let mut time_map = HashMap::new();

                // 使用下标遍历确保不会遗漏数据
                for idx in 0..g.height() {
                    let time = time_col.get(idx).expect("failed to get idx of time col");
                    let freq_of_time = freq_of_time_col
                        .get(idx)
                        .expect("failed to get idx of minute col");

                    let time =
                        NaiveTime::parse_from_str(time, format).expect("failed to parse time str");
                    let freq_of_time = NaiveTime::parse_from_str(freq_of_time, format)
                        .expect("failed to parse time str");
                    time_map.insert(time, freq_of_time);
                }

                let minute_freq = Freq::from_str(&minute).expect("unregistered freq");

                result.insert((market_type, minute_freq), time_map);
            }
        }
        result
    });

// #[allow(unused)]
// static FREQ_MARKET_TIMES: Lazy<HashMap<String, Vec<String>>> = Lazy::new(|| {
//     let mut result: HashMap<String, Vec<String>> = HashMap::new();

//     // 按照market分组
//     let groups = MINUTES_SPLIT_DF
//         .partition_by(["market"], true)
//         .expect("failed tp groupby markets");

//     for g in groups {
//         let market_type = g
//             .column("market")
//             .expect("failed to get market col")
//             .str()
//             .expect("failed to convert market col into str")
//             .get(0)
//             .expect("failed to get the first row for market col");

//         // 遍历所有包含 "分钟" 的列名
//         for minute in MINUTES_SPLIT_DF
//             .get_column_names()
//             .iter()
//             .filter(|&col| col.contains("分钟"))
//         {
//             let mut v: Vec<_> = MINUTES_SPLIT_DF
//                 .column(&minute)
//                 .expect("failed to get minute col")
//                 .str()
//                 .expect("failed to convert minute col into str")
//                 .into_iter()
//                 .flatten()
//                 .map(String::from)
//                 .collect();
//             // 去除连续的重复元素
//             v.dedup();

//             let key = format!("{}_{}", minute, market_type);
//             result.insert(key, v);
//         }
//     }

//     result
// });

/// 计算目标周期的结束时间(仅日期)
fn freq_end_date(dt: NaiveDate, freq: Freq) -> Result<NaiveDate, UtilsError> {
    match freq {
        Freq::D => Ok(dt),
        Freq::W => {
            // ISO weekday: 星期一是1, 星期日是7
            let weekday = dt.weekday().number_from_monday();
            // 计算到周五的天数
            let days_to_add = if weekday <= 5 {
                5 - weekday
            } else {
                5 - weekday + 7
            };
            Ok(dt + Duration::days(days_to_add as i64))
        }
        Freq::Y => {
            // 设置为12月31日
            NaiveDate::from_ymd_opt(dt.year(), 12, 31).ok_or(UtilsError::InvalidDateTime)
        }
        Freq::M => {
            let year = dt.year();
            let month = dt.month();
            // 计算下个月的第一天，然后减去一天
            let (next_year, next_month) = if month == 12 {
                (year + 1, 1)
            } else {
                (year, month + 1)
            };

            // 获取下个月第一天
            let date = NaiveDate::from_ymd_opt(next_year, next_month, 1)
                .ok_or(UtilsError::InvalidDateTime)?;
            let time = NaiveTime::from_hms_opt(0, 0, 0).ok_or(UtilsError::InvalidDateTime)?;
            let dt = NaiveDateTime::new(date, time);

            // 减去一天得到当月最后一天
            Ok((dt - Duration::days(1)).into())
        }
        Freq::S => {
            let year = dt.year();
            let month = dt.month();

            // 确定季度结束月份
            let (end_year, end_month) = match month {
                1..=3 => (year, 4),       // Q1 ends in March
                4..=6 => (year, 7),       // Q2 ends in June
                7..=9 => (year, 10),      // Q3 ends in September
                10..=12 => (year + 1, 1), // Q4 ends in December
                _ => unreachable!(),
            };

            // 获取下个季度第一天
            let date = NaiveDate::from_ymd_opt(end_year, end_month, 1)
                .ok_or(UtilsError::InvalidDateTime)?;
            let time = NaiveTime::from_hms_opt(0, 0, 0).ok_or(UtilsError::InvalidDateTime)?;
            let dt = NaiveDateTime::new(date, time);

            // 减去一天得到当前季度最后一天
            Ok((dt - Duration::days(1)).into())
        }
        // 对于其他周期，直接返回输入日期
        _ => Ok(dt),
    }
}

/// 计算目标周期的结束时间
pub fn freq_end_time(
    dt: DateTime<Utc>,
    freq: Freq,
    market: Market,
) -> Result<DateTime<Utc>, UtilsError> {
    // 如果秒>0 找下1分钟
    let dt = if dt.second() > 0 {
        dt + Duration::minutes(1)
    } else {
        dt
    };

    // 将 Utc 时间转成 本地 时区的时间
    let dt = Local.from_utc_datetime(&dt.naive_utc());

    // 获取本地时分
    let local_time = dt.time();

    // 如果是分钟周期
    if freq.is_minute_freq() {
        if let Some(time_map) = FREQ_EDT_MAP.get(&(market, freq)) {
            if let Some(end_time) = time_map.get(&local_time) {
                // 使用本地时区的时间来计算出新的结束时间
                let edt = dt
                    .with_hour(end_time.hour())
                    .ok_or(UtilsError::InvalidDateTime)?
                    .with_minute(end_time.minute())
                    .ok_or(UtilsError::InvalidDateTime)?;

                if end_time.num_seconds_from_midnight() == 0
                    && freq == Freq::F1
                    && local_time != *end_time
                {
                    // 特殊情况处理：如果结束时间是 00:00
                    return Ok((edt + Duration::days(1)).with_timezone(&Utc));
                }
                // 使用 with_timezone 将本地时间还原成Utc时间
                return Ok(edt.with_timezone(&Utc));
            }
        }

        czsc_bail!("无法找到对应的结束时间")
    }

    // 对于非分钟级别的周期
    // 计算出新的结束日期
    let local_date = freq_end_date(dt.date_naive(), freq)?;
    let edt = dt
        .with_year(local_date.year())
        .ok_or(UtilsError::InvalidDateTime)?
        .with_month(local_date.month())
        .ok_or(UtilsError::InvalidDateTime)?
        .with_day(local_date.day())
        .ok_or(UtilsError::InvalidDateTime)?;
    return Ok(edt.with_timezone(&Utc));
}

#[cfg(test)]
mod tests {
    use super::*;

    // #[test]
    // fn test_datetime() {
    //     use chrono::{Local, TimeZone};
    //     // 2024/12/12 1:31
    //     let naive_datetime = NaiveDateTime::new(
    //         chrono::NaiveDate::from_ymd_opt(2024, 12, 12).unwrap(), // 年月日
    //         chrono::NaiveTime::from_hms_opt(1, 31, 0).unwrap(),     // 时分秒
    //     );

    //     // 2024/12/12 9:31 GMT+08:00
    //     let local_datetime = Local.from_utc_datetime(&naive_datetime);

    //     assert_eq!(
    //         local_datetime.naive_utc().to_string().as_str(),
    //         "2024-12-12 01:31:00"
    //     );
    //     assert_eq!(
    //         local_datetime.naive_local().to_string().as_str(),
    //         "2024-12-12 09:31:00"
    //     );
    // }

    trait TestDateTime {
        /// 将DateTime转成本地时区的字符串表示(不带时区)
        fn into_local_dt_str(&self) -> String;
    }

    impl TestDateTime for DateTime<Utc> {
        fn into_local_dt_str(&self) -> String {
            let s = self
                .with_timezone(&Local)
                .format("%Y-%m-%d %H:%M:%S")
                .to_string();
            s
        }
    }

    #[test]
    fn test_freq_minute() {
        // 当地时区的时间
        let local_dt = Local
            .from_local_datetime(
                &NaiveDateTime::parse_from_str("2024-12-12 10:01:00", "%Y-%m-%d %H:%M:%S").unwrap(),
            )
            .unwrap();

        let dt = local_dt.with_timezone(&Utc);

        // 1分钟
        assert_eq!(
            freq_end_time(dt, Freq::F1, Market::AShare)
                .unwrap()
                .into_local_dt_str(),
            "2024-12-12 10:01:00"
        );

        // 5分钟
        assert_eq!(
            freq_end_time(dt, Freq::F5, Market::AShare)
                .unwrap()
                .into_local_dt_str(),
            "2024-12-12 10:05:00"
        );

        // 30分钟
        assert_eq!(
            freq_end_time(dt, Freq::F30, Market::AShare)
                .unwrap()
                .into_local_dt_str(),
            "2024-12-12 10:30:00"
        );

        // 60分钟
        assert_eq!(
            freq_end_time(dt, Freq::F60, Market::AShare)
                .unwrap()
                .into_local_dt_str(),
            "2024-12-12 10:30:00"
        );
    }

    /// 非分钟K线的测试(年线)
    #[test]
    fn test_freq_year() {
        let local_dt = Local
            .from_local_datetime(
                &NaiveDateTime::parse_from_str("2024-12-12 10:01:00", "%Y-%m-%d %H:%M:%S").unwrap(),
            )
            .unwrap();
        let dt = local_dt.with_timezone(&Utc);

        let res = freq_end_time(dt, Freq::Y, Market::AShare)
            .unwrap()
            .into_local_dt_str();

        assert_eq!(res, "2024-12-31 10:01:00");
    }
}
