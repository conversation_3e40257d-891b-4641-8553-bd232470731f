use crate::{
    errors::UtilsError,
    top_drawdowns::{
        calc_underwater, calc_underwater_peak, calc_underwater_recovery, calc_underwater_valley,
    },
};
use czsc_core::utils::rounded::{RoundToNthDigit, min_max};
use serde::Serialize;
use std::f64::EPSILON;

/// 单利计算日收益数据的各项指标
#[derive(Debug, PartialEq, Serialize)]
pub struct DailyPerformance {
    /// 绝对收益
    pub absolute_return: f64,
    /// 年化
    pub annual_returns: f64,
    /// 夏普
    pub sharpe_ratio: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 卡玛
    pub calmar_ratio: f64,
    /// 日胜率
    pub daily_win_rate: f64,
    /// 日盈亏比
    pub daily_profit_loss_ratio: f64,
    /// 日赢面
    pub daily_win_probability: f64,
    /// 年化波动率
    pub annual_volatility: f64,
    /// 下行波动率
    pub downside_volatility: f64,
    /// 非零覆盖
    pub non_zero_coverage: f64,
    /// 盈亏平衡点
    pub break_even_point: f64,
    /// 新高间隔
    pub new_high_interval: f64,
    /// 新高占比
    pub new_high_ratio: f64,
    /// 回撤风险
    pub drawdown_risk: f64,
    // 回归年度回报率
    pub annual_lin_reg_cumsum_return: Option<f64>,
    pub length_adjusted_average_max_drawdown: f64,
}

impl Default for DailyPerformance {
    fn default() -> DailyPerformance {
        DailyPerformance {
            absolute_return: 0.0,
            annual_returns: 0.0,
            sharpe_ratio: 0.0,
            max_drawdown: 0.0,
            calmar_ratio: 0.0,
            daily_win_rate: 0.0,
            daily_profit_loss_ratio: 0.0,
            daily_win_probability: 0.0,
            annual_volatility: 0.0,
            downside_volatility: 0.0,
            non_zero_coverage: 0.0,
            break_even_point: 0.0,
            new_high_interval: 0.0,
            new_high_ratio: 0.0,
            drawdown_risk: 0.0,
            annual_lin_reg_cumsum_return: None,
            length_adjusted_average_max_drawdown: 0.0,
        }
    }
}

/// 采用单利计算日收益数据的各项指标
///
/// 函数计算逻辑：
///
/// 1. 首先，将传入的日收益率数据转换为NumPy数组，并给定数据类型为float64。
/// 2. 然后，进行一系列判断：如果日收益率数据为空或标准差为零或全部为零，则返回字典，其中所有指标的值都为零。
/// 3. 如果日收益率数据满足要求，则进行具体的指标计算：
///
///     - 年化收益率 = 日收益率列表的和 / 日收益率列表的长度 * 252
///     - 夏普比率 = 日收益率的均值 / 日收益率的标准差 * 标准差的根号252
///     - 最大回撤 = 累计日收益率的最高累积值 - 累计日收益率
///     - 卡玛比率 = 年化收益率 / 最大回撤（如果最大回撤不为零，则除以最大回撤；否则为10）
///     - 日胜率 = 大于零的日收益率的个数 / 日收益率的总个数
///     - 年化波动率 = 日收益率的标准差 * 标准差的根号252
///     - 下行波动率 = 日收益率中小于零的日收益率的标准差 * 标准差的根号252
///     - 非零覆盖 = 非零的日收益率个数 / 日收益率的总个数
///     - 回撤风险 = 最大回撤 / 年化波动率；一般认为 1 以下为低风险，1-2 为中风险，2 以上为高风险
///     - 回归年度回报率
///     - 长度调整平均最大回撤
///
/// 4. 将所有指标的值存储在字典中，其中键为指标名称，值为相应的计算结果。
///
/// :param daily_returns: 日收益率数据，样例：
///     [0.01, 0.02, -0.01, 0.03, 0.02, -0.02, 0.01, -0.01, 0.02, 0.01]
/// :param yearly_days: 一年的交易日数，默认为 252
///
/// :return: DailyPerformance
pub fn daily_performance(
    daily_returns: &[f64],
    yearly_days: Option<usize>,
) -> Result<DailyPerformance, UtilsError> {
    if daily_returns.is_empty() {
        return Ok(DailyPerformance::default());
    }

    let total_days = daily_returns.len() as f64;
    let yearly_days = yearly_days.unwrap_or(252) as f64;

    // 初始化变量以进行单遍计算
    // 累计收益
    let mut cum_return = 0.0;
    let mut mean = 0.0;
    // 当前均值差的平方和，用于计算标准差
    let mut m2 = 0.0;
    // 记录到当前为止的最大累积收益
    let mut max_cum_return: f64 = 0.0;
    let mut zero_drawdown_count = 0;
    // 记录当前连续回撤的持续时间
    let mut current_interval = 0;
    // 计算最大回撤持续时间
    let mut new_high_interval: i32 = 0;
    // 盈利天数
    let mut win_count = 0;
    // 累计亏损
    let mut cum_win = 0.0;
    // 累计盈利
    let mut cum_loss = 0.0;
    let mut neg_count = 0.0;
    let mut neg_mean = 0.0;
    let mut neg_m2 = 0.0;
    let mut zero_count = 0;

    // 线性回归相关的变量
    // Σ(x*y)
    let mut lr_sum_xy = 0.0;
    // Σ(Σ(daily_return))
    let mut lr_sum_cum_return = 0.0;

    for (i, &daily_return) in daily_returns.iter().enumerate() {
        let delta = daily_return - mean;
        mean += delta / (i as f64 + 1.0);
        let delta2 = daily_return - mean;
        m2 += delta * delta2;

        cum_return += daily_return;

        lr_sum_cum_return += cum_return;
        lr_sum_xy += (i as f64) * cum_return;

        if cum_return > max_cum_return {
            max_cum_return = cum_return;
            // 达到新高
            // 刷新新高占比
            new_high_interval = new_high_interval.max(current_interval);
            current_interval = 0;
        }
        current_interval += 1;

        // 计算当前的回撤值判断是否处于高水位
        if max_cum_return - cum_return <= 0.0 {
            zero_drawdown_count += 1;
        }

        // 计算盈利天数和亏损天数
        match daily_return {
            d if d > 0.0 => {
                win_count += 1;
                cum_win += d;
            }
            d if d < 0.0 => {
                cum_loss += daily_return;

                neg_count += 1.0;
                let neg_delta = d - neg_mean;
                neg_mean += neg_delta / neg_count;
                let neg_delta2 = d - neg_mean;
                neg_m2 += neg_delta * neg_delta2;
            }
            _ => {
                // 0 收益也是赢
                win_count += 1;
                zero_count += 1;
            }
        };
    }

    if cum_return.abs() < EPSILON {
        return Ok(DailyPerformance::default());
    }

    // 线性回归相关的计算
    // x总和
    let lr_sum_x = (total_days - 1.0) * total_days / 2.0;
    let lr_sum_x_squared = (total_days - 1.0) * total_days * (2.0 * total_days - 1.0) / 6.0;
    // 斜率分母
    let lr_denominator = total_days * lr_sum_x_squared - lr_sum_x * lr_sum_x;
    // 日收益线性回归斜率
    let annual_lr_cumsum_slope = if lr_denominator.abs() > EPSILON {
        // 分母不为0才有意义
        let slope =
            (1.0 / lr_denominator) * (total_days * lr_sum_xy - lr_sum_x * lr_sum_cum_return);
        // 乘年交易日数
        Some((slope * yearly_days).round_to_4_digit())
    } else {
        None
    };

    // 回撤
    let (max_drawdown, length_adjusted_average_max_drawdown) =
        daily_performance_drawdown(5, daily_returns, yearly_days);

    // 计算标准差
    let variance = m2 / total_days;
    let std_val = variance.sqrt();
    if std_val < EPSILON {
        return Ok(DailyPerformance::default());
    }

    let sharpe_ratio = mean / std_val * yearly_days.sqrt();
    let new_high_ratio = (zero_drawdown_count as f64 / total_days).round_to_4_digit();
    let annual_returns = (mean * yearly_days).round_to_4_digit();
    let calmar_ratio = if max_drawdown < EPSILON {
        10.0
    } else {
        annual_returns / max_drawdown
    }
    .round_to_4_digit();
    // 日胜率
    let daily_win_rate = (win_count as f64 / total_days).round_to_4_digit();
    // NOTE: 没有盈利也算亏损
    let loss_count = total_days as usize - win_count;
    // 计算平均亏损
    let daily_mean_loss = if loss_count > 0 {
        cum_loss / loss_count as f64
    } else {
        0.0
    };
    // 计算平均盈利
    let daily_mean_win = if win_count > 0 {
        cum_win / win_count as f64
    } else {
        0.0
    };
    // 日盈亏比
    let daily_profit_loss_ratio = if daily_mean_loss.abs() > EPSILON {
        daily_mean_win / daily_mean_loss.abs()
    } else {
        5.0
    }
    .round_to_4_digit();
    // 日赢面: 日盈亏比*日胜率(盈利交易对整体盈利的贡献) - (1−日胜率)(表示亏损交易对整体盈利的拖累)
    let daily_win_probability =
        (daily_profit_loss_ratio * daily_win_rate - (1.0 - daily_win_rate)).round_to_4_digit();

    let annual_volatility = (std_val * yearly_days.sqrt()).round_to_4_digit();

    let drawdown_risk = (max_drawdown / annual_volatility).round_to_4_digit();

    let downside_volatility = if neg_count > 0.0 {
        let neg_variance = neg_m2 / neg_count;
        let neg_std_dev = neg_variance.sqrt();
        neg_std_dev * yearly_days.sqrt()
    } else {
        0.0
    }
    .round_to_4_digit();

    let absolute_return = cum_return.round_to_4_digit();

    let non_zero_coverage = ((total_days - zero_count as f64) / total_days).round_to_4_digit();

    let sharpe_ratio = (min_max(sharpe_ratio, -5.0, 10.0)).round_to_4_digit();
    let calmar_ratio = (min_max(calmar_ratio, -10.0, 20.0)).round_to_4_digit();

    // 计算盈亏平衡点
    let mut sorted_daily_returns = daily_returns.to_vec();
    sorted_daily_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());

    let break_even_index = sorted_daily_returns
        .iter()
        .scan(0.0, |sum, &daily_return| {
            *sum += daily_return;
            Some(*sum)
        })
        .position(|cum_sum| cum_sum > 0.0);

    let break_even_point =
        ((break_even_index.unwrap_or(0) + 1) as f64 / total_days).round_to_4_digit();

    Ok(DailyPerformance {
        absolute_return,
        annual_returns,
        sharpe_ratio,
        max_drawdown: max_drawdown.round_to_4_digit(),
        calmar_ratio,
        daily_win_rate,
        daily_profit_loss_ratio,
        daily_win_probability,
        annual_volatility,
        downside_volatility,
        non_zero_coverage,
        break_even_point,
        new_high_interval: new_high_interval as f64,
        new_high_ratio,
        drawdown_risk,
        annual_lin_reg_cumsum_return: annual_lr_cumsum_slope,
        length_adjusted_average_max_drawdown: length_adjusted_average_max_drawdown
            .round_to_4_digit(),
    })
}

pub(crate) fn daily_performance_drawdown(
    top_n_drawdown: usize,
    daily_returns: &[f64],
    yearly_days: f64,
) -> (f64, f64) {
    let total_days = daily_returns.len();
    let mut underwater = calc_underwater(daily_returns);
    let mut top_n_drawdown_days_sum = 0;
    let mut max_drawdown = 0.0;
    for _ in 0..top_n_drawdown {
        let valley = calc_underwater_valley(&underwater);

        if valley.is_none() {
            break;
        }
        let valley = valley.unwrap();

        let peak = calc_underwater_peak(&underwater, valley);

        let recovery = calc_underwater_recovery(&underwater, valley);

        let drawdown = -underwater[valley];
        max_drawdown = if max_drawdown > drawdown {
            max_drawdown
        } else {
            drawdown
        };
        top_n_drawdown_days_sum += valley.abs_diff(peak);

        if recovery.is_none() {
            underwater[peak..total_days].fill(0.0);
        } else {
            let recovery = recovery.unwrap();
            underwater[peak..recovery].fill(0.0);
        }
    }
    let length_adjusted_average_max_drawdown =
        top_n_drawdown_days_sum as f64 / top_n_drawdown as f64 / yearly_days;

    (max_drawdown, length_adjusted_average_max_drawdown)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_daily_performance() {
        let dr = vec![
            0.003, -0.0022, -0.0004, -0.0048, -0.0, 0.005, 0.0015, -0.0017, 0.0017, 0.0031,
            -0.0002, 0.0003, -0.0064, -0.0006, -0.0031, 0.0027, -0.0, -0.0013, -0.004, 0.0013,
            -0.0036, -0.0008, 0.0, 0.002, 0.0001, -0.0007, 0.0006, -0.0006, 0.0, 0.0005, -0.0017,
            -0.0001, 0.0008, 0.0005, 0.0, 0.0019, -0.003, -0.0015, 0.0016, 0.0009, -0.0002, 0.0009,
            0.0004, 0.0033, -0.0032, 0.0057, -0.0005, -0.0024, 0.0002, 0.0022, -0.0011, -0.0039,
            -0.0002, 0.0014, 0.001, -0.0012, 0.0008, -0.001, 0.0, 0.001, -0.0035, -0.0014, -0.0018,
            -0.0016, -0.0002, -0.0032, -0.0021, 0.0015, 0.0008, 0.0023, -0.0034, 0.0008, -0.0001,
            -0.0034, 0.0043, 0.0036, 0.005, -0.0005, 0.0025, 0.0001, -0.0005, 0.0038, -0.0018,
            -0.003, -0.0003, 0.0, -0.0013, 0.0007, 0.0015, -0.001, 0.0026, -0.0009, 0.0012, 0.005,
            -0.0045, 0.0, -0.0006, 0.0011, -0.0022, -0.0013, -0.003, 0.0, 0.0027, -0.0019, -0.0015,
            -0.0001, 0.0039, -0.0001, -0.0028, 0.0007, -0.004, -0.0024, 0.0007, 0.005, 0.0023,
            0.0001, -0.0, -0.0011, -0.0006, -0.0, -0.0003, 0.0012, -0.0, 0.0011, -0.0022, 0.0002,
            0.0007, 0.0018, 0.0001, 0.0029, -0.0004, 0.0062, -0.0017, -0.0012, -0.0, -0.0004,
            0.003, 0.0012, 0.0015, 0.0003, 0.0002, 0.0029, 0.0008, -0.0011, -0.0003, 0.0054,
            -0.0006, 0.0019, 0.0012, -0.0008, -0.001, -0.0034, 0.0002, -0.0017, 0.0017, 0.0003,
            -0.0024, -0.0022, -0.0, 0.0006, -0.0006, -0.0005, -0.0013, 0.003, -0.0, 0.0039, 0.0001,
            0.0011, -0.0008, 0.0011, 0.0001, 0.0001, 0.0028, 0.0038, 0.0072, -0.0021, -0.0001,
            -0.0003, -0.0005, 0.006, 0.0009, 0.0039, -0.0006, 0.0071, -0.0032, 0.0023, 0.0003,
            -0.0043, 0.0, 0.0025, -0.0019, 0.0, -0.0021, -0.0003, 0.0005, 0.0034, -0.0014, -0.0015,
            0.0006, -0.0027, 0.0003, 0.0003, 0.0011, 0.003, -0.0003, 0.0047, 0.0003, 0.0035,
            0.0039, 0.0011, 0.0089, 0.001, 0.0001, -0.0004, 0.0003, 0.0038, -0.0, -0.0018, 0.0004,
            -0.0002, 0.0011, -0.0025, 0.0015, -0.0001, -0.0012, -0.0014, 0.0044, 0.0007, 0.0009,
            0.0, 0.0018, 0.0003, -0.0001, 0.0002, 0.0006, -0.0001, -0.0045, 0.0005, -0.0027,
            0.0004, -0.0004, 0.0, 0.0049, -0.0017, 0.0054, -0.005, 0.0007, -0.0003, -0.0026,
            -0.0044, -0.0016, 0.0004, 0.0001, 0.0002, 0.003, 0.0026, 0.0027, -0.0029, -0.0005, 0.0,
            -0.0021, 0.0004, 0.0057, 0.0026, 0.0113, -0.0003, 0.0068, -0.0031, 0.0068, 0.0034,
            0.0045, 0.0, -0.0011, -0.004, 0.0003, -0.0044, -0.0017, -0.0, -0.0012, -0.0026,
            -0.0016, -0.0048, -0.0002, 0.0001, 0.0026, 0.0005, 0.0025, 0.0006, 0.0053, -0.0044,
            -0.0008, 0.0003, -0.0006, -0.0, -0.0005, -0.0002, -0.0005, 0.0004, 0.0003, 0.0002,
            0.0003, 0.0016, -0.0003, 0.0036, 0.0003, -0.0001, -0.0035, -0.0034, -0.0009, 0.0008,
            -0.0008, 0.0, -0.0002, 0.0011, -0.002, -0.0007, 0.003, 0.0004, 0.0022, 0.0002, 0.0019,
            -0.0013, -0.0021, -0.0002, -0.0007, -0.0004, -0.0001, -0.0001, 0.0049, -0.0, -0.0007,
            -0.0007, 0.0001, -0.0006, -0.0005, 0.0001, 0.0031, 0.0004, 0.0018, 0.0014, 0.0034,
            -0.0003, 0.0025, 0.0016, -0.0004, 0.0004, 0.0014, -0.0, -0.0, -0.0011, -0.0011,
            -0.0016, 0.0013, -0.0001, 0.002, 0.0061, 0.0024, -0.0004, -0.0038, -0.0, -0.0002,
            -0.0004, 0.0002, -0.0015, -0.0001, 0.0028, -0.0017, 0.0003, -0.0001, 0.0003, 0.005,
            -0.0005, -0.0005, -0.0016, -0.0001, 0.0047, -0.0006, 0.0005, 0.004, 0.0005, 0.0021,
            -0.002, 0.0009, 0.0002, 0.0026, -0.0018, 0.0002, -0.001, 0.0037, -0.0002, 0.0082,
            0.0066, 0.0019, -0.0004, -0.0031, -0.0006, -0.0003, 0.0065, -0.0063, -0.0026, 0.0023,
            0.0008, -0.002, -0.0018, 0.0012, 0.0006, -0.0012, 0.0002, -0.003, -0.0024, -0.0009,
            0.0015, 0.0019, 0.0001, -0.0028, -0.0013, 0.0014, 0.0024, -0.0001, -0.0017, -0.0062,
            -0.0008, -0.0059, -0.0003, 0.002, 0.002, 0.0001, 0.0004, -0.001, -0.0006, -0.0033,
            -0.0012, -0.001, -0.0027, -0.0019, -0.0002, -0.0013, 0.0014, 0.0011, -0.0002, -0.003,
            -0.0002, -0.0026, -0.0023, -0.0004, 0.0023, 0.0021, -0.0, 0.0019, 0.0043, -0.0001,
            0.0056, 0.0019, -0.0006, 0.0053, 0.0009, -0.0, 0.0057, 0.0059, 0.0003, 0.0096, -0.0089,
            0.0001, -0.0013, -0.0012, -0.0003, 0.0026, -0.0018, 0.0012, 0.0028, 0.0059, 0.0005,
            -0.0044, -0.0006, 0.0007, -0.0011, -0.0041, -0.0003, 0.0024, -0.0025, 0.0009, 0.0035,
            0.0002, 0.0001, 0.0025, -0.0008, 0.0001, -0.0015, -0.0042, -0.0009, 0.0, 0.0041,
            0.0012, -0.0034, -0.0019, 0.0004, -0.0019, -0.0017, 0.0013, 0.0006, 0.0047, -0.0031,
            -0.0003, 0.0044, -0.0066, 0.0014, 0.0072, -0.0045, 0.0013, 0.0053, -0.0008, -0.0,
            0.0014, -0.0013, -0.0022, 0.0035, -0.0002, -0.0004, 0.0008, -0.0035, -0.0002, -0.0034,
            0.0002, -0.0032, -0.0027, 0.0011, 0.0015, -0.0, 0.0002, -0.002, 0.0003, 0.0005, 0.0007,
            0.0055, -0.0005, 0.0023, 0.0035, 0.0011, 0.0005, -0.0024, -0.0002, -0.0027, 0.0042,
            -0.0043, -0.001, 0.008, -0.0, -0.0003, 0.0047, -0.0067, 0.001, -0.0033, -0.0046,
            -0.0013, 0.0039, -0.0023, -0.004, -0.0059, -0.0014, -0.0007, -0.0026, -0.0003, -0.0022,
            -0.0006, -0.0, -0.0002, 0.0026, 0.0047, 0.0017, 0.0029, 0.0, 0.0034, 0.0071, -0.0036,
            0.0042, -0.0001, 0.0002, 0.0026, 0.0051, -0.0004, 0.0033, -0.0016, 0.0021, -0.0002,
            -0.0001, -0.0, -0.0006, 0.0003, -0.0004, 0.0014, 0.0052, -0.0002, -0.0023, -0.0029,
            -0.0006, 0.0015, 0.0012, 0.0005, -0.0012, -0.0044, -0.001, -0.0002, 0.0003, -0.0039,
            -0.0037, -0.0003, 0.0012, 0.0017, 0.0016, -0.0018, 0.0, 0.0004, -0.003, 0.0025,
            -0.0002, -0.0006, 0.0004, -0.0014, -0.0005, -0.0007, 0.0012, -0.0012, 0.0004, -0.0014,
            0.0006, 0.0016, -0.0018, -0.0012, -0.0014, 0.0009, 0.0002, -0.0039, 0.0, 0.0019,
            0.0031, -0.0006, 0.0009, -0.0002, -0.0001, -0.0025, 0.0013, 0.0028, 0.003, -0.0017,
            0.0005, 0.0003, 0.0017, -0.0001, -0.0003, 0.0019, -0.0024, -0.0013, -0.0012, -0.0035,
            0.0004, 0.0034, -0.0016, -0.0025, -0.001, -0.0026, 0.0012, 0.0017, 0.0016, -0.0005,
            0.0033, -0.0015, -0.0005, -0.0018, 0.0018, -0.0033, -0.0011, 0.002, 0.0029, -0.0002,
            -0.0003, 0.0021, 0.0025, 0.004, 0.0029, 0.0015, 0.0014, 0.0029, 0.0046, 0.002, 0.004,
            0.0032, -0.0009, -0.0066, 0.0003, -0.0033, 0.0, 0.0078, 0.0026, 0.0016, -0.0034,
            0.0074, -0.0045, -0.0023, 0.0006, -0.0037, -0.005, 0.0003, -0.0008, 0.0022, -0.0009,
            -0.0, 0.0044, -0.002, 0.0005, -0.0011, -0.0007, 0.0025, -0.0022, -0.0027, 0.0004,
            -0.003, -0.0005, -0.0041, -0.0019, -0.0002, 0.0003, 0.0029, 0.0047, -0.0012, -0.0013,
            -0.0019, -0.0002, 0.0007, 0.0031, 0.0053, 0.0055, 0.0037, -0.0018, -0.0034, 0.002,
            -0.0002, -0.0006, 0.0017, -0.0005, 0.0016, -0.0032, 0.0006, 0.0079, -0.0029, 0.0002,
            0.0037, -0.0023, 0.0077, -0.0022, -0.0011, -0.0001, -0.0008, 0.0, -0.0055, -0.0022,
            -0.0004, -0.0001, -0.0025, -0.0039, -0.0002, -0.0035, 0.0009, 0.0019, 0.0024, 0.0062,
            -0.0009, 0.0034, -0.0048, -0.0003, -0.0033, 0.003, -0.0015, 0.001, 0.0028, 0.0032,
            0.0054, 0.0027, -0.0027, -0.0016, 0.009, -0.0058, -0.0026, 0.0014, -0.0006, 0.0005,
            0.0028, 0.0033, 0.0015, 0.0009, 0.0009, -0.0002, 0.0102, -0.0117, -0.0024, 0.0014,
            0.0033, -0.0002, 0.0044, -0.0026, 0.0062, 0.0029, -0.0018, 0.0004, 0.0007, -0.0028,
            -0.0006, 0.0023, 0.0008, 0.0007, -0.0043, -0.0031, 0.0005, 0.0018, -0.0032, -0.0007,
            0.0001, 0.0027, 0.0013, 0.0003, 0.0019, -0.0004, 0.0012, -0.0015, -0.0012, -0.0032,
            -0.0019, -0.0007, -0.0014, 0.0042, -0.0049, -0.0009, 0.0015, 0.0004, 0.0002, -0.0022,
            -0.0013, -0.0005, -0.0012, -0.0002, 0.0018, 0.0034, -0.0012, -0.0003, 0.0045, 0.0003,
            0.0008, 0.0012, -0.001, -0.0039, -0.0023, 0.0003, 0.0013, -0.0013, -0.0046, -0.0024,
            0.0005, -0.0001, 0.0026, 0.0007, 0.0018, -0.0008, -0.0014, 0.0003, 0.0008, -0.0018,
            0.0001, -0.0029, 0.0024, 0.0017, -0.0015, 0.0053, -0.0153, 0.0045, 0.0016, 0.0001,
            0.0026, 0.0008, 0.0007, -0.0039, -0.0021, 0.0001, -0.0001, -0.0, 0.0024, 0.0304,
            0.0084, -0.0086, -0.0081, -0.0016, 0.0001, -0.0012, 0.0016, 0.0012, -0.0009, 0.0019,
            -0.0008, 0.0006, 0.0036, 0.0017, 0.0019, -0.0028, -0.0016, 0.0014, 0.0113, 0.0039,
            -0.0146, 0.0032, 0.0002, 0.0018, 0.0185, 0.0112, -0.0109, 0.0093, 0.019, 0.0052, 0.015,
            0.0181, 0.0241, -0.0058, 0.0214, -0.005, 0.005, 0.0064, -0.0057, 0.0023, 0.0019,
            -0.0076, -0.0008, -0.0018, 0.0038, -0.0079, 0.0083, -0.0019, 0.0064, -0.008, 0.0011,
            -0.0063, 0.0056, 0.0068, 0.0037, 0.0128, 0.0071, -0.0173, 0.0127, -0.0008, -0.0027,
            0.0063, 0.0098, -0.0081, -0.0013, -0.0023, -0.0003, -0.0001, -0.0035, -0.0003, 0.0004,
            0.0108, 0.0054, 0.0084, -0.0076, 0.0052, -0.0014, -0.0077, -0.0003, -0.0054, -0.0012,
            -0.0054, 0.0004, 0.0019, 0.0018, 0.0013, 0.0041, 0.0027, -0.0038, 0.0026, 0.0013,
            -0.0034, -0.0029, 0.0048, -0.0, -0.0093, -0.0011, -0.0021, -0.0035, 0.0008, 0.0043,
            0.0024, 0.0008, -0.0042, -0.0006, 0.0044, -0.0021, 0.0047, 0.001, -0.0059, 0.0009, 0.0,
            -0.0014, -0.0036, 0.0028, -0.0011, -0.0013, 0.0002, 0.004, -0.0053, -0.0001, 0.001,
            0.0043, 0.0004, -0.0013, 0.0052, 0.0081, 0.0089, -0.0024, 0.0001, 0.0026, 0.0008,
            -0.0016, 0.001, 0.001, 0.0001, 0.011, 0.0061, 0.002, 0.0053, 0.0072, 0.0, -0.0082,
            -0.0036, 0.0027, -0.0037, 0.0021, -0.0012, -0.0023, -0.0022, -0.0036, 0.0046, 0.0041,
            0.0004, -0.0, 0.0021, -0.001, 0.0009, 0.0004, 0.0002, 0.0058, 0.0046, 0.0018, -0.0009,
            0.001, 0.0011, -0.003, 0.0124, -0.0061, 0.0025, -0.0051, 0.0002, -0.0018, -0.0021,
            0.0045, 0.0026, 0.0016, -0.0007, -0.001, 0.0024, 0.0059, 0.0006, -0.0023, -0.0003,
            -0.0061, -0.0033, -0.0069, 0.0128, -0.0, 0.0015, 0.0044, -0.0, -0.0065, 0.0027, -0.0,
            0.0004, 0.0033, -0.0052, -0.0001, 0.0047, 0.0015, 0.0037, 0.0022, 0.0057, 0.0125,
            0.0033, 0.0019, -0.0003, 0.0042, 0.0013, -0.0002, 0.0097, -0.0008, -0.003, -0.0063,
            0.0041, -0.0018, 0.0014, 0.0001, -0.0053, -0.0067, -0.0012, 0.0022, 0.0035, 0.0004,
            -0.0049, 0.0078, -0.0042, -0.0024, -0.0023, 0.0009, 0.0006, 0.0045, 0.0027, -0.0018,
            0.0138, -0.0, -0.0055, -0.0047, 0.0087, 0.003, -0.0026, 0.0004, -0.0088, -0.0052,
            0.0023, 0.0148, 0.0043, -0.0018, -0.0004, -0.0082, 0.0008, -0.0043, 0.0102, 0.0012,
            -0.0063, -0.0081, -0.0038, 0.0027, 0.0046, 0.0051, 0.0034, 0.0063, 0.0072, 0.0058,
            0.0042, 0.0011, 0.0024, -0.0043, -0.0089, 0.0007, -0.0083, -0.0008, -0.0011, -0.0046,
            -0.007, -0.0013, -0.0026, 0.0034, -0.0002, 0.0005, 0.0129, 0.0039, 0.0043, 0.0036,
            -0.0056, -0.0032, 0.0015, 0.0005, -0.0034, -0.0044, 0.0029, 0.0048, 0.0114, -0.0002,
            0.0163, -0.0047, 0.0059, -0.0124, 0.0119, -0.0013, 0.0005, -0.005, -0.0026, 0.0076,
            0.0115, 0.0022, -0.0114, 0.0008, 0.0007, -0.0088, 0.0012, -0.0011, -0.0016, -0.003,
            0.012, 0.0006, 0.0137, -0.0013, -0.0043, 0.0039, -0.0084, -0.0054, -0.0003, 0.0004,
            0.0016, -0.0026, -0.0019, -0.0011, -0.0031, 0.0011, -0.0047, -0.0014, -0.0046, 0.0002,
            -0.0045, -0.0047, 0.0022, 0.0029, 0.003, -0.0005, 0.0064, 0.0002, 0.0016, 0.0002,
            -0.0008, 0.0001, -0.0044, -0.0024, 0.003, -0.0028, 0.0007, 0.0157, 0.0053, 0.0012,
            -0.0108, 0.0062, 0.0168, -0.015, -0.0097, -0.0005, 0.0011, -0.001, 0.0054, -0.0017,
            0.006, 0.0, -0.0085, 0.0009, -0.0017, -0.0021, 0.0026, -0.0013, 0.0038, 0.0057, 0.006,
            -0.0031, 0.0014, 0.0012, 0.0015, -0.0106, 0.0065, -0.0023, -0.0035, -0.0031, 0.0027,
            0.008, -0.0069, -0.0006, -0.0077, -0.0066, 0.0061, 0.0057, -0.0046, 0.0003, -0.0108,
            0.0053, -0.002, -0.0018, 0.0045, -0.0, 0.0031, -0.0198, 0.0041, -0.0052, -0.0021,
            -0.0001, -0.0027, 0.0049, -0.0074, 0.0076, 0.0016, 0.0015, -0.0009, 0.0116, -0.003,
            0.0002, 0.0029, -0.0, 0.002, 0.0003, 0.0023, 0.004, -0.0121, -0.0002, 0.0022, -0.0054,
            0.0014, -0.0004, 0.0035, 0.0012, -0.0058, 0.0009, 0.0012, 0.0031, 0.0111, -0.0001,
            -0.0088, 0.0002, 0.0052, 0.0028, 0.0009, -0.0, 0.0026, -0.001, 0.0056, -0.0036,
            -0.0045, 0.0013, 0.0023, -0.0007, 0.0018, 0.0062, -0.0028, -0.0012, 0.0116, 0.0041,
            0.0183, 0.0081, -0.0134, 0.0017, 0.0005, 0.005, 0.006, -0.0019, 0.0089, 0.0123, 0.0069,
            0.003, 0.0018, -0.0065, 0.0048, 0.0039, 0.0174, 0.0047, 0.0001, 0.0182, 0.0074,
            -0.0315, -0.0073, 0.0057, 0.0002, 0.0096, -0.0166, -0.0112, 0.0051, 0.0164, -0.0,
            0.0169, 0.0039, 0.0299, -0.0271, 0.0015, -0.0003, -0.0006, 0.0006, -0.0144, -0.0118,
            -0.0074, 0.0002, 0.0013, 0.0085, -0.0066, -0.0035, 0.001, -0.0001, 0.0081, -0.0027,
            -0.003, 0.0088, -0.0124, 0.0014, -0.0043, 0.0038, 0.0068, -0.0095, 0.014, -0.0032,
            -0.0056, 0.0039, -0.0067, 0.0005, -0.0051, -0.0009, -0.0036, 0.0059, 0.0067, -0.005,
            -0.0018, -0.0009, -0.0076, -0.0021, 0.0043, -0.0023, -0.0117, 0.0007, 0.0012, -0.009,
            -0.0018, -0.0059, -0.003, 0.0003, -0.0025, 0.0008, 0.0006, 0.0015, 0.0049, -0.0029,
            -0.0003, 0.0003, 0.0021, -0.0006, -0.0039, 0.0028, 0.0069, -0.0066, 0.006, 0.0014,
            -0.0111, -0.0015, -0.0031, 0.0018, -0.0037, -0.0016, -0.0073, 0.0007, 0.005, 0.0094,
            -0.0021, 0.0059, -0.0172, -0.0056, 0.0068, -0.0117, 0.0025, 0.0004, -0.0094, 0.0018,
            0.0012, -0.0006, -0.0002, -0.0003, -0.0001, 0.0003, 0.0038, -0.0051, -0.0048, -0.0016,
            0.0017, 0.0103, 0.0079, 0.0263, 0.0043, -0.0135, 0.0203, -0.0287, -0.0034, 0.0048,
            0.0012, 0.0117, 0.0017, -0.0054, -0.0111, -0.0004, 0.0007, -0.0024, -0.0071, 0.0058,
            0.0015, 0.0021, -0.0006, -0.0005, 0.0081, -0.0009, -0.0059, 0.0064, -0.0046, 0.0069,
            0.0023, -0.0004, -0.0045, -0.0, 0.002, 0.0049, 0.005, 0.0021, 0.0058, -0.0083, -0.0033,
            -0.0013, 0.0039, 0.0024, 0.012, -0.0053, 0.002, 0.0013, 0.0033, 0.0006, 0.0087,
            -0.0011, 0.0022, 0.0032, -0.0144, 0.0092, 0.0, -0.0002, -0.0036, -0.0044, -0.0046,
            -0.008, -0.0024, 0.0003, -0.0065, -0.0004, 0.0003, 0.0041, 0.0066, 0.0017, 0.0048,
            -0.0016, -0.0031, 0.001, 0.0023, 0.0125, 0.0086, -0.0113, -0.0067, 0.0002, 0.0014,
            0.0084, -0.0024, 0.0137, 0.0173, 0.0017, 0.0029, -0.001, 0.0035, -0.0015, 0.001,
            -0.0002, -0.0094, 0.0063, -0.0006, -0.0062, 0.0144, 0.0008, -0.0116, 0.0088, 0.001,
            -0.0104, 0.0126, 0.0004, 0.0065, 0.0172, -0.0026, 0.0094, -0.0138, -0.0008, 0.0013,
            -0.0094, -0.0033, 0.0008, -0.0087, -0.0007, 0.0008, -0.0136, 0.0047, 0.011, 0.0078,
            -0.0023, -0.0123, -0.0015, -0.0033, 0.0054, -0.0065, 0.0003, -0.0089, -0.0049, -0.0048,
            -0.0065, 0.0014, -0.0, -0.0116, 0.0017, 0.0044, 0.0077, 0.0041, -0.0, -0.0095, 0.0024,
            0.0044, 0.0005, -0.004, 0.0003, -0.0033, -0.0007, 0.001, 0.008, -0.0091, -0.0011,
            0.0056, -0.003, -0.0039, 0.0037, 0.0173, -0.0055, -0.0038, -0.0075, -0.0029, -0.0004,
            0.0072, -0.0063, -0.0028, 0.01, -0.0111, 0.0004, 0.0079, 0.0006, -0.0055, 0.0012,
            0.0169, 0.0006, -0.0083, 0.0023, -0.0054, 0.0049, -0.0009, 0.0057, 0.0026, 0.0026,
            -0.0033, -0.0027, 0.0013, -0.0016, 0.0024, 0.0002, 0.0112, 0.0022, -0.0, 0.0023,
            -0.0062, -0.0007, -0.0004, 0.0019, 0.0004, 0.0033, 0.0045, 0.0019, 0.0048, 0.0048,
            -0.0001, -0.0112, 0.0099, -0.0031, 0.0055, 0.0011, 0.0005, 0.0036, -0.0048, 0.0024,
            0.0019, -0.0, 0.0028, 0.0003, -0.0081, -0.0074, -0.0011, 0.0017, 0.0015, -0.0079,
            0.0047, -0.0014, 0.0023, 0.0116, 0.0002, -0.0019, 0.0022, 0.0049, -0.0011, -0.0074,
            -0.002, 0.0062, -0.0043, -0.0033, 0.0014, 0.0028, 0.0011, -0.0111, -0.0022, -0.0047,
            -0.0022, -0.0013, -0.0021, -0.0023, 0.0017, -0.0042, -0.0006, -0.0043, 0.0002, 0.0006,
            0.0069, 0.0018, 0.0002, 0.0006, 0.0102, -0.0016, 0.0026, 0.0047, -0.0158, -0.0052,
            0.0067, 0.0034, 0.0033, 0.0024, 0.0018, -0.0047, 0.0022, 0.0013, 0.0035, 0.0002,
            0.0002, -0.0077, -0.0036, 0.001, -0.0065, -0.0001, 0.0015, 0.0011, 0.0077, -0.0008,
            -0.0033, 0.0006, -0.0046, -0.0032, -0.0046, -0.0026, -0.0065, 0.001, 0.0008, 0.0004,
            0.0042, -0.0046, 0.0026, -0.0015, 0.0062, 0.0019, 0.0069, 0.0014, -0.0015, -0.0006,
            0.0015, -0.004, -0.0017, 0.0013, -0.0037, -0.0002, 0.0016, 0.0026, -0.0029, 0.0011,
            0.0039, 0.0063, 0.0017, 0.0067, 0.0071, 0.0003, -0.0005, -0.0011, -0.0007, 0.0034,
            -0.0007, -0.0067, -0.0002, -0.0071, -0.0032, 0.0005, 0.0001, 0.0089, 0.0003, -0.0019,
            0.0049, -0.001, 0.0087, 0.0151, 0.0054, -0.0138, -0.0003, -0.003, 0.0019, 0.0023,
            0.0068, 0.0044, -0.0007, -0.0003, 0.0002, -0.002, 0.0083, 0.0044, 0.0001, 0.0089,
            0.0008, 0.013, 0.0015, -0.0111, -0.0002, 0.0014, -0.0044, -0.0031, -0.0021, -0.0052,
            0.0001, -0.006, -0.0022, 0.0009, -0.0013, 0.0006, -0.0002, 0.0058, -0.0083, -0.0012,
            -0.0002, 0.0012, -0.0004, -0.0049, -0.0024, 0.0014, -0.0007, -0.0052, -0.0019, 0.002,
            -0.0004, -0.0004, -0.004, -0.001, -0.0028, -0.0042, 0.0021, 0.0006, 0.0063, 0.0015,
            0.0092, 0.0118, -0.0093, -0.001, -0.0004, 0.0124, 0.0069, -0.0033, -0.0004, 0.0001,
            0.0008, 0.0118, 0.0062, -0.0024, -0.0004, 0.0003, -0.0019,
        ];
        let dr = daily_performance(&dr, None).unwrap();
        assert_eq!(dr.absolute_return, 0.6889);
        assert_eq!(dr.annual_returns, 0.0922);
        assert_eq!(dr.sharpe_ratio, 1.1931);
        assert_eq!(dr.max_drawdown, 0.1373);
        assert_eq!(dr.calmar_ratio, 0.6715);
        assert_eq!(dr.daily_win_rate, 0.5436);
        assert_eq!(dr.daily_profit_loss_ratio, 1.0568);
        assert_eq!(dr.daily_win_probability, 0.1181);
        assert_eq!(dr.annual_volatility, 0.0773);
        assert_eq!(dr.downside_volatility, 0.0551);
        assert_eq!(dr.non_zero_coverage, 0.9665);
        assert_eq!(dr.break_even_point, 0.9782);
        assert_eq!(dr.new_high_interval, 229.0);
        assert_eq!(dr.new_high_ratio, 0.0861);
        assert_eq!(dr.drawdown_risk, 1.7762);
    }
}
