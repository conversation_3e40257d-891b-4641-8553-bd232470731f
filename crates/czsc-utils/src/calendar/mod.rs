use chrono::NaiveDate;
use once_cell::sync::Lazy;
use polars::{frame::<PERSON><PERSON>rame, io::SerReader, prelude::<PERSON>p<PERSON><PERSON>eader};
use std::{collections::BTreeMap, io::Cursor};

static CALENDAR_DF: Lazy<DataFrame> = Lazy::new(|| {
    const CALENDAR_BYTES: &[u8] = include_bytes!("./china_calendar.feather");
    let cursor = Cursor::new(CALENDAR_BYTES);
    IpcReader::new(cursor).finish().unwrap()
});

static CALENDAR_MAP: Lazy<BTreeMap<NaiveDate, bool>> = Lazy::new(|| {
    // 确保数据完整性
    let df = CALENDAR_DF.drop_nulls::<String>(None).unwrap();

    // 处理 is_open 列
    let is_open = df
        .column("is_open")
        .unwrap()
        .i64()
        .unwrap()
        .into_no_null_iter()
        .map(|v| v == 1);

    // 处理 cal_date 列
    let date = df
        .column("cal_date")
        .unwrap()
        .datetime()
        .unwrap()
        .as_datetime_iter()
        .filter_map(|dt| dt)
        .map(|dt| dt.date());

    date.zip(is_open).collect::<BTreeMap<NaiveDate, bool>>()
});

#[allow(unused)]
/// 获取两个日期之间的所有交易日
pub fn get_trading_dates<'a>(
    start_date: NaiveDate,
    end_date: NaiveDate,
) -> impl Iterator<Item = NaiveDate> + 'a {
    CALENDAR_MAP
        .range(start_date..=end_date)
        .filter(|(_, v)| **v)
        .map(|(&date, _)| date)
}

/// 获取给定日期的上一个交易日
/// 如果输入日期是交易日，直接返回该日期；
/// 如果不是交易日，返回上一个交易日。
pub fn get_recent_trading_date(date: NaiveDate) -> Option<NaiveDate> {
    // 获取到所有日期小于等于 `date` 的交易日
    CALENDAR_MAP
        .range(..=date)
        // 倒序迭代，最近的日期优先
        .rev()
        // 只保留交易日
        .filter(|(_, is_open)| **is_open)
        // 提取日期
        .map(|(&trading_date, _)| trading_date)
        // 返回第一个匹配的日期
        .next()
}

#[allow(unused)]
/// 判断是否是交易日
pub fn is_trading_date(date: NaiveDate) -> bool {
    CALENDAR_MAP.get(&date).cloned().unwrap_or(false)
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{Datelike, Weekday};

    #[test]
    fn test_() {
        let date = NaiveDate::from_ymd_opt(2024, 12, 6).unwrap();
        assert_eq!(get_recent_trading_date(date), Some(date));

        let date2 = NaiveDate::from_ymd_opt(2024, 12, 8).unwrap();
        assert_eq!(get_recent_trading_date(date2), Some(date));
    }

    #[test]
    fn test_is_trading_date() {
        let date = NaiveDate::from_ymd_opt(2024, 12, 6).unwrap();
        assert!(is_trading_date(date));

        let date = NaiveDate::from_ymd_opt(2024, 12, 7).unwrap();
        assert!(!is_trading_date(date));
    }

    #[test]
    fn test_get_trading_dates() {
        let start_date = NaiveDate::from_ymd_opt(2024, 12, 6).unwrap();
        let end_date = NaiveDate::from_ymd_opt(2024, 12, 9).unwrap();
        // 周五
        assert_eq!(start_date.weekday(), Weekday::Fri);
        // 下周一
        assert_eq!(end_date.weekday(), Weekday::Mon);

        let result = get_trading_dates(start_date, end_date)
            .into_iter()
            .collect::<Vec<_>>();
        assert_eq!(result, vec![start_date, end_date]);
    }
}
