[package]
name = "czsc-utils"
version.workspace = true
edition.workspace = true

[dependencies]
error-macros = { path = "../error-macros" }
error-support = { path = "../error-support" }

chrono = { workspace = true, features = ["serde"] }
thiserror.workspace = true
anyhow.workspace = true
serde.workspace = true
polars = { workspace = true, features = [] }

czsc-core = { path = "../czsc-core", optional = true }

hashbrown = { workspace = true, optional = true }
once_cell = { version = "1.20.2", optional = true }
parking_lot = { version = "0.12.3", optional = true }

serde_json = { workspace = true }
bytes = { version = "1.8.0", optional = true }
reqwest = { version = "0.12.12", default-features = false, features = [
    "rustls-tls",
    "json",
], optional = true }
md5 = { version = "0.7.0", optional = true }
home = "0.5.11"
tracing.workspace = true

pyo3 = { workspace = true, optional = true }
pyo3-stub-gen = { workspace = true, optional = true }


[features]
backtest = ["czsc-core", "polars/default"]
calendar = [
    "polars/polars-io",
    "hashbrown",
    "once_cell",
    "parking_lot",
    "czsc-core",
]
data_client = ["bytes", "reqwest", "md5"]
axon_util = ["data_client", "polars/default"]
python = ["pyo3", "pyo3-stub-gen", "czsc-core/python"]

full = ["backtest", "calendar", "data_client", "axon_util"]
full_py = ["backtest", "calendar", "data_client", "axon_util", "python"]

[dev-dependencies]
tokio = { version = "1.41.0", features = ["full"] }
