name: CI

on:
  release:
    types: [created]

jobs:
  publish-to-pypi:
    runs-on: ${{ matrix.platform.runner }}
    strategy:
      matrix:
        platform:
          - runner: ubuntu-latest
            target: x86_64
          - runner: macos-latest
            target: x86_64
          - runner: macos-latest
            target: aarch64
          - runner: windows-latest
            target: x64

    steps:
      - uses: actions/checkout@v4
      - name: Publish wheels
        uses: PyO3/maturin-action@v1
        with:
          target: ${{ matrix.platform.target }}
          command: publish
          args: --no-sdist -m python/Cargo.toml
          manylinux: auto
        env:
          MATURIN_PYPI_TOKEN: ${{ secrets.PYPI_TOKEN }}
