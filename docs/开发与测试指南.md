# 开发与测试指南

本项目为 Rust + PyO3 实现的 Python 包，推荐如下开发与测试流程：

## 一、用 uv + maturin 打包和测试

1. 安装 uv（如未安装）：
   ```shell
   pip install uv
   ```
2. 创建虚拟环境：
   ```shell
   uv venv .venv
   ```
3. 激活虚拟环境（Windows）：
   ```shell
   .venv\Scripts\activate
   ```
4. 安装 maturin：
   ```shell
   uv pip install maturin
   ```
5. 进入 python 目录，打包 wheel：
   ```shell
   maturin build --release
   ```
   生成的 wheel 包在 `target/wheels/` 目录下。
6. 本地安装 wheel 包测试：
   ```shell
   uv pip install target/wheels/rs_czsc-*.whl
   ```

## 二、单元测试的便捷方法

### 1. 直接用 pytest 运行 Python 测试
- 安装开发依赖：
  ```shell
  uv pip install .[dev]
  ```
- 运行测试：
  ```shell
  pytest
  ```

### 2. 用 maturin develop 快速开发安装
- 构建并开发安装 Rust 扩展：
  ```shell
  maturin develop
  ```
- 然后直接运行 pytest：
  ```shell
  pytest
  ```

### 3. 用 maturin pytest 一步到位
- maturin 支持直接构建并运行 pytest：
  ```shell
  maturin pytest
  ```
- 如果需要测试指定文件下的某个函数，可以使用以下命令：
  ```shell
  maturin pytest tests/test_czsc.py::test_specific_function
  ```
  其中 `tests/test_czsc.py` 是测试文件路径，`test_specific_function` 是要测试的函数名。

### 4. Rust 层单元测试
- 仅测试 Rust 逻辑：
  ```shell
  cargo test
  ```

---

**推荐开发调试时用 `maturin develop` 或 `maturin pytest`，无需手动打包 wheel，测试体验最佳。**


## 三、生成 Python 类型提示存根文件

项目支持通过 Rust 代码自动生成 Python 类型提示存根（stub）文件，便于编辑器和 IDE 提供更完善的类型提示和代码补全。

1. 进入 `python` 目录：

   ```shell
   cd ./python/
   ```
2. 执行生成存根文件命令：

   ```shell
   cargo run --bin stub_gen
   ```
3. 生成的存根文件会自动输出到指定目录（一般为 `python\rs_czsc\_rs_czsc.pyi`）。

> **说明**：该存根文件用于增强编辑器的类型提示体验，不影响运行时逻辑。
